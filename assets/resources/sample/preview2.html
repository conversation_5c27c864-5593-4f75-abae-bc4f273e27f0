<!DOCTYPE html>
<html lang="ja">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=2.0,user-scalable=yes"/>
    <title>MobiAgent Sample</title>
    <style>
      body{
        height: 100vh;
        overflow: scroll;
        -webkit-overflow-scrolling: touch;
      }
      pre{
        overflow: scroll;
      }
    </style>
  </head>
  <body>
    <div>
      <p>このページは<strong><code>mobi-agent-chatwindow-frame-loader.min.js</code></strong>を使用したサンプルです。</p>
      <p>ご利用ドメインの本番環境に接続します</p>
      <p>ログイン中は利用できません。一度ログアウトするか別のブラウザからアクセスしてください。</p>
    </div>
    <script type="text/javascript" >
      document.addEventListener("DOMContentLoaded", function() {
        var query = {};
        location.search.replace("?","").split("&").forEach(function(keyVal){var tokens = keyVal.split("="); query[tokens[0]] = decodeURIComponent(tokens[1])});
        if (!query.domainId) {
          alert("Error: domainId is not defined");
          return;
        }
        var hostUrl = location.protocol + '//' + location.host + location.pathname.replace('/assets/resources/sample/preview2.html', '');
        var scriptUrl = hostUrl + '/web/mobi-agent-chatwindow-frame-loader.min.js?domainId=' + query.domainId;
        var sTag = document.createElement( "script" );
        sTag.type = "text/javascript";
        sTag.onload = function() {
          Object.keys(MobiAgentClient.Events).forEach(function(key){
            MobiAgentClient.on(key, function(data){
              console.log(key, data);
            });
          });
          MobiAgentClient.init(
            hostUrl,
            query.domainId,
            {
              location: '管理者テストページ',
            },
            function(f){
              console.log(f);
            }
          );
        };
        sTag.src = scriptUrl;
        document.body.appendChild(sTag);
      });
    </script>
  </body>
</html>
