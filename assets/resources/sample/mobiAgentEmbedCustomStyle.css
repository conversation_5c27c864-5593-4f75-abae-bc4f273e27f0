@charset "utf-8";
/*
デフォルトスタイルはすべてインラインスタイルで定義されているため
custom.cssを使用する場合は!important指定を行ってください。
描画されるHTML要素には以下のクラス定義が行われていますが、必ずしもすべての要素にclass名が定義してあるわけではないので、
実際にブラウザで確認を行ってください。
*/

div#konnect { /* 小窓全体のボディー */
}
  div.mbaGuestFrame { /* 小窓全体の仕様設定 */
  }

    /*Trigger*/
    div.mbaGuestFrame_trigger { /* ヘッダーエリア */
    }
      /*旧タイプ*/
      p.mbaGuestFrame_trigger_line {  /* 旧タイプのヘッダー */
      }
        span.mbaGuestFrame_trigger_line_left { /* 旧タイプヘッダーの「お問合せ」タイトル */
        }
        span.mbaGuestFrame_trigger_line_right { /* 旧タイプヘッダーの「隠す」「再表示」ボタン */
        }

    /*Content*/
    div.mbaGuestFrame_content {/* チャット本文エリア */
    }

      /*Error 発生時*/
      div.mbaGuestFrame_content_fatalError {  /*Errorメッセージエリアの背景*/
      }
        span.mbaGuestFrame_content_fatalError_message {  /*Errorメッセージ*/
        }
          span.mbaGuestFrame_content_fatalError_message_bull {/*Errorメッセージ左側の装飾黒丸マーカー*/
          }
          span.mbaGuestFrame_content_fatalError_message_text {/*Errorメッセージの本文テキスト*/
          }

      /*Timeline */
      div.mbaGuestFrame_content_timeline {/*チャットタイムライン*/
      }
        ul.mbaGuestFrame_content_timeline_list {/*チャットリストエリア*/
        }
          li.mbaGuestFrame_content_timeline_list_message { /*メッセージ*/
          }
          li.mbaGuestFrame_content_timeline_list_message.you { /*自分の発言の場合*/
          }
          li.mbaGuestFrame_content_timeline_list_message.operator {/*オペレーターの発言の場合*/
          }
            div.mbaGuestFrame_content_timeline_list_message_header {/*メッセージヘッダー*/
            }
              span.mbaGuestFrame_content_timeline_list_message_speaker { /*発信者側の名前*/
              }
              span.mbaGuestFrame_content_timeline_list_message_time {/*発信の時間*/
              }
            div.mbaGuestFrame_content_timeline_list_message_body {/*メッセージボディー*/
            }
              div.mbaGuestFrame_content_timeline_list_message_body_content {/*メッセージエリア背景*/
              }
                p.mbaGuestFrame_content_timeline_list_message_body_content_text { /*メッセージ送信がテキストの場合 */
                }
                div.mbaGuestFrame_content_timeline_list_message_time_body_content_image {/* メッセージ送信が画像の場合 */
                }
                div.mbaGuestFrame_content_timeline_list_message_time_body_content_location {/*メッセージ送信が地図の場合 */
                }
                div.mbaGuestFrame_content_timeline_list_message_time_body_content_urlpreview { /*url先読み*/
                }
                div.mbaGuestFrame_content_timeline_list_message_time_body_content_template { /*テンプレートの場合*/
                }
                  div.mbaGuestFrame_content_timeline_list_message_time_body_content_template_slider { /*テンプレートのカルーセル*/
                  }
                    div.mbaGuestFrame_content_timeline_list_message_time_body_content_template_item {/*テンプレートのカルーセルリスト*/
                    }
                      div.mbaGuestFrame_content_timeline_list_message_time_body_content_template_item_cell {/*テンプレートのカルーセルリスト詳細*/
                      }
                      p.mbaGuestFrame_content_timeline_list_message_time_body_content_template_item_cell_title {/*テンプレートのカルーセルのタイトル*/
                      }
                      p.mbaGuestFrame_content_timeline_list_message_time_body_content_template_item_cell_desc {/*テンプレートのカルーセル本文*/
                      }
                      div.mbaGuestFrame_content_timeline_list_message_time_body_content_template_item_cell_buttons {/*テンプレートのカルーセル選択肢リンク*/
                      }
      /*Input*/
      div.mbaGuestFrame_content_input_wrapper { /*入力欄エリア*/
      }
        div.mbaGuestFrame_content_input_wrapper_textarea {/* 入力欄エリアの各詳細  /  入力欄エリア旧タイプの入力欄エリア*/
        }
        textarea.mbaGuestFrame_content_input_wrapper_textarea_textarea {/* 入力フォーム */
        }
        button.mbaGuestFrame_content_input_wrapper_textarea_sendButton {/* 入力フォーム送信ボタン */
        }

        /*Input -- 旧タイプの場合*/
        div.mbaGuestFrame_content_input_wrapper_textarea > textarea { /*旧タイプ入力欄エリアの入力フォーム*/
        }
        div.mbaGuestFrame_content_input_wrapper_sendButton {/*旧タイプの入力フォーム送信ボタンエリア背景*/
        }
          div.mbaGuestFrame_content_input_wrapper_sendButton > button {/*旧タイプの入力フォームの送信ボタン*/
          }

        /* WEB小窓設定 > オプション設定 > 名前入力欄表示 > ONの時表示 */
        div.mbaGuestFrame_content_input_wrapper_initial_nameInput { /*名前入力欄ボディー*/
        }
          div.mbaGuestFrame_content_input_wrapper_initial_nameInput > div { /*名前入力エリア*/
          }
            span.mbaGuestFrame_content_input_wrapper_initial_nameInput_message{/*問合せする前の案内文*/
            }
            input.mbaGuestFrame_content_input_wrapper_initial_nameInput_input{ /*問合せ者の名前を入力する*/
            }
            button.mbaGuestFrame_content_input_wrapper_initial_nameInput_button{/*問合せ者開始ボタン*/
            }
        
        /* ゲスト入力欄左側の歯車マークをクリックすると出る */
        div.mbaGuestFrame_content_input_wrapper_option { /* ゲスト側の入力オプション */
        }
        /* 名前入力欄表示オプションONの時 */
          div.mbaGuestFrame_content_input_wrapper_option_nameInput {/* ゲスト名前変更の場合 */
          }
            input.mbaGuestFrame_content_input_wrapper_option_nameInput_input{/* ゲスト名前変更入力フォーム */
            }
            button.mbaGuestFrame_content_input_wrapper_option_nameInput_button{/* ゲスト名前変更ボタン */
            }

        div.mbaGuestFrame_content_input_wrapper_footer {/*旧タイプのフッター*/
        }
      
        /* オペレーション設定 > 対応モード設定 > ゲストによる満足度評価 > ONの時表示 */
      div.mbaGuestFrame_content_input_wrapper_guestReview { /* 満足度評価エリアのボディー */
      }
        div.mbaGuestFrame_content_input_wrapper_guestReview_header{ /* 満足度評価エリアのヘッダー */
        }
        div.mbaGuestFrame_content_input_wrapper_guestReview_content{ /* 満足度評価入力エリア */
        }
        div.mbaGuestFrame_content_input_wrapper_guestReview_rating{ /* 満足度評価星マーク */
        }
        div.mbaGuestFrame_content_input_wrapper_guestReview_footer{ /* 満足度評価エリアのフッター */
        }
          div.mbaGuestFrame_content_input_wrapper_guestReview_buttons{ /* 満足度評価送信ボタン */
          }