/* eslint-disable no-param-reassign */
/* eslint-disable object-shorthand */
(function (KonnectPlus) {

  // unitTest用(karma.confでロードしている)
  // https://mobilus-corp.atlassian.net/browse/MBAJ-504
  if (!KonnectPlus) {
    window.KonnectPlus = {
      option: function (key, val) {
        window.KonnectPlus[key] = val;
      }
    }
    window.KonnectPlusServerJSON = {
      service: {},
    };
    KonnectPlus = window.KonnectPlus;
  }

  // follow lib/constants/i18n.js
  // /constants/ErrorCodes.jsにerrorCodesの内容は記載。
  KonnectPlus.option('i18n.errorCodes', {
    '-1' : 'サーバ接続に失敗しました',
    '0' : 'APIの呼び出しに成功',

    '1' : 'ログインエラー: 認証に失敗しました。',
    '2' : 'ログインエラー: アカウントが管理者によって停止されています',
    '3' : 'ログインエラー: 契約が終了しています',
    '4' : 'ログインエラー: サーバーはメンテナンス中です',
    '5' : 'ログインエラー: アカウントがロックされています',
    '6' : 'ログインエラー: \n多要素認証に必要な情報が設定されていません。\n管理者にお問い合わせください。',
    '7' : 'ログインエラー: \n多要素認証の実行に失敗しました。\nサービス提供元にお問い合わせください。',

    '100' : 'APIエラー: ログインしてください',
    '101' : 'APIエラー: ユーザーが不正です',
    '102' : 'APIエラー: ルームが見つかりません',
    '103' : 'APIエラー: ルームに参加していません',
    '104' : 'APIエラー: パブリックルームにアクセスできません',
    '105' : 'APIエラー: プライベートルームにアクセスできません',
    '106' : 'APIエラー: ドメインが不正です',
    '107' : 'APIエラー: データはすでに存在します',
    '108' : 'APIエラー: 削除されたルームです',
    '109' : 'APIエラー: リクエスト上限に達しました',
    '110' : 'APIエラー: 1-1ルームにアクセスできません',
    '111' : 'APIエラー: 1-Nルームにアクセスできません',
    '112' : 'APIエラー: データが存在しません',
    '150' : 'APIエラー: 不正なリクエストです',
    '151' : 'APIエラー: 不正なリクエストです',
    '152' : 'ユーザーの上限数を超えたためユーザーを追加できません',
    '160' : 'APIエラー: 不正なリクエストです',
    '200' : 'APIエラー: サーバエラーです',
    '201' : 'APIエラー: SDKのバージョンが不正です',
    '202' : 'APIエラー: リクエスト数が上限に達しました',
    '400' : 'APIエラー: リクエストが許可されていません',
    '404' : 'APIエラー: 見つかりません',
    '405' : 'APIエラー: リクエストが許可されていません',
    '501' : 'APIエラー: 禁止語が含まれています',

    '1001' : 'エラー: パスワードが弱いです',
    '1002' : 'エラー: パスワードが正しくありません',
    '1003' : 'エラー: 脆弱なパスワードが指定されたため設定に失敗しました',
    '1004' : 'エラー: アカウントがロックされています',
    '1005' : 'エラー: 一時パスワードが無効になっています。再度登録してください。',
    '1006' : 'エラー: ユーザーはファイルを所有しています',
    '1007' : 'エラー: このパスワードは最近使用されています。別のパスワードを使用してください',

    '2001':　'エラー: アンケートが存在しません。',
    '2002':　'エラー: オリジナルアンケートがせんたくされていません。',
    '2003':　'エラー: アンケートの設定が存在しません。',
    '2004':　'エラー: オリジナルアンケートの設定が存在しません。',
    '2005':　'アンケート登録上限数(10)を超えているため追加できません。',
    '2006':　'優先アンケートは削除できません。',
    '2007':　'既にルームが終了しています。',
    '2008':　'エラー: このルームではアンケートをすでに回答済みです。',
    '2009':　'エラー: ルーム情報の更新に失敗しました。',
    '2010':　'エラー: 回答可能なアンケートセッションが存在しません。',
    '2011':　'エラー: ゲストユーザーはアンケートセッションを所有していません。',
    '2012':　'エラー: ゲストユーザーがルームに存在しません。',
    '2013':　'ルームの操作権限がありません。',
    '2014':　'アンケート項目登録上限数(10)を超えているため追加できません。',
    '2015':　'選択肢登録上限数(20)を超えているため追加できません。',
  });

  KonnectPlus.option('i18n.fileErrorCodes', {
    '101': 'ウィルスが検知されたため、ゲストから送られたデータを受け取れませんでした',
    '102': 'タイムアウトにより、ゲストから送られたデータを受け取れませんでした',
    '103': 'ファイル容量が大きいため、ゲストから送られたデータを受け取れませんでした',
    '104': '複数のファイルがゲストによって添付されました。最初の1つ以外は無視されています',
    '1': 'エラーが発生したため、ゲストから送られたデータを受け取れませんでした',
    '2': 'エラーが発生したため、ゲストから送られたデータを受け取れませんでした',
    '3': 'エラーが発生したため、ゲストから送られたデータを受け取れませんでした',
  });

  KonnectPlus.option('i18n', {
    'LANG': 'ja',

    'admin_callcenter_auto_replies_label_comment': 'コメント',
    'admin_callcenter_auto_replies_label_format': 'ファイル形式',
    'admin_callcenter_auto_replies_label_register_info': '登録シナリオ情報',
    'admin_callcenter_auto_replies_label_register_info_desc': 'V18(2018/02)リリース後にアップロードされたもの',
    'admin_callcenter_auto_replies_message': '自動応答メッセージ',
    'admin_callcenter_auto_replies_message_max_text': 'テキスト1,000文字まで',
    'admin_callcenter_auto_replies_page_desc_1': 'ここで指定した文言の自動応答を有効にするには、',
    'admin_callcenter_auto_replies_page_desc_2': 'において、[自動応答モジュール]項目として、`Simple モジュール`を指定してください。',
    'admin_callcenter_auto_replies_page_subHeader': 'ファイルをアップロードして登録',
    'admin_callcenter_auto_replies_page_title': '自動応答文言登録',
    'admin_callcenter_auto_replies_select_file_label': 'ファイル選択',
    'admin_callcenter_auto_replies_resume_title': '自動応答開始位置登録',
    'admin_callcenter_auto_replies_resume_point_label': '自動応答開始位置を指定してください',
    'admin_callcenter_category_desc_update_currentTab': '現在のタブの内容のみ更新します。',
    'admin_callcenter_category_desc_update_useTreeCategory': 'ツリー構造オプションを変更します。',
    'admin_callcenter_category_dialog_title': '分類区分更新',
    'admin_callcenter_category_error_label': 'カテゴリー名は空欄保存できません',
    'admin_callcenter_category_error_payload_duplicate': '同じコードが重複してます。',
    'admin_callcenter_category_error_payload': 'コードは半角英数字小文字32文字以内で指定してください',
    'admin_callcenter_category_error_text': 'ラベルは空欄保存できません',
    'admin_callcenter_category_hint_initialValue': 'ルーム作成時の初期値を選択してください',
    'admin_callcenter_category_hint_label': 'カテゴリー名を指定してください',
    'admin_callcenter_category_hint_payload': 'コードを指定してください',
    'admin_callcenter_category_hint_required': 'オペレーターがルーム完了時に、この分類区分の必須項目にします',
    'admin_callcenter_category_hint_text': 'オペレーター向けのテキストを指定してください',
    'admin_callcenter_category_hint_type': '択一選択か複数選択を指定してください',
    'admin_callcenter_category_hint_timeoutValue': 'タイムアウト時に設定する値を選択してください。',
    'admin_callcenter_category_hint_useTreeCategory_1': 'ツリー構造を有効にする場合、下位の区分は上位の区分をプレフィックスとして定義する必要があります。',
    'admin_callcenter_category_hint_useTreeCategory_2': '例: 分類区分1:01, 分類区分2:0101, 分類区分3:010101',
    'admin_callcenter_category_label_initialValue': '初期値',
    'admin_callcenter_category_label_label': 'カテゴリー名',
    'admin_callcenter_category_label_options': '選択肢',
    'admin_callcenter_category_label_payload' : 'コード',
    'admin_callcenter_category_label_selectMulti': '複数選択',
    'admin_callcenter_category_label_selectOne': '択一選択',
    'admin_callcenter_category_label_setDefaultAsEmpty': '初期値を指定しない',
    'admin_callcenter_category_label_setTimeoutAsEmpty': '更新しない',
    'admin_callcenter_category_label_setting': '設定',
    'admin_callcenter_category_label_text' : 'ラベル',
    'admin_callcenter_category_label_timeoutValue': 'タイムアウト発生時の値',
    'admin_callcenter_category_label_useTreeCategory': 'ツリー構造を有効にする',
    'admin_callcenter_category_page_title': '分類区分',
    'admin_callcenter_category_warning_initial_data_changed': '定義を変更した場合、既にルームに設定されているデータとの整合性が保たれなくなる可能性があります',
    'admin_callcenter_category_warning_initialValue': '選択肢に存在しない値が指定されています。初期値を変更するか選択肢を追加してください',
    'admin_callcenter_category_warning_requiredWithNoOptionError': '必須項目に指定されていますが、選択肢が存在しません',
    'admin_callcenter_category_warning_timeoutValue': '選択肢に存在しない値が指定されています。タイムアウト発生時の値を変更するか選択肢を追加してください',
    'admin_callcenter_chatwindow_button_label_copy': 'コピーする',
    'admin_callcenter_chatwindow_button_label_copy_success': 'コピーしました',
    'admin_callcenter_chatwindow_defaultValue_chat_end_confirmation': 'チャットを終了しますか？',
    'admin_callcenter_chatwindow_defaultValue_chat_support': 'チャットサポート',
    'admin_callcenter_chatwindow_defaultValue_end_chat': 'チャットを終了しました',
    'admin_callcenter_chatwindow_defaultValue_error_after_hours': '申し訳ございません。ただいま営業時間外です。',
    'admin_callcenter_chatwindow_defaultValue_error_chat_unavailable': '申し訳ございません。ただいま一時的な問題により、ご利用いただけません。',
    'admin_callcenter_chatwindow_defaultValue_error_file_size_exceedance': 'ファイルサイズの上限(25MB)を超えているため、アップロードできません。',
    'admin_callcenter_chatwindow_defaultValue_error_maintenance': 'メンテナンス中のためご利用いただけません。',
    'admin_callcenter_chatwindow_defaultValue_error_offline': '一時的にネットワークへの接続が切れました。オフライン中にメッセージを受信した可能性があります。',
    'admin_callcenter_chatwindow_defaultValue_error_other': 'エラーが発生しました。',
    'admin_callcenter_chatwindow_defaultValue_error_operator_response_wait': 'オペレーターを呼び出しています。恐れ入りますが、もうしばらくお待ちください。',
    'admin_callcenter_chatwindow_defaultValue_error_operator_congestion': '現在問い合わせが集中しています。しばらくしてからご利用ください。',
    'admin_callcenter_chatwindow_defaultValue_error_prohibited_word': 'ご利用できない文字列が含まれているため、メッセージを送信できません。',
    'admin_callcenter_chatwindow_defaultValue_exit': '終了する',
    'admin_callcenter_chatwindow_defaultValue_header1_text': 'チャットでのお問い合わせ',
    'admin_callcenter_chatwindow_defaultValue_header3_text': 'オペレーター対応は平日9:00~17:00、時間外は自動応答します。',
    'admin_callcenter_chatwindow_defaultValue_modify' : '修正する',
    'admin_callcenter_chatwindow_defaultValue_name': 'お名前',
    'admin_callcenter_chatwindow_defaultValue_name_change': '名前を変更する',
    'admin_callcenter_chatwindow_defaultValue_name_entry_description_field': 'お問い合わせを始める前に、お名前を入力してください',
    'admin_callcenter_chatwindow_defaultValue_operator_inputting': '入力中',
    'admin_callcenter_chatwindow_defaultValue_pop_open': 'ポップアップで開く',
    'admin_callcenter_chatwindow_defaultValue_privacy_masking_policy_mask': '入力されたメッセージには個人情報が含まれているため送信できません。\nこのまま送信する場合、個人情報は削除されます。\n送信しますか？',
    'admin_callcenter_chatwindow_defaultValue_privacy_masking_policy_block': '入力されたメッセージには個人情報が含まれているため送信できません。\n入力内容を修正してください。',
    'admin_callcenter_chatwindow_defaultValue_restart_chat_service': '再読み込みを行う',
    'admin_callcenter_chatwindow_defaultValue_send': '送信する',
    'admin_callcenter_chatwindow_hint_copy_script': 'コピーしたスクリプトをbodyタグの最後に追加してください。',
    'admin_callcenter_chatwindow_hint_display_setting': '表示しないにした場合、Web小窓は表示されません。',
    'admin_callcenter_chatwindow_hint_launch_icon': 'この設定は、MobiAgentClient.initialize()を利用している場合にのみ有効です。',
    'admin_callcenter_chatwindow_hint_operation_check': '下記リンクより、本ページ上で設定を保存したWeb小窓の動作確認を行うことができます。\nログアウトするか、別のブラウザもしくはシークレットウィンドウ上で下記リンクをご確認ください。',
    'admin_callcenter_chatwindow_hint_version': 'バージョンリリース後1年間の動作サポートとなります。サポート対象外のWeb小窓バージョンを選択していた場合、最新版リリース時に自動的にバージョンが「最新バージョン(x.xx.x)」に変更されます。',
    'admin_callcenter_chatwindow_label_autoReply': '自動応答',
    'admin_callcenter_chatwindow_label_background': '背景',
    'admin_callcenter_chatwindow_label_back_chat': 'チャットに戻る',
    'admin_callcenter_chatwindow_label_cache_retention_message_suggestions': 'メッセージサジェストのキャッシュ保持',
    'admin_callcenter_chatwindow_label_bubble_color_setting': '吹き出しカラー設定',
    'admin_callcenter_chatwindow_label_cancel_and_modify_button': 'キャンセル・修正ボタン',
    'admin_callcenter_chatwindow_label_cancel_button': 'キャンセルボタン',
    'admin_callcenter_chatwindow_label_chat_end_confirmation': 'チャット終了確認',
    'admin_callcenter_chatwindow_label_chat_timeline_color_setting': 'チャットタイムラインのカラー設定',
    'admin_callcenter_chatwindow_label_check_operator_status': 'オペレーターのステータス確認',
    'admin_callcenter_chatwindow_label_close_button': '閉じるボタン',
    'admin_callcenter_chatwindow_label_close_chat': 'チャットを終了する',
    'admin_callcenter_chatwindow_label_close_button_area_color_setting': '閉じるボタンエリアのカラー設定',
    'admin_callcenter_chatwindow_label_close_button_color_setting': '閉じるボタンのカラー設定',
    'admin_callcenter_chatwindow_label_close_button_size': '閉じるボタンのサイズ',
    'admin_callcenter_chatwindow_label_close_button_text': '閉じるボタンのテキスト',
    'admin_callcenter_chatwindow_label_color_setting': 'カラー設定',
    'admin_callcenter_chatwindow_label_copy_script': 'スクリプトコピー',
    'admin_callcenter_chatwindow_label_css_sample_link': 'CSSサンプルを見る',
    'admin_callcenter_chatwindow_label_dateTime_and_read_text': '日時・既読テキスト',
    'admin_callcenter_chatwindow_label_desktop_notification_audio_file_url': 'デスクトップ通知音再生ファイルURL',
    'admin_callcenter_chatwindow_label_desktop_notification_title': 'デスクトップ通知タイトル',
    'admin_callcenter_chatwindow_label_display_message_suggestions': 'メッセージサジェストの表示',
    'admin_callcenter_chatwindow_label_display_setting': '小窓の表示設定',
    'admin_callcenter_chatwindow_label_display_type': '小窓表示タイプ',
    'admin_callcenter_chatwindow_label_download_send_exit_button': 'ダウンロード・送信・終了ボタン',
    'admin_callcenter_chatwindow_label_download_button': 'ダウンロードボタン',
    'admin_callcenter_chatwindow_label_end_chat': 'チャット終了時',
    'admin_callcenter_chatwindow_label_error_after_hours': '営業時間外エラー',
    'admin_callcenter_chatwindow_label_error_chat_unavailable': 'チャット利用不可エラー',
    'admin_callcenter_chatwindow_label_error_file_size_exceedance': 'ファイルサイズ超過エラー',
    'admin_callcenter_chatwindow_label_error_maintenance': 'メンテナンスエラー',
    'admin_callcenter_chatwindow_label_error_offline': 'オフラインエラー',
    'admin_callcenter_chatwindow_label_error_operator_congestion': 'オペレーター混雑エラー',
    'admin_callcenter_chatwindow_label_error_operator_response_wait': 'オペレーター対応待ちエラー',
    'admin_callcenter_chatwindow_label_error_other': 'その他エラー',
    'admin_callcenter_chatwindow_label_error_prohibited_word': '禁止ワードエラー',
    'admin_callcenter_chatwindow_label_file_upload': 'ファイルアップロード',
    'admin_callcenter_chatwindow_label_first_display_message': '初期表示メッセージ',
    'admin_callcenter_chatwindow_label_footer_background': 'フッターの背景',
    'admin_callcenter_chatwindow_label_footer_color_setting': 'フッターのカラー設定',
    'admin_callcenter_chatwindow_label_footer_icon': 'フッターアイコン',
    'admin_callcenter_chatwindow_label_guest': 'ゲスト',
    'admin_callcenter_chatwindow_label_guest_user_name_input_field': 'ゲストユーザー名入力欄',
    'admin_callcenter_chatwindow_label_header_icon_image': 'ヘッダーアイコン画像',
    'admin_callcenter_chatwindow_label_header_1': 'ヘッダー1',
    'admin_callcenter_chatwindow_label_header_2': 'ヘッダー2',
    'admin_callcenter_chatwindow_label_header_3': 'ヘッダー3',
    'admin_callcenter_chatwindow_label_height': '高さ',
    'admin_callcenter_chatwindow_label_maximum_number_message_suggestions_display': 'メッセージサジェストの表示上限数',
    'admin_callcenter_chatwindow_label_message_modal_color_setting': 'メッセージモーダルのカラー設定',
    'admin_callcenter_chatwindow_label_message_send_button': '送信ボタン',
    'admin_callcenter_chatwindow_label_modal': 'モーダル',
    'admin_callcenter_chatwindow_label_operator': 'オペレーター',
    'admin_callcenter_chatwindow_label_operator_inputting': 'オペレーター入力中',
    'admin_callcenter_chatwindow_label_pop_open_small_web_window': 'Web小窓をポップアップで開く',
    'admin_callcenter_chatwindow_label_icon': 'アイコン',
    'admin_callcenter_chatwindow_label_icon_button_color_setting': 'アイコンボタンのカラー設定',
    'admin_callcenter_chatwindow_label_icon_button_size': 'アイコンボタンのサイズ',
    'admin_callcenter_chatwindow_label_icon_distance_from_display_position': '表示位置からの距離',
    'admin_callcenter_chatwindow_label_icon_display_position': 'アイコン表示位置',
    'admin_callcenter_chatwindow_label_icon_image': 'アイコン画像',
    'admin_callcenter_chatwindow_label_icon_image_width': 'アイコン画像横幅',
    'admin_callcenter_chatwindow_label_startup_initial_response': '小窓起動時の初回応答',
    'admin_callcenter_chatwindow_label_name': '名前',
    'admin_callcenter_chatwindow_label_name_change_field_label': '名前変更欄ラベル',
    'admin_callcenter_chatwindow_label_name_entry_description_field': '名前入力欄説明文',
    'admin_callcenter_chatwindow_label_name_field_label': '名前入力欄ラベル',
    'admin_callcenter_chatwindow_label_radio_allow': '許可する',
    'admin_callcenter_chatwindow_label_radio_check': '確認する',
    'admin_callcenter_chatwindow_label_radio_display': '表示する',
    'admin_callcenter_chatwindow_label_radio_close_room': 'ルームを終了する',
    'admin_callcenter_chatwindow_label_radio_hidden': '表示しない',
    'admin_callcenter_chatwindow_label_radio_not_allow': '許可しない',
    'admin_callcenter_chatwindow_label_radio_not_check': '確認しない',
    'admin_callcenter_chatwindow_label_radio_not_close_room': 'ルームを終了しない',
    'admin_callcenter_chatwindow_label_radio_not_save': '保持しない',
    'admin_callcenter_chatwindow_label_radio_save': '保持する',
    'admin_callcenter_chatwindow_label_radio_speak_bot': 'ボットから発話する',
    'admin_callcenter_chatwindow_label_radio_wait_guest': 'ゲストの発話を待つ',
    'admin_callcenter_chatwindow_label_restart_chat_service': '再読み込みラベル',
    'admin_callcenter_chatwindow_label_room_exit_button': 'ルーム終了ボタン',
    'admin_callcenter_chatwindow_label_room_status_at_end_of_inquiry': '問い合わせ終了時のルーム状態',
    'admin_callcenter_chatwindow_label_launch_icon': '起動アイコン用',
    'admin_callcenter_chatwindow_label_latest_version': '最新バージョン(#{value})',
    'admin_callcenter_chatwindow_label_left': '左',
    'admin_callcenter_chatwindow_label_left_lower': '左下',
    'admin_callcenter_chatwindow_label_lineHeight': '入力エリア高さ(行数)',
    'admin_callcenter_chatwindow_label_lineHeight_color_setting': '入力エリアのカラー設定',
    'admin_callcenter_chatwindow_label_lineHeight_description': '入力エリア説明文',
    'admin_callcenter_chatwindow_label_link_text': 'リンクテキスト',
    'admin_callcenter_chatwindow_label_link_to_open_in_popup': 'ポップアップで開くリンク',
    'admin_callcenter_chatwindow_label_operation_check': '動作確認',
    'admin_callcenter_chatwindow_label_right': '右',
    'admin_callcenter_chatwindow_label_right_lower': '右下',
    'admin_callcenter_chatwindow_label_ruled_line': '罫線',
    'admin_callcenter_chatwindow_label_send_button_background': '送信ボタンの背景',
    'admin_callcenter_chatwindow_label_send_button_icon': '送信ボタンのアイコン',
    'admin_callcenter_chatwindow_label_send_control_modify_button': '送信制御　修正ボタン',
    'admin_callcenter_chatwindow_label_send_control_send_button': '送信制御　送信ボタン',
    'admin_callcenter_chatwindow_label_send_location_information': '位置情報の送信',
    'admin_callcenter_chatwindow_label_send_message_by_enterKey': 'Enterキーによるメッセージ送信',
    'admin_callcenter_chatwindow_label_sender_icon': '送信者アイコン',
    'admin_callcenter_chatwindow_label_show_url_preview': 'URLプレビューの表示',
    'admin_callcenter_chatwindow_label_text': 'テキスト',
    'admin_callcenter_chatwindow_label_text_position': 'テキストの位置',
    'admin_callcenter_chatwindow_label_text_position_center': '中央寄せ',
    'admin_callcenter_chatwindow_label_text_position_left': '左寄せ',
    'admin_callcenter_chatwindow_label_text_position_right': '右寄せ',
    'admin_callcenter_chatwindow_label_timeline_background': 'タイムラインの背景',
    'admin_callcenter_chatwindow_label_transmission_control_description': '送信制御説明文',
    'admin_callcenter_chatwindow_label_type_embed': '埋め込みタイプ',
    'admin_callcenter_chatwindow_label_type_embed_pc_display': '(埋め込みタイプ PC表示)',
    'admin_callcenter_chatwindow_label_type_select_001': 'ウィジェットタイプ1',
    'admin_callcenter_chatwindow_label_type_select_002': 'ウィジェットタイプ2',
    'admin_callcenter_chatwindow_label_type_select_003': 'ウィジェットタイプ3',
    'admin_callcenter_chatwindow_label_type_window': '小窓タイプ',
    'admin_callcenter_chatwindow_label_under': '下',
    'admin_callcenter_chatwindow_label_usual': '通常',
    'admin_callcenter_chatwindow_label_version': '小窓バージョン',
    'admin_callcenter_chatwindow_label_width': '横幅',
    'admin_callcenter_chatwindow_label_window_size': '小窓のサイズ',
    'admin_callcenter_chatwindow_label_whole_color_setting': '小窓全体のカラー設定',
    'admin_callcenter_chatwindow_tab_base_design_setting': '基本デザイン設定',
    'admin_callcenter_chatwindow_tab_base_design_setting_title_display_setting': '表示設定',
    'admin_callcenter_chatwindow_tab_base_design_setting_title_launch_icon': '起動アイコン',
    'admin_callcenter_chatwindow_tab_base_design_setting_title_select_version': 'バージョン選択',
    'admin_callcenter_chatwindow_tab_detail_design_setting': '詳細デザイン設定',
    'admin_callcenter_chatwindow_tab_detail_design_setting_chat_timeline': 'チャットタイムライン',
    'admin_callcenter_chatwindow_tab_detail_design_setting_footer': 'フッター',
    'admin_callcenter_chatwindow_tab_detail_design_setting_header': 'ヘッダー',
    'admin_callcenter_chatwindow_tab_detail_design_setting_header_top_icon_button': 'ヘッダー上部のアイコンボタン',
    'admin_callcenter_chatwindow_tab_detail_design_setting_message_modal': 'メッセージモーダル',
    'admin_callcenter_chatwindow_tab_css_setting': 'CSS設定',
    'admin_callcenter_chatwindow_tab_css_setting_edit_style': 'スタイル編集',
    'admin_callcenter_chatwindow_tab_action_setting': 'アクション設定',
    'admin_callcenter_chatwindow_tab_action_setting_title_guest_suggestion_setting': 'ゲスト向けサジェスト設定',
    'admin_callcenter_chatwindow_tab_action_setting_title_operation_setting_for_guest': 'ゲスト向け操作設定',
    'admin_callcenter_chatwindow_tab_action_setting_title_other_setting': 'その他設定',
    'admin_callcenter_chatwindow_tab_message_setting': 'メッセージ設定',
    'admin_callcenter_chatwindow_tab_message_setting_title_error_message': 'エラーメッセージ',
    'admin_callcenter_chatwindow_tab_message_setting_title_other_message': 'その他メッセージ',
    'admin_callcenter_chatwindow_tab_message_setting_title_send_control_message': '送信制御メッセージ',
    'admin_callcenter_embed_dialog_reset_body': '初期化してもよろしいですか？',
    'admin_callcenter_embed_dialog_reset_title': '初期化確認',
    'admin_callcenter_embed_doc_css_sample_link': 'CSSサンプルはこちら',
    'admin_callcenter_embed_doc_link': 'より詳細なオプションはこちら',
    'admin_callcenter_embed_doc_script_sample_link': 'スクリプトサンプルはこちら',
    'admin_callcenter_embed_error_required': '${field}は必須項目です',
    'admin_callcenter_embed_file_upload_successful': 'ファイルのロードに成功しました。設定内容を適用するには保存を行ってください。',
    'admin_callcenter_embed_file_upload_failed': 'ファイル名が不正です。',
    'admin_callcenter_embed_file_validation_error': 'JSONファイルフォーマットエラー。',
    'admin_callcenter_embed_form_desc_base_setting': '基本設定',
    'admin_callcenter_embed_form_desc_button_labels': 'ボタン文言',
    'admin_callcenter_embed_form_desc_custom_style': 'スタイルの上書き設定',
    'admin_callcenter_embed_form_desc_editor': 'パラメータを編集してください',
    'admin_callcenter_embed_form_desc_error_labels': 'エラー文言',
    'admin_callcenter_embed_form_desc_header_setting': 'ヘッダー設定',
    'admin_callcenter_embed_form_desc_initialize_icon_message': 'メッセージ本文',
    'admin_callcenter_embed_form_desc_initialize_icon_note': 'この設定は、MobiAgentClient.initialize()を利用している場合にのみ有効です',
    'admin_callcenter_embed_form_desc_initialize_icon_setting': '起動アイコン設定',
    'admin_callcenter_embed_form_desc_label_setting': '文言設定',
    'admin_callcenter_embed_form_desc_message_setting': 'メッセージUI設定',
    'admin_callcenter_embed_form_desc_misc_labels': 'その他文言',
    'admin_callcenter_embed_form_desc_option_setting': 'オプション設定',
    'admin_callcenter_embed_form_desc_privacy_masking_policy_labels': '送信制限文言',
    'admin_callcenter_embed_form_desc_review_labels': '評価機能文言',
    'admin_callcenter_embed_form_desc_script_option': 'scriptタグで指定したオプションは上記の設定よりも優先されます',
    'admin_callcenter_embed_form_desc_script_preview': 'サンプルページで動作確認が可能です。※ログアウトするか別のブラウザで確認してください',
    'admin_callcenter_embed_form_desc_script': '以下の内容をbodyタグの最後に追加してください',
    'admin_callcenter_embed_form_desc_select_type': 'タイプ選択',
    'admin_callcenter_embed_form_desc_select_version': 'バージョン選択',
    'admin_callcenter_embed_form_desc_system_messages': 'システムメッセージ',
    'admin_callcenter_embed_form_label_css_edit': 'CSS編集',
    'admin_callcenter_embed_hint_bubbleChat': '有効にした場合、背景色/テキストカラーはメッセージに対して適用されます。無効の場合テキストカラーのみ発言者の名前に適用されます。',
    'admin_callcenter_embed_hint_bubbleChatDateDisplay': '「吹き出し表示」を有効にしている場合のみ設定可能です。',
    'admin_callcenter_embed_hint_button_labels': 'ボタン文言を指定してください',
    'admin_callcenter_embed_hint_classic_type': '以前の形式の小窓を利用する場合、パラメーター指定はscriptタグ内で行ってください',
    'admin_callcenter_embed_hint_color': '色指定は"#rrggbb"の16進数表記で指定してください',
    'admin_callcenter_embed_hint_disabled': '無効にした場合、ゲスト問い合わせ機能は表示されません',
    'admin_callcenter_embed_hint_embed_type': '埋め込みビューは親エレメントの左上基準で表示されます\n必ず起動スクリプト内で挿入先親エレメントIDをoptions.targetIdとして指定してください\nモバイルブラウザでは小窓タイプ（アイコン、ヘッダー2、ヘッダー3 非表示）が適用されます',
    'admin_callcenter_embed_hint_error_labels': 'エラー表記を指定してください',
    'admin_callcenter_embed_hint_i18n_cc_button_cancel': '',
    'admin_callcenter_embed_hint_i18n_cc_button_download': '',
    'admin_callcenter_embed_hint_i18n_cc_button_quit': '',
    'admin_callcenter_embed_hint_i18n_cc_button_send': '',
    'admin_callcenter_embed_hint_i18n_cc_error_asOperator': '',
    'admin_callcenter_embed_hint_i18n_cc_error_cannotUse': '',
    'admin_callcenter_embed_hint_i18n_cc_error_initialTimeout_1': '',
    'admin_callcenter_embed_hint_i18n_cc_error_initialTimeout_2': '',
    'admin_callcenter_embed_hint_i18n_cc_error_maintenance': '未設定の場合、`その他エラー`が適用されます',
    'admin_callcenter_embed_hint_i18n_cc_error_offline_1': '',
    'admin_callcenter_embed_hint_i18n_cc_error_offline_2': '',
    'admin_callcenter_embed_hint_i18n_cc_error_operatorNotReady_1': '',
    'admin_callcenter_embed_hint_i18n_cc_error_operatorNotReady_2': '',
    'admin_callcenter_embed_hint_i18n_cc_error_outOfService': '',
    'admin_callcenter_embed_hint_i18n_cc_error_other': '',
    'admin_callcenter_embed_hint_i18n_cc_error_textmsg_with_ngword': '',
    'admin_callcenter_embed_hint_i18n_cc_error_too_large_file': '',
    'admin_callcenter_embed_hint_i18n_cc_guest_sent_file': 'ゲストのタイムラインには表示されません',
    'admin_callcenter_embed_hint_i18n_cc_guest_sent_image': 'ゲストのタイムラインには表示されません',
    'admin_callcenter_embed_hint_i18n_cc_guest_sent_location': 'ゲストのタイムラインには表示されません',
    'admin_callcenter_embed_hint_i18n_cc_label_show_guest_review': 'Ver.22以降の小窓では表示されません',
    'admin_callcenter_embed_hint_i18n_cc_header1_text': '最大100文字、改行を含めることはできません',
    'admin_callcenter_embed_hint_i18n_cc_header2_text': '最大100文字、改行を含めることはできません',
    'admin_callcenter_embed_hint_i18n_cc_header3_text': '最大100文字、改行を含めることはできません',
    'admin_callcenter_embed_hint_i18n_cc_label_back_to_chat': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_comment': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_desc': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_no_rating': '評価なし',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_rating_1': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_rating_2': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_rating_3': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_rating_4': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_rating_5': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_rating': '',
    'admin_callcenter_embed_hint_i18n_cc_label_guest_review_title': '',
    'admin_callcenter_embed_hint_i18n_cc_label_name_is_required': '',
    'admin_callcenter_embed_hint_i18n_cc_label_name_update': '',
    'admin_callcenter_embed_hint_i18n_cc_label_name': '',
    'admin_callcenter_embed_hint_i18n_cc_label_operation_closed': '',
    'admin_callcenter_embed_hint_i18n_cc_label_operator_writing': '',
    'admin_callcenter_embed_hint_i18n_cc_label_quit_chat': '',
    'admin_callcenter_embed_hint_i18n_cc_label_quit_chat_confirm': '',
    // 'admin_callcenter_embed_hint_i18n_cc_label_show_guest_review': '',
    'admin_callcenter_embed_hint_i18n_cc_link_openInPopup': '',
    'admin_callcenter_embed_hint_i18n_cc_link_restartChatService': '',
    'admin_callcenter_embed_hint_i18n_cc_operator_sent_file': '',
    'admin_callcenter_embed_hint_i18n_cc_placeholder_reviewInput': '',
    'admin_callcenter_embed_hint_i18n_cc_placeholder_textInput': '',
    'admin_callcenter_embed_hint_i18n_cc_system_message_name_updated': '',
    'admin_callcenter_embed_hint_i18n_cc_system_message_review_submit': '',
    'admin_callcenter_embed_hint_i18n_cc_system_message_session_closed': '',
    'admin_callcenter_embed_hint_i18n_cc_system_message_session_closed_byGuestWeb': '',
    'admin_callcenter_embed_hint_i18n_cc_system_message_session_restarted': '',
    'admin_callcenter_embed_hint_i18n_cc_system_message_session_started': '',
    'admin_callcenter_embed_hint_i18n_cc_welcome_message': '<br>は改行に変換されます',
    'admin_callcenter_embed_hint_image_avatar_autoReply': '推奨 w60/h60(px) png形式',
    'admin_callcenter_embed_hint_image_avatar_guest': '推奨 w60/h60(px) png形式',
    'admin_callcenter_embed_hint_image_avatar_operator': '推奨 w60/h60(px) png形式',
    'admin_callcenter_embed_hint_image_header_1': '推奨 w36/h36(px) png形式',
    'admin_callcenter_embed_hint_image_header_2': '推奨 w98/h32(px) png形式',
    'admin_callcenter_embed_hint_image_header_icon': '推奨 w120/h120(px) png形式',
    'admin_callcenter_embed_hint_image_initialize_icon': '推奨 w64/h64(px) png形式',
    'admin_callcenter_embed_hint_labelTexts': '改行を含めることはできません',
    'admin_callcenter_embed_hint_misc_labels': 'その他文言の指定をしてください',
    'admin_callcenter_embed_hint_options_googleMapAPIVersion': '特定バージョンのGoogle Map JavaScript APIを利用する場合のみ指定してください。未指定時は"weekly"が利用されます。',
    'admin_callcenter_embed_hint_options_sendStartHiddenMessage': 'ONの場合は小窓を起動した時点でチャットボットを自動で開始します。OFFの場合はユーザーが任意の文字を送信してからチャットボットを開始します。',
    'admin_callcenter_embed_hint_options_guestQuitEnable': 'ゲストが問い合わせルームを終了状態にすることができます。\n満足度アンケート>設定>アンケートタイプ が  [オリジナルアンケートを利用する] に設定されている場合はOFFにできません。\n 満足度アンケート>設定>アンケートタイプ が [満足度評価のみ利用する] に設定されている場合、ゲストがお問い合わせを終了し評価を送信すると、この設定に関わらずにルームが終了します。',
    'admin_callcenter_embed_hint_options_hidePopupLink': 'ONにした場合、ポップアップで開くリンクを表示しません',
    'admin_callcenter_embed_hint_options_notificationSoundFileUrl': 'デスクトップ通知で再生する通知音ファイルの有効なHTTPSのURLを指定してください。未指定時は通知音は再生されません。',
    'admin_callcenter_embed_hint_options_notificationTitle': 'デスクトップ通知で表示するタイトルを指定してください。未指定時はデスクトップ通知が行われません。',
    'admin_callcenter_embed_hint_options_sendOnEnter': 'OFFの場合はShift+Enterで送信となります。ONの場合スマートフォンでは改行ボタンを押下してもメッセージが送信されます。',
    'admin_callcenter_embed_hint_options_skipDomainStatusCheck': '自動応答を利用する場合はtrueにしてください',
    'admin_callcenter_embed_hint_privacy_masking_policy_labels': '個人情報送信制御モーダルに表示する文言の指定をしてください',
    'admin_callcenter_embed_hint_review_labels': '評価機能関連文言を指定してください',
    'admin_callcenter_embed_hint_system_messages': '特定のイベント発生時に自動で送信されるメッセージを指定します。ゲスト画面には表示されません',
    'admin_callcenter_embed_hint_window_type': '小窓ビューはウィンドウ右下に表示されます',
    'admin_callcenter_embed_label_avatar_autoReply': '自動応答アバター',
    'admin_callcenter_embed_label_avatar_guest': 'ゲストアバター',
    'admin_callcenter_embed_label_avatar_icon': 'アイコン',
    'admin_callcenter_embed_label_avatar_name': '名前',
    'admin_callcenter_embed_label_avatar_operator': 'オペレーターアバター',
    'admin_callcenter_embed_label_avatar': 'アバター',
    'admin_callcenter_embed_label_background_color': '背景色',
    'admin_callcenter_embed_label_base_color': '基本色',
    'admin_callcenter_embed_label_bubbleChat': '吹き出し表示',
    'admin_callcenter_embed_label_bubbleChatDateDisplay': '同一送信日時をまとめて表示する',
    'admin_callcenter_embed_label_color_setting': '色設定',
    'admin_callcenter_embed_label_csv_upload': 'CSV アップロード',
    'admin_callcenter_embed_label_header_1': 'ヘッダー 1',
    'admin_callcenter_embed_label_header_2': 'ヘッダー 2',
    'admin_callcenter_embed_label_header_3': 'ヘッダー 3',
    'admin_callcenter_embed_label_header_4': 'ヘッダー',
    'admin_callcenter_embed_label_header_icon': 'アイコン',
    'admin_callcenter_embed_label_header_image': '画像',
    'admin_callcenter_embed_label_header_text': 'テキスト',
    'admin_callcenter_embed_label_height': '高さ(px)',
    'admin_callcenter_embed_label_i18n_cc_button_cancel': 'キャンセルボタン',
    'admin_callcenter_embed_label_i18n_cc_button_download': 'ダウンロードボタン',
    'admin_callcenter_embed_label_i18n_cc_button_quit': '終了ボタン',
    'admin_callcenter_embed_label_i18n_cc_button_send': '送信ボタン',
    'admin_callcenter_embed_label_i18n_cc_error_asOperator': 'オペレーターとしてログインエラー',
    'admin_callcenter_embed_label_i18n_cc_error_cannotUse': '利用不可エラー',
    'admin_callcenter_embed_label_i18n_cc_error_initialTimeout_1': 'オペレーター対応待ち 1行目',
    'admin_callcenter_embed_label_i18n_cc_error_initialTimeout_2': 'オペレーター対応待ち 2行目',
    'admin_callcenter_embed_label_i18n_cc_error_maintenance': 'メンテナンスエラー',
    'admin_callcenter_embed_label_i18n_cc_error_offline_1': 'オフライン 1行目',
    'admin_callcenter_embed_label_i18n_cc_error_offline_2': 'オフライン 2行目',
    'admin_callcenter_embed_label_i18n_cc_error_operatorNotReady_1': 'オペレーター混雑エラー 1行目',
    'admin_callcenter_embed_label_i18n_cc_error_operatorNotReady_2': 'オペレーター混雑エラー 2行目',
    'admin_callcenter_embed_label_i18n_cc_error_outOfService': '利用時間外エラー',
    'admin_callcenter_embed_label_i18n_cc_error_other': 'その他エラー',
    'admin_callcenter_embed_label_i18n_cc_error_textmsg_with_ngword': 'NGワードエラー',
    'admin_callcenter_embed_label_i18n_cc_error_too_large_file': 'ファイルサイズエラー',
    'admin_callcenter_embed_label_i18n_cc_guest_sent_file': 'ゲストファイル送信時',
    'admin_callcenter_embed_label_i18n_cc_guest_sent_image': 'ゲスト画像送信時',
    'admin_callcenter_embed_label_i18n_cc_guest_sent_location': 'ゲスト位置情報送信時',
    'admin_callcenter_embed_label_initialize_icon_text': 'タイトル',
    'admin_callcenter_embed_label_i18n_cc_label_back_to_chat': 'チャットに戻る',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_comment': 'レビューコメント',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_desc': 'レビュー 概要説明',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_rating_1': '評価 1',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_rating_2': '評価 2',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_rating_3': '評価 3',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_rating_4': '評価 4',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_rating_5': '評価 5',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_rating_default': '評価選択',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_rating': '評価',
    'admin_callcenter_embed_label_i18n_cc_label_guest_review_title': 'タイトル',
    'admin_callcenter_embed_label_i18n_cc_label_name_is_required': '名前を入力してください',
    'admin_callcenter_embed_label_i18n_cc_label_name_update': '名前を変更する',
    'admin_callcenter_embed_label_i18n_cc_label_name': '名前',
    'admin_callcenter_embed_label_i18n_cc_label_operation_closed': '対応終了時',
    'admin_callcenter_embed_label_i18n_cc_label_operator_writing': 'オペレーター入力中',
    'admin_callcenter_embed_label_i18n_cc_label_privacy_masking_policy_block_1': '説明文 1行目',
    'admin_callcenter_embed_label_i18n_cc_label_privacy_masking_policy_block_2': '説明文 2行目',
    'admin_callcenter_embed_label_i18n_cc_label_privacy_masking_policy_block_edit': '送信制御 修正ボタン',
    'admin_callcenter_embed_label_i18n_cc_label_privacy_masking_policy_mask_1': '説明文 1行目',
    'admin_callcenter_embed_label_i18n_cc_label_privacy_masking_policy_mask_2': '説明文 2行目',
    'admin_callcenter_embed_label_i18n_cc_label_privacy_masking_policy_mask_3': '説明文 3行目',
    'admin_callcenter_embed_label_i18n_cc_label_privacy_masking_policy_mask_send': '送信制御 送信ボタン',
    'admin_callcenter_embed_label_i18n_cc_label_privacy_masking_policy_mask_edit': '送信制御 修正ボタン',
    'admin_callcenter_embed_label_i18n_cc_label_quit_chat': 'お問い合わせを終了する',
    'admin_callcenter_embed_label_i18n_cc_label_quit_chat_confirm': '終了確認',
    'admin_callcenter_embed_label_i18n_cc_label_read': '既読表示',
    'admin_callcenter_embed_label_i18n_cc_label_show_guest_review': 'レビュー評価リンク',
    'admin_callcenter_embed_label_i18n_cc_link_openInPopup': 'ポップアップで開くリンク',
    'admin_callcenter_embed_label_i18n_cc_link_restartChatService': 'リスタートリンク',
    'admin_callcenter_embed_label_i18n_cc_operator_sent_file': 'オペレーターファイル送信時',
    'admin_callcenter_embed_label_i18n_cc_placeholder_reviewInput': '評価コメント入力プレースホルダー',
    'admin_callcenter_embed_label_i18n_cc_placeholder_textInput': 'メッセージ入力プレースホルダー',
    'admin_callcenter_embed_label_i18n_cc_system_message_name_updated': '名前更新時',
    'admin_callcenter_embed_label_i18n_cc_system_message_review_submit': '評価送信時',
    'admin_callcenter_embed_label_i18n_cc_system_message_session_closed': 'セッション終了時',
    'admin_callcenter_embed_label_i18n_cc_system_message_session_closed_byGuestWeb': 'Web小窓終了時',
    'admin_callcenter_embed_label_i18n_cc_system_message_session_restarted': 'セッション再開時',
    'admin_callcenter_embed_label_i18n_cc_system_message_session_started': 'セッション開始時',
    'admin_callcenter_embed_label_i18n_cc_welcomeMessage': '初期表示メッセージ',
    'admin_callcenter_embed_label_image_default': 'デフォルト',
    'admin_callcenter_embed_label_image_none': '表示しない',
    'admin_callcenter_embed_label_image_upload': 'アップロード',
    'admin_callcenter_embed_label_image_user': 'プロフィール優先',
    'admin_callcenter_embed_label_options_allowFileUpload': 'ファイルアップロード許可',
    'admin_callcenter_embed_label_options_allowLocationSend': '位置情報送信許可',
    'admin_callcenter_embed_label_options_disableSuggest': 'サジェスト無効化',
    'admin_callcenter_embed_label_options_suggest_count': 'サジェスト上限数',
    'admin_callcenter_embed_label_options_suggest_allowCash': 'サジェストのキャッシュ機能',
    'admin_callcenter_embed_label_options_disableUrlPreview': 'URLプレビュー無効化',
    'admin_callcenter_embed_label_options_googleMapAPIVersion': 'Google Map JavaScript APIバージョン',
    'admin_callcenter_embed_label_options_sendStartHiddenMessage': '小窓起動時にチャットボットを自動で開始する',
    'admin_callcenter_embed_label_options_guestQuitEnable': '問い合わせ終了時にルームを終了する',
    'admin_callcenter_embed_label_options_hidePopupLink': 'ポップアップで開くリンクを非表示',
    'admin_callcenter_embed_label_options_notificationSoundFileUrl': 'デスクトップ通知音再生ファイルURL',
    'admin_callcenter_embed_label_options_notificationTitle': 'デスクトップ通知タイトル',
    'admin_callcenter_embed_label_options_sendOnEnter': 'Enterで送信',
    'admin_callcenter_embed_label_options_showAvatar': 'アイコンを表示する',
    'admin_callcenter_embed_label_options_showNameInput': '名前入力欄表示',
    'admin_callcenter_embed_label_options_skipDomainStatusCheck': 'ドメインステータス確認スキップ',
    'admin_callcenter_embed_label_primary_color': 'プライマリーカラー',
    'admin_callcenter_embed_label_secondary_color': 'セカンダリーカラー',
    'admin_callcenter_embed_label_text_color': 'テキストカラー',
    'admin_callcenter_embed_label_type_classic': '旧タイプ',
    'admin_callcenter_embed_label_type_embed': '埋め込みタイプ',
    'admin_callcenter_embed_label_type_select': 'ウィジェットタイプ',
    'admin_callcenter_embed_label_type_window': '小窓タイプ',
    'admin_callcenter_embed_label_width': '幅(px)',
    'admin_callcenter_ember_message_lineheight':'入力エリア 高さ (行数)',
    'admin_callcenter_embed_page_subtitle': 'WEB小窓設定パラメータ編集',
    'admin_callcenter_embed_page_title': 'WEB小窓設定',
    'admin_callcenter_embed_script_desc_initialize': '起動アイコンを利用する場合はこちら',
    'admin_callcenter_external_userId_table_page_desc': '外部連携IDに対応する値が設定された場合オペレーター画面で表示されます。区分を利用する場合などに設定してください。',
    'admin_callcenter_external_userId_table_page_title': '外部連携ID変換テーブル',
    'admin_callcenter_extraGuestAttributes_page_confirm_body_confirm': '拡張ゲスト属性定義を更新してもよろしいですか？',
    'admin_callcenter_extraGuestAttributes_page_confirm_title': '拡張ゲスト属性更新',
    'admin_callcenter_extraGuestAttributes_page_title': '拡張ゲスト属性編集',
    'admin_callcenter_extraGuestAttributes_page_searchable_attribute': '検索対象',
    'admin_callcenter_extraGuestAttributes_page_non_searchable_attribute': '検索対象外',
    'admin_callcenter_extraGuestAttributes': '拡張ゲスト属性',
    'admin_callcenter_extraGuestAttributes_page_confirm_body_inconsistency_notice': '定義が変更されています。\n登録済みのゲスト情報が存在する場合、または登録済みのゲスト情報を外部システムに連携している場合などに、\nデータ不整合が発生しゲスト情報が正しく保存できなくなる可能性があります。',
    'admin_callcenter_extraRoomTags_page_confirm_body_inconsistency_notice': '定義が変更されています。\n登録済みのルーム情報が存在する場合、または登録済みのルーム情報を外部システムに連携している場合などに、\nデータ不整合が発生しルーム情報が正しく保存できなくなる可能性があります。',
    'admin_callcenter_extraRoomTags_disable_hint': 'このタグの利用を無効化します',
    'admin_callcenter_extraRoomTags_error_code_duplicate': 'コードが重複しています',
    'admin_callcenter_extraRoomTags_error_code_format': 'コードは半角英数1~8文字で入力してください',
    'admin_callcenter_extraRoomTags_error_label_invalid': 'ラベルは1~50文字で入力してください',
    'admin_callcenter_extraRoomTags_error_max_less_min': '最大値は最小値以上の値を入力してください',
    'admin_callcenter_extraRoomTags_error_negative_value': '値は正の値を入力してください',
    'admin_callcenter_extraRoomTags_label': 'ラベル',
    'admin_callcenter_extraRoomTags_length_max': '最大',
    'admin_callcenter_extraRoomTags_length_min': '最小',
    'admin_callcenter_extraRoomTags_page_confirm_body_confirm': '拡張ルームタグ定義を更新してもよろしいですか？',
    'admin_callcenter_extraRoomTags_page_confirm_title': '拡張ルームタグ更新',
    'admin_callcenter_extraRoomTags_page_title': '拡張ルームタグ編集',
    'admin_callcenter_extraRoomTags_required_hint': 'このタグを必須にします',
    'admin_callcenter_extraRoomTags_tag': 'タグ',
    'admin_callcenter_extraRoomTags_type_bool': '真偽値(0/1)選択',
    'admin_callcenter_extraRoomTags_type_change_notice': '定義を更新した場合、既に存在するレコードと整合性が取れなくなります。',
    'admin_callcenter_extraRoomTags_type_nowTime': '現在時刻設定',
    'admin_callcenter_extraRoomTags_type_number': '数値入力',
    'admin_callcenter_extraRoomTags_type_selectMulti': '複数選択',
    'admin_callcenter_extraRoomTags_type_selectOne': '択一選択',
    'admin_callcenter_extraRoomTags_type_text': 'テキスト入力',
    'admin_callcenter_extraRoomTags_type': 'タイプ',
    'admin_callcenter_extraRoomTags_validation_hint_max': '数値入力の場合は値の最大値、テキスト入力の場合は値の最大桁数を指定します',
    'admin_callcenter_extraRoomTags_validation_hint_min': '数値入力の場合は値の最小値、テキスト入力の場合は値の最小桁数を指定します',
    'admin_callcenter_extraRoomTags_validation': '入力制限',
    'admin_callcenter_extraRoomTags': '拡張ルームタグ',
    'admin_callcenter_crmConnectSf_title': 'CRM Connect',
    'admin_callcenter_finishRooms_dialog_button_download1': 'ルーム開始時間のみを検索条件に適用',
    'admin_callcenter_finishRooms_dialog_button_download2': 'その他の検索条件を適用',
    'admin_callcenter_finishRooms_dialog_desc_download1': '検索条件を指定してください。',
    'admin_callcenter_finishRooms_dialog_desc_download2': '「ルーム開始時間のみを検索条件に適用」した場合、すべてのデータは1ファイルにまとめてダウンロードされます。',
    'admin_callcenter_finishRooms_dialog_desc_download3': '「その他の検索条件を適用」した場合、データが10000件以上存在する場合には複数ファイルに分割してダウンロードされます。',
    'admin_callcenter_finishRooms_label_condition_finishPhrase': '完了フレーズ',
    'admin_callcenter_finishRooms_label_condition_finishReason': '完了コード',
    'admin_callcenter_finishRooms_label_condition_middleItem': '中項目',
    'admin_callcenter_finishRooms_label_condition_operatorUserId': 'オペレーター',
    'admin_callcenter_finishRooms_label_condition_room_close_time': 'ルーム終了時間',
    'admin_callcenter_finishRooms_label_condition_room_finish_time': 'ルーム完了時間',
    'admin_callcenter_finishRooms_label_condition_room_start_time': 'ルーム開始時間',
    'admin_callcenter_finishRooms_label_condition_smallItem': '小項目',
    'admin_callcenter_finishRooms_label_condition_userId': 'ユーザー',
    'admin_callcenter_finishRooms_label_queryField': '検索条件',
    'admin_callcenter_finishRooms_label_select_condition': '検索条件を選択してください',
    'admin_callcenter_finishRooms_label_toDate': '検索する最後の日付',
    'admin_callcenter_finishRooms_page_title': '完了ルーム検索',
    'admin_callcenter_finishRoomsAdv_page_title': '完了ルーム検索',
    'admin_callcenter_fulltextRoomSearch_page_title': 'あいまい検索',
    'admin_callcenter_grouping_dialog_body_debugPrint_disable': 'オペレーター割り当てテーブルでの振り分け処理をデバッグ出力を無効にする',
    'admin_callcenter_grouping_dialog_body_debugPrint_enable': 'オペレーター割り当てテーブルでの振り分け処理をデバッグ出力を有効にする',
    'admin_callcenter_grouping_dialog_body_delete_group': 'グループを削除してもよろしいですか？',
    'admin_callcenter_grouping_dialog_body_delete_list': '振分条件を削除してもよろしいですか？',
    'admin_callcenter_grouping_dialog_body_groupingEnable_disable': 'グループ振分機能を無効にする。全て削除ボタンを実行した場合、現在の設定は削除されます',
    'admin_callcenter_grouping_dialog_body_groupingEnable_enable': 'グループ振分機能を有効にする',
    'admin_callcenter_grouping_dialog_title': 'グループ振分機能設定変更',
    'admin_callcenter_grouping_error_groupId_format': 'グループIDは1~16文字の半角英数を指定してください',
    'admin_callcenter_grouping_error_groupId_required': 'グループIDは必須項目です',
    'admin_callcenter_grouping_error_groupId_used': 'グループIDが振分指定されています。先に振分条件を削除してください',
    'admin_callcenter_grouping_error_list_condition_too_large': '条件は10個までしか指定できません',
    'admin_callcenter_grouping_error_list_name_required': '振分条件名は必須項目です',
    'admin_callcenter_grouping_error_refresh_room_members': 'グループ定義の更新に成功しましたが、既存ルームへのメンバー追加に失敗しました。',
    'admin_callcenter_grouping_hintText_groupId': 'グループIDはユニークである必要があります',
    'admin_callcenter_grouping_hintText_groupMaxRoom': 'このグループが対応可能なopen状態のルーム数\nこの設定で上限を無制限にしたい場合は-1を指定してください\nデフォルト: -1',
    'admin_callcenter_grouping_hintText_groupMaxRoomPerUser': 'このグループに属するオペレーターでログインしている（ステータス設定が有効な場合は「新規受付可」状態）のユーザー数に応じて\nこのグループが対応可能なルーム数を変化させる場合に利用します\nこの設定で上限を無制限にしたい場合は-1を指定してください\nデフォルト: -1',

    'admin_callcenter_grouping_hintText_groupMaxRoomByWorkingTime': '営業時間ごとのオペレーター対応可能数を設定している場合は、営業時間設定で営業時間ごとの対応可能数設定が必要です',
    'admin_callcenter_grouping_hintText_groupMaxRoomPerUserByWorkingTime': '営業時間ごとのオペレーター対応可能数を設定している場合は、営業時間設定で営業時間ごとの対応可能数設定が必要です',

    'admin_callcenter_grouping_hintText_groupName': 'グループ名を指定してください ',
    'admin_callcenter_grouping_hintText_groupOperatorAutoAssign': 'ステータス設定機能が無効の場合は自動割り当ては実施されません',
    'admin_callcenter_grouping_hintText_groupOperatorsEnable': 'オフを選択するとこのグループへの割り当ては失敗となります',
    'admin_callcenter_grouping_hintText_guestTypeCondition': 'ゲスト流入元を指定してください',
    'admin_callcenter_grouping_hintText_joinGroupsSelectionType': 'グループ割り当て方法を選択して下さい',
    'admin_callcenter_grouping_hintText_joinGroupsWeight': '重み付け 1 ~ 10000で指定',
    'admin_callcenter_grouping_hintText_listConditions': '条件には"roomTag1" ~ "roomTag60","roomTag200" ~ "roomTag249", "guestAttribute_$属性名"が利用できます\n条件の$属性名および値に `_` （半角アンダースコア）が含まれる場合は、 `＿` (全角アンダースコア)に置き換えて入力してください\n例) ゲスト属性 `m_01` が `1_a` の場合を指定する場合: 条件欄は `guestAttribute_m＿01` 、値欄には `1＿a` と入力してください',
    'admin_callcenter_grouping_label_debug': 'デバッグ出力',
    'admin_callcenter_grouping_label_enable': '有効',
    'admin_callcenter_grouping_label_groupMaxRoom': 'グループ最大対応可能数',
    'admin_callcenter_grouping_label_groupMaxRoomPerUser': '1オペレーターの対応可能問い合わせ数',
    'admin_callcenter_grouping_label_operator': '所属ユーザー',
    'admin_callcenter_grouping_label_holiday': '休日設定',
    'admin_callcenter_grouping_label_businessHours': '営業時間設定',
    'admin_callcenter_grouping_label_specialOpenDays': '特別な営業日時設定',
    'admin_callcenter_grouping_label_groupOperatorAutoAssign': '担当者自動割り当て',
    'admin_callcenter_grouping_label_groupOperatorForceAssign': '無条件担当者割り当てを有効にする',
    'admin_callcenter_grouping_hintText_groupOperatorForceAssign': '有効にすると、オペレーターのステータスとログイン状態に関わらず、ゲストの担当者 (tag79) に設定されたオペレーターに強制着信します。',
    'admin_callcenter_grouping_label_crmConnectSfGroupId': 'CRM Connect 設定グループID',
    'admin_callcenter_grouping_label_groups': 'グループ',
    'admin_callcenter_grouping_label_groupSelectionType_headFirst': '先頭から順に対応可能なグループへ割り当て',
    'admin_callcenter_grouping_label_groupSelectionType_minLoad': '負荷(対応中の数／最大数)の小さいグループへ割り当て',
    'admin_callcenter_grouping_label_groupSelectionType_random': 'ランダムに割り当て',
    'admin_callcenter_grouping_label_groupSelectionType_single': '単一グループ指定',
    'admin_callcenter_grouping_label_guestType': 'ゲストタイプ',
    'admin_callcenter_grouping_label_joinGroupsSelectionType': 'グループ割り当て方法',
    'admin_callcenter_grouping_label_list_conditionsAnd': 'AND条件',
    'admin_callcenter_grouping_label_list_conditionsExtra': '追加条件',
    'admin_callcenter_grouping_label_list_joinGroups': '担当グループ指定',
    'admin_callcenter_grouping_label_list_name': '名前',
    'admin_callcenter_grouping_label_list': '振分条件',
    'admin_callcenter_grouping_label_unselectedText': '設定しない',
    'admin_callcenter_grouping_page_desc_group': 'ユーザーグループを作成します',
    'admin_callcenter_grouping_page_desc_list_andCondition': '10個まで指定できます。条件未指定の場合、一致したとみなされます',
    'admin_callcenter_grouping_page_desc_list_target': '条件にマッチした場合参加させるグループを指定してください',
    'admin_callcenter_grouping_page_desc_list': '振分条件は上位のものからチェックし最初にヒットしたものが適用されます。最大30個まで作成できます',
    'admin_callcenter_grouping_page_title_group' :'グループ管理',
    'admin_callcenter_grouping_page_title_list_andCondition': '条件管理',
    'admin_callcenter_grouping_page_title_list_target': '振分対象指定',
    'admin_callcenter_grouping_page_title_list' :'振分条件管理',
    'admin_callcenter_guest_dialog_desc': 'インポートファイルの形式は\n'+
                                          '以下のようなJSON文字列を改行で区切ったテキストフォーマットとなります\n'+
                                          '詳細はサービス仕様書を参照してください',
    'admin_callcenter_guest_dialog_title': 'ゲストユーザーインポート',
    'admin_callcenter_guest_error_condition': '検索条件を入力してください',
    'admin_callcenter_guest_hint_facebook': 'FacebookユーザーIDを入力してください',
    'admin_callcenter_guest_hint_bedoreWeb': 'ユーザーIDを入力してください',
    'admin_callcenter_guest_hint_foreignUserId': '外部連携IDを入力してください',
    'admin_callcenter_guest_hint_line': '旧ビジネスコネクトAPIのMIDを入力してください',
    'admin_callcenter_guest_hint_line2': 'UIDを入力してください',
    'admin_callcenter_guest_hint_linecc': 'UIDを入力してください',
    'admin_callcenter_guest_hint_lineWorks': 'LINEWorksのユーザーID、ボットIDを入力してください',
    'admin_callcenter_guest_hint_mobiGuest': '#{mobiguest}のユーザーIDを入力してください'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'admin_callcenter_guest_hint_msbot': 'Microsoft Bot FrameworkのユーザーIDとチャネルID(skype,webchatなど)を入力してください',
    'admin_callcenter_guest_hint_userId': '`guest-`からはじまるゲストユーザーのIDを入力してください',
    'admin_callcenter_guest_page_title': 'ゲストユーザー管理',
    'admin_callcenter_hintText_external_url_': '利用できる変数\n- $foreign_userid : 外部連携ID',
    'admin_callcenter_linterSetting': '文章校正コンシェルジュ機能',
    'admin_callcenter_lintSetting_desc_enableTextlint': '機能を有効にしてもIEではご利用いただけません。\n機能をご利用いただくにはChrome、Firefox、Edgeの各最新バージョンをご利用ください。',
    'admin_callcenter_lintSetting_label_enableTextlint': '文章校正コンシェルジュ機能を有効にする',
    'admin_callcenter_lintSetting_label_rules': 'ルール',
    'admin_callcenter_lintSetting_label_ruleDetals': 'ルール詳細',
    'admin_callcenter_lintSetting_rule_desc_date-weekday-mismatch': '(例)\nOK: 2020年01月01日(水曜日)\nNG: 2020年01月01日(木曜日)',
    'admin_callcenter_lintSetting_rule_desc_ja-hiragana-hojodoushi': '(例)\nOK:〜していただく\nNG:〜して頂く',
    'admin_callcenter_lintSetting_rule_desc_ja-unnatural-alphabet': '(例)\nOK: aiueo\nNG: 対応でｋない',
    'admin_callcenter_lintSetting_rule_desc_max-comma': 'カンマ(,)は1文に4つまで利用できます',
    'admin_callcenter_lintSetting_rule_desc_max-ten': '読点(、)は1文に2つまで利用できます',
    'admin_callcenter_lintSetting_rule_desc_no-double-negative-ja': '(例)\nOK: 確かにそういった懸念は存在します。\nNG: 確かにそういった懸念はない事はない。',
    'admin_callcenter_lintSetting_rule_desc_no-doubled-conjunction': '(例)\nOK: 明日は晴れる。しかし寒い。\nNG: 明日は晴れる。しかし寒い。しかし外出する。',
    'admin_callcenter_lintSetting_rule_desc_no-doubled-conjunctive-particle-ga': '(例)\nOK: 出発したが、定刻には間に合わなかった。それでも無事会場に到着した。\nNG: 出発したが、定刻には間に合わなかったが、無事会場に到着した。',
    'admin_callcenter_lintSetting_rule_desc_no-doubled-joshi': '(例)\nOK: 私は彼が好きだ。\nNG: 私は彼は好きだ。',
    'admin_callcenter_lintSetting_rule_desc_no-dropping-the-ra': '(例)\nOK: お刺身を食べられない\nNG: お刺身を食べれない。',
    'admin_callcenter_lintSetting_rule_desc_no-exclamation-question-mark': '(例)\nOK: おはようございます。\nNG: おはようございます！',
    'admin_callcenter_lintSetting_rule_desc_no-mix-dearu-desumasu': '(例)\nOK: 今日はいい天気です。明日も晴れです。\nNG: 今日はいい天気である。明日も晴れです。',
    'admin_callcenter_lintSetting_rule_desc_no-nfd': '(例)\nOK: ポケット\nNG: ホ゜ケット ',
    'admin_callcenter_lintSetting_rule_desc_no-start-duplicated-conjunction': '同じ接続詞で始まる文は2文以上あける必要があります。',
    'admin_callcenter_lintSetting_rule_desc_no-unmatched-pair': '(例)\nOK: 「括弧の開始と終了が一致」\nNG: [括弧の開始と終了が不一致)',
    'admin_callcenter_lintSetting_rule_desc_prefer-tari-tari': '(例)\nOK: 階段を上がったり、下がったりする。\nNG:階段を上がったり、下がる。',
    'admin_callcenter_lintSetting_rule_desc_sentence-length': '1文は100文字まで利用できます',
    'admin_callcenter_lintSetting_rule_label_date-weekday-mismatch': '曜日不一致の検出',
    'admin_callcenter_lintSetting_rule_label_ja-hiragana-hojodoushi': '[日本語] 漢字よりもひらがなで表記したほうが読みやすい補助動詞の検出',
    'admin_callcenter_lintSetting_rule_label_ja-unnatural-alphabet': '[日本語] 不自然なアルファベットを検出',
    'admin_callcenter_lintSetting_rule_label_max-comma': '1文中におけるカンマ数のチェック',
    'admin_callcenter_lintSetting_rule_label_max-ten': '1文中における読点数のチェック',
    'admin_callcenter_lintSetting_rule_label_no-double-negative-ja': '[日本語] 二重否定の検出',
    'admin_callcenter_lintSetting_rule_label_no-doubled-conjunction': '[日本語] 同じ接続詞の連続検出',
    'admin_callcenter_lintSetting_rule_label_no-doubled-conjunctive-particle-ga': '[日本語] 逆接の接続助詞「が」の重複検出',
    'admin_callcenter_lintSetting_rule_label_no-doubled-joshi': '[日本語] 助詞の重複検出',
    'admin_callcenter_lintSetting_rule_label_no-dropping-the-ra': '[日本語] らぬき言葉の検出',
    'admin_callcenter_lintSetting_rule_label_no-exclamation-question-mark': 'エクスクラメーションマーク、クエスチョンマークの検出',
    'admin_callcenter_lintSetting_rule_label_no-mix-dearu-desumasu': '[日本語] 文の敬体(ですます調)、常体(である調)の混在の検出',
    'admin_callcenter_lintSetting_rule_label_no-nfd': 'NFDの検出',
    'admin_callcenter_lintSetting_rule_label_no-start-duplicated-conjunction': '重複した接続詞の検出',
    'admin_callcenter_lintSetting_rule_label_no-unmatched-pair': 'ペアキャラクター不一致の検出',
    'admin_callcenter_lintSetting_rule_label_prefer-tari-tari': '[日本語] 例示・並列表現の「～たり、（～たり）する」の検出',
    'admin_callcenter_lintSetting_rule_label_sentence-length': '1文中における文字数のチェック',
    'admin_callcenter_metrics_dailyReport_error_day_limit': '期間指定の最大幅は#{count}日です',
    'admin_callcenter_metrics_dailyReport_error_select_dates': '期間指定を選択してください',
    'admin_callcenter_metrics_dailyReport_error_to_more_than_from': '検索対象期間の開始終了が不正です',
    'admin_callcenter_metrics_dailyReport_form_title': '検索対象日を選択してください',
    'admin_callcenter_metrics_dailyReport_label_acceptCount': '完了数',
    'admin_callcenter_metrics_dailyReport_label_averageCloseTime': '平均対応時間\n(開始~終了)',
    'admin_callcenter_metrics_dailyReport_label_averageFinishTime': '平均後処理時間\n(終了~完了)',
    'admin_callcenter_metrics_dailyReport_label_averageMessageCount':'１ルーム\n平均発言数',
    'admin_callcenter_metrics_dailyReport_label_finishRoomCategories': 'カテゴリー別問合せ数',
    'admin_callcenter_metrics_dailyReport_label_finishRoomMetrics': '完了ルーム統計',
    'admin_callcenter_metrics_dailyReport_label_messageCount':'発言数',
    'admin_callcenter_metrics_dailyReport_label_msgPub':'メッセージ数',
    'admin_callcenter_metrics_dailyReport_label_msgPubAvg':'１ルーム 平均発言数',
    'admin_callcenter_metrics_dailyReport_label_opratorUniqCount':' 担当者\nユニーク数',
    'admin_callcenter_metrics_dailyReport_label_rejectCount': '放棄数',
    'admin_callcenter_metrics_dailyReport_label_totalCount': '総件数',
    'admin_callcenter_metrics_dailyReport_label_undefined': '未指定',
    'admin_callcenter_metrics_dailyReport_lable_check_all': '全ての項目',
    'admin_callcenter_metrics_data_label_msg__id': 'メッセージID',
    'admin_callcenter_metrics_data_label_msg_dateStr': '送信時刻',
    'admin_callcenter_metrics_data_label_msg_roomId': 'ルームID',
    'admin_callcenter_metrics_data_label_msg_userId': '送信者ID',
    'admin_callcenter_metrics_data_label_msg_system': 'システムメッセージ',
    'admin_callcenter_metrics_data_label_msg_body': '本文',
    'admin_callcenter_metrics_data_label_msg_type': 'メッセージタイプ',
    'admin_callcenter_metrics_data_label_msg_data': 'メッセージデータ',
    'admin_callcenter_metrics_data_label_msg_extra': 'メッセージに付随するデータ',
    'admin_callcenter_metrics_data_label_msg_removed': '削除フラグ',
    'admin_callcenter_metrics_data_label_msg_removedDate': '削除された日時',
    'admin_callcenter_metrics_data_label_msgUser_name': 'メッセージ発言者',
    'admin_callcenter_metrics_data_label_msgUser_permitLevel': '発言者（権限別）',
    'admin_callcenter_metrics_data_label_roomOperator_name': 'ルーム担当者',
    'admin_callcenter_metrics_data_label_roomOperator_permitLevel': 'ルーム担当者（権限別）',
    'admin_callcenter_metrics_data_label_room_createdAtStr': '作成時日時',
    'admin_callcenter_metrics_data_label_room_desc': '発言者のその他の情報',
    'admin_callcenter_metrics_data_label_room_descObj_autoReplyWorkingTime': '自動応答対応時間',
    'admin_callcenter_metrics_data_label_room_descObj_operatorWorkingTime': 'オペレーター対応時間',
    'admin_callcenter_metrics_data_label_room_descObj_lastMsgTime': '最終発言時刻',
    'admin_callcenter_metrics_data_label_room_extra': '問い合わせ状態',
    'admin_callcenter_metrics_data_label_room_id': 'ルームID',
    'admin_callcenter_metrics_data_label_room_name': 'ルーム名',
    'admin_callcenter_metrics_data_label_room_tags_1': 'オペレーターID',
    'admin_callcenter_metrics_data_label_room_tags_4': 'ルーム終了日時',
    'admin_callcenter_metrics_data_label_room_tags_5': 'ルームが終了した理由',
    'admin_callcenter_metrics_data_label_room_tags_7': 'オペレーターメモ',
    'admin_callcenter_metrics_data_label_room_tags_8': '問い合わせ元情報',
    'admin_callcenter_metrics_data_label_room_tags_9': 'ルームの自動応答状態',
    'admin_callcenter_metrics_data_label_room_tags_10': 'ゲスト評価',
    'admin_callcenter_metrics_data_label_room_tags_11': 'ゲストコメント',
    'admin_callcenter_metrics_data_label_room_tags_12': 'オペレーター対応開始日時',
    'admin_callcenter_metrics_data_label_room_tags_13': '最初にオペレーターが対応した日時',
    'admin_callcenter_metrics_data_label_room_tags_14': '最初に担当したオペレーターID',
    'admin_callcenter_metrics_data_label_room_tags_15': '最初のメッセージの送信日時',
    'admin_callcenter_metrics_data_label_room_tags_16': '最初にメッセージを送信したオペレーターID',
    'admin_callcenter_metrics_data_label_room_tags_17': '保留フラグ',
    'admin_callcenter_metrics_data_label_room_tags_18': '保留回数',
    'admin_callcenter_metrics_data_label_room_tags_19': '保留開始日時',
    'admin_callcenter_metrics_data_label_room_tags_20': '合計の保留時間',
    'admin_callcenter_metrics_data_label_room_tags_21': '問い合わせ元情報詳細',
    'admin_callcenter_metrics_data_label_room_tags_22': '監視キーワードを含めたメッセージID',
    'admin_callcenter_metrics_data_label_room_tags_23': '一時保存情報',
    'admin_callcenter_metrics_data_label_room_tags_24': 'グループID',
    'admin_callcenter_metrics_data_label_room_tags_25': 'ルームが完了した日時',
    'admin_callcenter_metrics_data_label_room_tags_26': '外部連携ID',
    'admin_callcenter_metrics_data_label_room_tags_27': '担当オペレーターID履歴',
    'admin_callcenter_metrics_data_label_room_tags_28': '保留終了時刻',
    'admin_callcenter_metrics_data_label_room_tags_29': 'ルーム完了状態',
    'admin_callcenter_metrics_data_label_room_tags_30': 'PQPA',
    'admin_callcenter_metrics_data_label_room_tags_61': '自動応答モジュール名',
    'admin_callcenter_metrics_data_label_room_tags_62': '流入元ゲストユーザー種別',
    'admin_callcenter_metrics_data_label_room_tags_63': 'メッセージ更新状況',
    'admin_callcenter_metrics_data_label_room_tags_64': '自動翻訳設定',
    'admin_callcenter_metrics_data_label_room_tags_65': '流入元詳細情報',

    'admin_callcenter_metrics_data_label_room_tag21Obj_jsVersion': 'バージョン',
    'admin_callcenter_metrics_data_label_room_tag21Obj_language': '言語',
    'admin_callcenter_metrics_data_label_room_tag21Obj_language_forCsvHeader': '言語設定（小窓のみ）', // MBAJ-482
    'admin_callcenter_metrics_data_label_room_tag21Obj_platform': 'プラットフォーム',
    'admin_callcenter_metrics_data_label_room_tag21Obj_platform_forCsvHeader': 'プラットフォーム（小窓のみ）', // MBAJ-482
    'admin_callcenter_metrics_data_label_room_tag21Obj_referer': 'リファラ',
    'admin_callcenter_metrics_data_label_room_tag21Obj_referer_forCsvHeader': 'リファラ（小窓のみ）', // MBAJ-482
    'admin_callcenter_metrics_data_label_room_tag21Obj_timeZone': 'タイムゾーン',
    'admin_callcenter_metrics_data_label_room_tag21Obj_timeZone_forCsvHeader': 'タイムゾーン（小窓のみ）', // MBAJ-482
    'admin_callcenter_metrics_data_label_room_tag21Obj_title': 'タイトル',
    'admin_callcenter_metrics_data_label_room_tag21Obj_userAgent': 'ユーザーエージェント',
    'admin_callcenter_metrics_data_label_room_tag21Obj_userAgent_forCsvHeader': 'ユーザーエージェント（小窓のみ）',
    'admin_callcenter_metrics_data_label_room_tag86Obj_escalation_count': 'エスカレーション回数',
    'admin_callcenter_metrics_data_label_room_tag92Obj_salesforceContactId': '顧客識別ID',
    'admin_callcenter_metrics_data_label_room_tag92Obj_salesforceDisplayName': '顧客識別名称',
    'admin_callcenter_metrics_data_label_room_world': 'ゲストユーザーID',
    'admin_callcenter_metrics_dialog_title_display_items': '表示項目',
    'admin_callcenter_metrics_dialog_title_report_type': '表示レコード',
    'admin_callcenter_metrics_dialog_title': '表示オプション',
    'admin_callcenter_metrics_dialog_title_pattern': '出力パターン',
    'admin_callcenter_metrics_dialog_sub_pattern_new': '新パターン',
    'admin_callcenter_metrics_dialog_sub_pattern_old': '旧パターン',
    'admin_callcenter_metrics_export_label_categoryExport': '分類区分別件数出力',
    'admin_callcenter_metrics_export_label_export_failed': 'CSV出力に失敗しました',
    'admin_callcenter_metrics_export_label_llmSummaryExport': '対話内容要約情報出力',
    'admin_callcenter_metrics_export_label_metricExport': '問合せ統計情報出力',
    'admin_callcenter_metrics_export_label_msgExport': 'メッセージ/ルーム情報出力',
    'admin_callcenter_metrics_export_page_title': 'データ出力',
    'admin_callcenter_metrics_form_title': '問合せの統計情報を表示します',
    'admin_callcenter_metrics_graph_title_category': '問合せ種別',
    'admin_callcenter_metrics_graph_title_closeReason': 'クローズ理由',
    'admin_callcenter_metrics_graph_title_count': '件数',
    'admin_callcenter_metrics_graph_title_finishReason': 'オペレーターメモ',
    'admin_callcenter_metrics_graph_title_time': '処理時間',
    'admin_callcenter_metrics_graph_title_urgencyLevel': '緊急度',
    'admin_callcenter_metrics_label_active_guest_uniq_count': '問い合わせをしたことのあるユーザー数',
    'admin_callcenter_metrics_label_active_operator_count_min': '対応中オペレーター数 (Min)',
    'admin_callcenter_metrics_label_active_operator_count_max': '対応中オペレーター数 (Max)',
    'admin_callcenter_metrics_label_active_operator_count_avg': '対応中オペレーター数 (Avg)',
    'admin_callcenter_metrics_label_active_operator_uniq_count': '対応したことのあるオペレーター数',
    'admin_callcenter_metrics_label_autoReply_working_time_avg': '自動応答の対応時間 (Avg)',
    'admin_callcenter_metrics_label_autoReply_working_time_max': '自動応答の対応時間 (Max)',
    'admin_callcenter_metrics_label_autoReply_working_time_min': '自動応答の対応時間 (Min)',
    'admin_callcenter_metrics_label_autoReply_working_count': '自動応答の対応回数',
    'admin_callcenter_metrics_label_config_room_limit_min': 'オペレーター対応可能問合せ数(Min)',
    'admin_callcenter_metrics_label_config_room_limit_max': 'オペレーター対応可能問合せ数(Max)',
    'admin_callcenter_metrics_label_config_room_limit_avg': 'オペレーター対応可能問合せ数(Avg)',
    'admin_callcenter_metrics_label_count_roomPub':'問合せ',
    'admin_callcenter_metrics_label_count_total': '合計',
    'admin_callcenter_metrics_label_login_time': '稼働時間(h.)',
    'admin_callcenter_metrics_label_occupancy_rate': '稼働率',
    'admin_callcenter_metrics_label_operator_assigned_count': '担当ルーム数',
    'admin_callcenter_metrics_label_operator_assignee_count': '転送回数',
    'admin_callcenter_metrics_label_operator_response_count': '応答回数',
    'admin_callcenter_metrics_label_operator_response_time_avg': '応答時間(Avg)',
    'admin_callcenter_metrics_label_operator_response_time_max': '応答時間(Max)',
    'admin_callcenter_metrics_label_operator_response_time_min': '応答時間(Min)',
    'admin_callcenter_metrics_label_operator_transfer_rate': '転送率',
    'admin_callcenter_metrics_label_operator_working_time_avg': 'オペレーターの対応時間 (Avg)',
    'admin_callcenter_metrics_label_operator_working_time_max': 'オペレーターの対応時間 (Max)',
    'admin_callcenter_metrics_label_operator_working_time_min': 'オペレーターの対応時間 (Min)',
    'admin_callcenter_metrics_label_operator_working_count': 'オペレーターの対応回数',
    'admin_callcenter_metrics_label_room_close_time_avg': '対応時間(Avg)',
    'admin_callcenter_metrics_label_room_close_time_except_hold_avg': '対応時間(保留除く)(Avg)',
    'admin_callcenter_metrics_label_room_close_time_except_hold_max': '対応時間(保留除く)(Max)',
    'admin_callcenter_metrics_label_room_close_time_except_hold_min': '対応時間(保留除く)(Min)',
    'admin_callcenter_metrics_label_room_close_time_max': '対応時間(Max)',
    'admin_callcenter_metrics_label_room_close_time_min': '対応時間(Min)',
    'admin_callcenter_metrics_label_inflow_source_all': '流入元（全て)',
    'admin_callcenter_metrics_label_inflow_source_line': '流入元(LINE)',
    'admin_callcenter_metrics_label_inflow_source_web': '流入元(Web)',
    'admin_callcenter_metrics_label_inflow_source_linecc': '流入元(LINE（Switcher API）)',
    'admin_callcenter_metrics_label_inflow_source_lineWorks': '流入元(LINE Works)',
    'admin_callcenter_metrics_label_inflow_source_facebook': '流入元(Facebook Messenger)',
    'admin_callcenter_metrics_label_inflow_source_msbot': '流入元（Microsoft Bot Framework)',
    'admin_callcenter_metrics_label_inflow_source_mobiGuest': '流入元(オリジナルクライアントアプリ)',
    'admin_callcenter_metrics_label_csv_items': 'csvの担当者/期間/時間帯データの出力項目に差異があります。',
    'admin_callcenter_metrics_label_new_pattern': '新パターン：別項目として出力',
    'admin_callcenter_metrics_label_old_pattern': '旧パターン：同一項目として出力',
    'admin_callcenter_metrics_label_used_old_pattern': '*旧パターンを利用したい場合、一度手動で旧パターンを選択してください。設定変更は保持されます。',
    'admin_callcenter_metrics_label_room_finish_count': '完了数',
    'admin_callcenter_metrics_label_room_finish_time_avg': '完了時間(Avg)',
    'admin_callcenter_metrics_label_room_finish_time_max': '完了時間(Max)',
    'admin_callcenter_metrics_label_room_finish_time_min': '完了時間(Min)',
    'admin_callcenter_metrics_label_room_first_accept_count': '初回対応数',
    'admin_callcenter_metrics_label_room_first_accept_time_avg': '初回対応時間(Avg)',
    'admin_callcenter_metrics_label_room_first_accept_time_max': '初回対応時間(Max)',
    'admin_callcenter_metrics_label_room_first_accept_time_min': '初回対応時間(Min)',
    'admin_callcenter_metrics_label_room_first_message_count': '初回発言数',
    'admin_callcenter_metrics_label_room_first_message_time_avg': '初回発言時間(Avg)',
    'admin_callcenter_metrics_label_room_first_message_time_max': '初回発言時間(Max)',
    'admin_callcenter_metrics_label_room_first_message_time_min': '初回発言時間(Min)',
    'admin_callcenter_metrics_label_room_full_load_count' : '対応可能上限に達した回数',
    'admin_callcenter_metrics_label_room_full_load_duration' : '対応可能上限に達していた期間 (ms)',
    'admin_callcenter_metrics_label_room_hold_count_avg' : '1ルームの保留回数(Avg)',
    'admin_callcenter_metrics_label_room_hold_count_max' : '1ルームの保留回数(Max)',
    'admin_callcenter_metrics_label_room_hold_count_min' : '1ルームの保留回数(Min)',
    'admin_callcenter_metrics_label_room_hold_count' : '保留回数',
    'admin_callcenter_metrics_label_room_hold_time_avg' : '1ルームの保留累計時間(Avg)',
    'admin_callcenter_metrics_label_room_hold_time_max' : '1ルームの保留累計時間(Max)',
    'admin_callcenter_metrics_label_room_hold_time_min' : '1ルームの保留累計時間(Min)',
    'admin_callcenter_metrics_label_room_load_min': '対応中ルーム数(担当者がいないルームを含む)(Min)',
    'admin_callcenter_metrics_label_room_load_max': '対応中ルーム数(担当者がいないルームを含む)(Max)',
    'admin_callcenter_metrics_label_room_load_avg': '対応中ルーム数(担当者がいないルームを含む)(Avg)',
    'admin_callcenter_metrics_label_room_load2_min': '対応中ルーム数(担当者がついているルームのみ)(Min)',
    'admin_callcenter_metrics_label_room_load2_max': '対応中ルーム数(担当者がついているルームのみ)(Max)',
    'admin_callcenter_metrics_label_room_load2_avg': '対応中ルーム数(担当者がついているルームのみ)(Avg)',
    'admin_callcenter_metrics_label_room_reject_count': '対応できなかった回数',
    'admin_callcenter_metrics_label_tag29_count_00': 'オペレーター初期状態',
    'admin_callcenter_metrics_label_tag29_count_01': 'オペレーター通常完了',
    'admin_callcenter_metrics_label_tag29_count_02': 'ユーザー無応答',
    'admin_callcenter_metrics_label_tag29_count_03': '対応未完了',
    'admin_callcenter_metrics_label_tag29_count_04': '後処理未完了',
    'admin_callcenter_metrics_label_tag29_count_05': '未対応（あふれ）',
    'admin_callcenter_metrics_label_tag29_count_10': '自動応答初期状態',
    'admin_callcenter_metrics_label_tag29_count_11': '自動応答通常完了',
    'admin_callcenter_metrics_label_tag29_count_12': '自動応答でタイムアウト完了\n ',
    'admin_callcenter_metrics_label_tag29_count_13': '自動応答でタイムアウト\n(オペレーター対応数超過)',
    'admin_callcenter_metrics_label_tag29_count_14': '自動応答でタイムアウト\n(時間外)',
    'admin_callcenter_metrics_label_tag29_count_15': '自動応答でタイムアウト\n(休日)',
    'admin_callcenter_metrics_label_tag29_count_16': '自動応答でタイムアウト\n(その他理由)',
    'admin_callcenter_metrics_label_time_avg': '平均',
    'admin_callcenter_metrics_label_time_max': '最大',
    'admin_callcenter_metrics_label_time_min': '最小',
    'admin_callcenter_openRooms_page_title': '対応中ルーム検索',
    'admin_callcenter_option_desc_openHours1': '曜日毎に営業時間を設定してください。日を跨いで営業する場合は、00:00 ~ 04:00, 22:00 ~ 23:59のように設定してください。日を跨いで営業しない場合は対応開始終了時刻1のみセットし、対応開始終了時刻2には00:00をセットしてください。',
    'admin_callcenter_option_desc_openHours2': '終日休業する場合は開始と終了に00:00をセットしてください。',
    'admin_callcenter_option_hintText_maxInteractionAdmin': '管理者権限のユーザーが同時に担当できるopen状態の有人対応ルーム数',
    'admin_callcenter_option_hintText_maxInteractionSv': 'スーパーバイザー権限のユーザーが同時に担当できるopen状態の有人対応ルーム数',
    'admin_callcenter_option_hintText_maxInteractionOpe': '一般オペレーター権限のユーザーが同時に担当できるopen状態の有人対応ルーム数',
    'admin_callcenter_option_hintText_enableUserInteraction': '有効にしている場合、ユーザー詳細でユーザー単位の「1オペレーター同時対応上限」が設定できます。',
    'admin_callcenter_option_hintText_escalationEnable': '「別オペレーターの対応中ルームを非表示」を有効にしている場合、本機能を有効にできません。',
    'admin_callcenter_option_hintText_leaveOtherOperatorsRoom': '「エスカレーションを有効にする」を有効にしている場合、本機能を有効にできません。',
    'admin_callcenter_option_hintText_enableInternalChatRoom': '有人対応開始時に、オペレーター間でチャットができる内部チャット機能が利用できます',
    'admin_callcenter_option_hintText_operatorQuickMemo': 'ナビゲーション内にゲスト名とメモ情報の入力エリアを表示します\nオペレーターが対応中の自分用メモとして利用できます',
    'admin_callcenter_option_hintText_enableRoomColor': '「私の担当一覧」にあるルームリストとルーム画面を色別に表示します',   // MBA-15176
    'admin_callcenter_option_label_addCode': '追加',
    'admin_callcenter_option_label_all_about_room': 'キーワード検索',
    'admin_callcenter_option_label_allowOperatorAbandonRoom': 'オペレーターがルーム担当者を未指定で転送することを許可する',
    'admin_callcenter_option_label_allowOperatorAssignGroup': 'オペレーターによるグループ指定を許可する',
    'admin_callcenter_option_label_attachFileByGuest': 'ゲストによるファイル添付を許可する',
    'admin_callcenter_option_label_category_body': 'ラベル',
    'admin_callcenter_option_label_category_code': 'コード',
    'admin_callcenter_option_label_category_label': 'ラベル',
    'admin_callcenter_option_label_category_type_selectMulti': '複数選択',
    'admin_callcenter_option_label_category_type_selectOne': '選択',
    'admin_callcenter_option_label_category_type_text': '自由入力',
    'admin_callcenter_option_label_category_type': 'タイプ',
    'admin_callcenter_option_label_copyAsCSV': 'CSV形式でクリップボードに保存する',
    'admin_callcenter_option_label_copyMessageDetail': 'メッセージ詳細情報のコピー',
    'admin_callcenter_option_label_ed_time': '対応終了時刻',
    'admin_callcenter_option_label_enableRoomHold': 'ルーム保留機能',
    'admin_callcenter_option_label_enableSendImage': '画像送信',
    'admin_callcenter_option_label_enableSendLINEStamp': 'LINEスタンプ送信',
    'admin_callcenter_option_label_enableSuggestSnippet': '定型文による入力補完機能',
    'admin_callcenter_option_label_enableSwitchIconNickname': 'アイコンニックネームの切替',
    'admin_callcenter_option_label_forceRefreshForeignUserId': '外部連携IDの常時問い合わせ',
    'admin_callcenter_option_label_holidays': '休業日',
    'admin_callcenter_option_label_leaveOtherOperatorsRoom': '別オペレーターの対応中ルームを非表示',
    'admin_callcenter_option_label_markMessage': 'メッセージ削除予約',
    'admin_callcenter_option_label_maxInteraction': '1オペレーター同時対応上限<br />(デフォルト)',
    'admin_callcenter_option_label_maxInteractionAdmin': '1オペレーター同時対応上限<br />(管理者権限)',
    'admin_callcenter_option_label_maxInteractionOpe': '1オペレーター同時対応上限<br />(一般オペレーター)',
    'admin_callcenter_option_label_maxInteractionSv': '1オペレーター同時対応上限<br />(スーパーバイザー)',
    'admin_callcenter_option_label_enableUserInteraction': '1オペレーター同時対応上限(ユーザー別)を有効にする',
    'admin_callcenter_option_label_replyRemindTimer': '返信アラーム点灯(オレンジ)までの時間(分)',
    'admin_callcenter_option_hintText_replyRemindTimer': '設定可能範囲は、1～10,079（7日未満）です',
    'admin_callcenter_option_label_monitorAllGroup': 'グループ別稼働状況の表示',
    'admin_callcenter_option_label_openHours_Fri': '営業時間 (金)',
    'admin_callcenter_option_label_openHours_Mon': '営業時間 (月)',
    'admin_callcenter_option_label_openHours_Sat': '営業時間 (土)',
    'admin_callcenter_option_label_openHours_Sun': '営業時間 (日)',
    'admin_callcenter_option_label_openHours_Thu': '営業時間 (木)',
    'admin_callcenter_option_label_openHours_Tue': '営業時間 (火)',
    'admin_callcenter_option_label_openHours_Wed': '営業時間 (水)',
    'admin_callcenter_option_label_removeCode': '削除',
    'admin_callcenter_option_label_st_time': '対応開始時刻',
    'admin_callcenter_option_label_tag10': 'ゲスト評価',
    'admin_callcenter_option_label_tag11': 'ゲストコメント',
    'admin_callcenter_option_label_tag2': '分類区分1',
    'admin_callcenter_option_label_tag29_00': 'オペレーター初期状態',
    'admin_callcenter_option_label_tag29_01': 'オペレーター通常完了',
    'admin_callcenter_option_label_tag29_02': 'ユーザーからの応答なし',
    'admin_callcenter_option_label_tag29_03': '対応未完了',
    'admin_callcenter_option_label_tag29_04': '後処理未完了',
    'admin_callcenter_option_label_tag29_05': '未対応（あふれ）',
    'admin_callcenter_option_label_tag29_10': '自動応答初期状態',
    'admin_callcenter_option_label_tag29_11': '自動応答通常完了',
    'admin_callcenter_option_label_tag29_12': '自動応答でタイムアウト完了',
    'admin_callcenter_option_label_tag29_13': '自動応答でタイムアウト (オペレーター対応数超過)',
    'admin_callcenter_option_label_tag29_14': '自動応答でタイムアウト (時間外)',
    'admin_callcenter_option_label_tag29_15': '自動応答でタイムアウト (休日)',
    'admin_callcenter_option_label_tag29_16': '自動応答でタイムアウト (その他理由)',
    'admin_callcenter_option_label_tag29': '完了ステータス',
    'admin_callcenter_option_label_tag3': '分類区分2',
    'admin_callcenter_option_label_tag6': '分類区分3',
    'admin_callcenter_option_label_tag7': 'オペレーターメモ',
    'admin_callcenter_option_label_tag8': '問合せ元情報',
    'admin_callcenter_option_label_tag22': '監視キーワードを含む',
    'admin_callcenter_option_label_tag75': '担当者 グループ(履歴)',
    'admin_callcenter_option_label_timeout': '問合せタイムアウト(秒)',
    'admin_callcenter_option_label_roomListDisplaySetting': '問い合わせ一覧表示設定',
    'admin_callcenter_option_label_roomListDisplaySetting_status': '状態',
    'admin_callcenter_option_label_roomListDisplaySetting_elapsedTime': '経過時間',
    'admin_callcenter_option_label_roomListDisplaySetting_holdTime': '保留時間',
    'admin_callcenter_option_label_roomListDisplaySetting_lastMessageTime': '最後の発言から',
    'admin_callcenter_option_label_roomListDisplaySetting_roomType': '流入元',
    'admin_callcenter_option_label_roomListDisplaySetting_issueOrigin': '問い合わせ元',
    'admin_callcenter_option_label_roomListDisplaySetting_roomName': 'ルーム名',
    'admin_callcenter_option_label_roomListDisplaySetting_startTime': '開始時刻',
    'admin_callcenter_option_label_roomListDisplaySetting_endTime': '終了時刻',
    'admin_callcenter_option_label_roomListDisplaySetting_personInCharge': '担当者',
    'admin_callcenter_option_label_roomListDisplaySetting_guestAlert': '要注意',
    'admin_callcenter_option_label_roomListDisplaySetting_process': '処理',
    'admin_callcenter_option_label_roomListDisplaySetting_lastOpeMessageTime': '最後のオペレータ発言から',
    'admin_callcenter_option_label_roomListDisplaySetting_lastGuestMessageTime': '最後のゲスト発言から',
    'admin_callcenter_option_label_roomListDisplaySetting_lastOpeResponseTime': 'OPレスポンス',
    'admin_callcenter_option_label_roomListDisplaySetting_lastOpeStandbyTime': 'OPスタンバイ',
    'admin_callcenter_option_label_roomListDisplaySetting_group': '担当グループ',
    'admin_callcenter_option_label_roomListDisplaySetting_unreadMessages': '未読数',
    'admin_callcenter_option_label_showGuestOtherAttributes': 'ゲストのその他ユーザー属性の表示',
    'admin_callcenter_option_label_enableInternalChatRoom': '内部チャットを有効にする',
    'admin_callcenter_option_label_raiseHand': 'エスカレーションを有効にする',
    'admin_callcenter_option_label_raiseHand_type_enable': '種別フラグを利用する',
    'admin_callcenter_option_label_raiseHand_type': 'エスカレーション種別フラグ設定',
    'common_label_escalation_trouble': 'トラブル',
    'common_label_escalation_complain': 'クレーム',
    'common_label_escalation_help': 'ケア必要',
    'common_label_escalation_advice': '相談',
    'common_label_escalation_check': '確認',
    'admin_callcenter_option_label_raiseHand_type_btn_initialize': '初期化',
    'admin_callcenter_option_label_raiseHand_listItems': 'エスカレーション一覧表示項目設定',
    'admin_callcenter_option_label_raiseHand_listItems_status': '状態',
    'admin_callcenter_option_label_raiseHand_listItems_elapsedTime': '経過時間',
    'admin_callcenter_option_label_raiseHand_listItems_deploymentTime': 'エスカレ経過時間',
    'admin_callcenter_option_label_raiseHand_listItems_address': '宛先',
    'admin_callcenter_option_label_raiseHand_listItems_group': '担当グループ',
    'admin_callcenter_option_label_raiseHand_listItems_roomName': 'ルーム名',
    'admin_callcenter_option_label_raiseHand_listItems_guestAlert': '要注意',
    'admin_callcenter_option_label_raiseHand_listItems_personInCharge': '担当者',
    'admin_callcenter_option_label_raiseHand_listItems_process': '処理',
    'admin_callcenter_option_label_raiseHand_message_enable': 'エスカレーション時メッセージ機能を利用する',
    'admin_callcenter_option_label_raiseHand_timeAlert': '時間超過アラートを有効にする',
    'admin_callcenter_option_label_raiseHand_timeAlert_groupSetting': '時間超過アラートをグループ毎に設定する',
    'admin_callcenter_option_label_raiseHand_timeAlert_minute': '時間超過アラート表示までの時間（分）',
    'admin_callcenter_option_label_raiseHand_timeAlert_minute_error' : '時間超過アラート表示までの時間を入力してください',
    'admin_callcenter_option_label_operatorQuickMemo': 'ルームメモを有効にする',
    'admin_callcenter_option_label_enableRoomColor': 'ルームの色分け機能を有効にする',     // MBA-15176
    'admin_callcenter_option_label_roomListTimeUnit': '問い合わせ一覧項目時間単位',
    'admin_callcenter_option_label_roomListTimeUnit_minute': '分',
    'admin_callcenter_option_label_roomListTimeUnit_second': '秒',
    'admin_callcenter_option_hintText_roomListTimeUnit': '経過時間、最後の発言から、最後のオペレータ発言から、最後のゲスト発言から、保留時間、OPレスポンス、OPスタンバイ',
    'admin_callcenter_period_specified_response_empty_msg': '期間指定応答が設定されていません。設定を完了すると期間指定応答「#{index}」をグループに割り当てることができるようになります。',
    'admin_callcenter_period_specified_response': '期間指定応答',
    'admin_callcenter_period_specified_response_setting': '期間指定応答設定',
    'admin_callcenter_period_specified_setting': '期間設定',
    'admin_callcenter_period_specified_setting_error_to_more_than_from': '期間設定の開始終了が不正です',
    'admin_callcenter_period_specified_setting_list_item_last_updated': '更新日時',
    'admin_callcenter_readSetting': '既読通知設定',
    'admin_callcenter_readSetting_guestMode': 'ゲスト側設定',
    'admin_callcenter_readSetting_guestMode_0': '既読通知しない',
    'admin_callcenter_readSetting_guestMode_1': '自動で行う',
    'admin_callcenter_readSetting_guestMode_hint': 'ゲストユーザーがオペレーターに対して既読を通知するタイミングを指定します',
    'admin_callcenter_readSetting_operatorMode': 'オペレーター側設定',
    'admin_callcenter_readSetting_operatorMode_0': '既読通知しない',
    'admin_callcenter_readSetting_operatorMode_1': '担当者がルームを見た時に自動で行う',
    'admin_callcenter_readSetting_operatorMode_2': '担当者が既読ボタンを押した時',
    'admin_callcenter_readSetting_operatorMode_hint': 'オペレーターがゲストに対して既読を通知するタイミングを指定します',
    'admin_callcenter_readSetting_channel_label': 'チャネル',
    'admin_callcenter_working_time_closeDaysSetting': '休日設定',
    'admin_callcenter_working_time_closeDays': '休日',
    'admin_callcenter_working_time_closeDays_empty_msg': '休日が設定されていません。設定を完了すると休日設定「#{index}」をグループに割り当てることができるようになります。',
    'admin_callcenter_working_time_closeDays_file_label': 'ファイル選択',
    'admin_callcenter_working_time_closeDays_label_format': 'ファイル形式',
    'admin_callcenter_working_time_closeDays_label_sjis_csv': 'Shift_JIS形式CSV',
    'admin_callcenter_working_time_usedGroup': 'この設定を使用中のグループ',
    'admin_callcenter_working_time_setting': '営業時間設定',
    'admin_callcenter_working_time_setting_reference': '営業時間設定はこちら',
    'admin_callcenter_working_time_specialOpenDays_empty_msg': '特別な営業日時が設定されていません。設定を完了すると特別な営業日時設定「#{index}」をグループに割り当てることができるようになります。',
    'admin_callcenter_working_time_specialOpenDaysSetting': '特別な営業日時設定',
    'admin_callcenter_working_time_specialOpenDays': '特別な営業日時',
    'admin_callcenter_working_time': '開始終了時刻',
    'admin_callcenter_working_time_hours_empty_msg': '営業時間が設定されていません。設定を完了すると営業時間「#{index}」をグループに割り当てることができるようになります。',
    'admin_callcenter_working_time_endtime_earlier_than_starttime': '終了時刻を開始時刻より以前に設定することはできません。',
    'admin_callcenter_working_time_reset_msg': '休日設定を削除します。\n削除するとこの登録されているグループの休日設定はデフォルトに変更されます。',
    'admin_callcenter_working_time_reset_btn': 'リセット',

    'admin_callcenter_working_time_total': 'オペレーター対応可能問い合わせ数',
    'admin_callcenter_working_time_perUser': '1オペレーターの対応可能問い合わせ数',
    'admin_callcenter_working_time_total_group': 'グループ最大対応可能数',
    'admin_callcenter_working_time_Total_PerUser_hint_text_workingTime1': '空欄の場合、対応モードの設定値に従います',
    'admin_callcenter_working_time_Total_PerUser_hint_text_workingTime2': 'ただし、グループ設定している場合は該当グループの詳細の設定値に従います',
    'admin_callcenter_working_time_Total_PerUser_hint_text_specialOpenDay1': '空欄の場合、対応モードの設定値に従います',
    'admin_callcenter_working_time_Total_PerUser_hint_text_specialOpenDay2': 'ただし、グループ設定している場合は該当グループの詳細の設定値に従います',
    'admin_callcenter_working_time_Total_PerUser_hint_url_text': '設定はこちら',
    'admin_callcenter_working_time_Total_PerUser_group_hint_text': '空欄の場合、該当グループの詳細の設定値に従います',
    'admin_callcenter_working_time_Total_PerUser_group_hint_url_text': '設定はこちら',

    'admin_closed_room_management_management_title': '完了ルーム検索',
    'admin_customize_favicon_hint_default': 'デフォルト',
    'admin_customize_favicon_hint_mobiadmin': '/web/adminに反映されます',
    'admin_customize_favicon_hint_mobiagent': '/web/agentに反映されます',
    'admin_customize_favicon_hint_mobidashboard': '/web/admin/dashboardに反映されます',
    'admin_customize_favicon_hint_mobiwork': '/web/workに反映されます',
    'admin_customize_favicon_hint_mobistat': '/web/admin/statに反映されます',
    'admin_domain_account_lock_count': '最大ログイン失敗回数',
    'admin_domain_add_new_management': 'ドメイン新規作成',
    'admin_domain_attribute_accountLockCount': 'パスワード間違い回数によるアカウントロック',
    'admin_domain_attribute_accountUnlockAtResetPasswd': 'パスワードリセットによりアカウントロックを解除',
    'admin_domain_attribute_clearFieldsOnMsgDel': 'メッセージ削除時にフィールドをクリアする',
    'admin_domain_attribute_fileSizeLimit': 'ファイルのサイズ制限',
    'admin_domain_attribute_fileSizeLimitWarnPercent': 'ファイルサイズ超過判定しきい値',
    'admin_domain_attribute_greetingMessage': '挨拶メッセージ',
    'admin_domain_attribute_name': 'ドメイン名',
    'admin_domain_attribute_passwdExpireDay': 'パスワード有効日数',
    'admin_domain_attribute_passwordStrength': 'パスワードの強度',
    'admin_domain_attribute_removeOldChatMsgsbySeconds': '古いメッセージの削除',
    'admin_domain_attribute_removeOldExtraRoomTagsBySeconds': '古い拡張ルームタグの削除',

    'admin_domain_attribute_samePasswdProhibitCount': '同じパスワードの使いまわし禁止回数',
    'admin_domain_attribute_sysParam_BotEnable': 'Bot オプション',
    'admin_domain_attribute_sysParam_ExternalUserIdTable': '外部連携ID変換テーブル オプション',
    // TODO: Facebook連携機能の非表示（フェーズ1対応）v1.50.0 で削除
    // 'admin_domain_attribute_sysParam_FacebookEnable': 'Facebook連携 オプション',
    'admin_domain_attribute_sysParam_BedoreWebEnable': 'BEDORE for WEB連携 オプション',
    'admin_domain_attribute_sysParam_GroupingEnable': 'グループ分け機能 オプション',
    'admin_domain_attribute_sysParam_HiddenAccountFlowComplete': 'アカウント登録処理完了',
    'admin_domain_attribute_sysParam_HiddenAccountMemo': 'アカウントメモ',
    'admin_domain_attribute_sysParam_HiddenAccountPartner': '担当者',
    'admin_domain_attribute_sysParam_HiddenCreatedAt': '作成日時',
    'admin_domain_attribute_sysParam_KnowledgeEnable': 'Knowledge オプション',
    'admin_domain_attribute_sysParam_LINECCEnable': 'LINE公式アカウント（Switcher API利用あり）オプション',
    'admin_domain_attribute_sysParam_LINEEnable': 'LINE連携 オプション',
    'admin_domain_attribute_sysParam_LINEWorksEnable': 'LINEWorks連携 オプション',
    'admin_domain_attribute_sysParam_MediaCaptureEnable': 'テレワーク機能 オプション',
    'admin_domain_attribute_sysParam_MobiAgentEnable': 'モビエージェントを有効にする'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'admin_domain_attribute_sysParam_MobiBotEnable': 'モビボットを有効にする'.replace('モビボット', window.KonnectPlusServerJSON.service.mobibot || 'モビボット'),
    'admin_domain_attribute_sysParam_MobiCastEnable': 'モビキャストを有効にする'.replace('モビキャスト', window.KonnectPlusServerJSON.service.mobicast || 'モビキャスト'),
    'admin_domain_attribute_sysParam_MobiConsoleEnable': 'モビコンソールを有効にする'.replace('モビコンソール', window.KonnectPlusServerJSON.service.mobiconsole || 'モビコンソール'),
    'admin_domain_attribute_sysParam_MobiGuestEnable': '#{mobiguest}連携 オプション'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'admin_domain_attribute_sysParam_MobiWorkEnable': 'モビワークを有効にする'.replace('モビワーク', window.KonnectPlusServerJSON.service.mobiwork || 'モビワーク'),
    'admin_domain_attribute_sysParam_MsBotEnable': 'Microsoft Bot Framework連携 オプション',
    'admin_domain_attribute_sysParam_PQPAEnable': 'PQPA(主質問/主回答管理機能) オプション',
    'admin_domain_attribute_sysParam_PrivacyMaskingEnable': '個人情報検出アラート オプション',
    'admin_domain_attribute_sysParam_TextToSpeechEnable': '音声読み上げ機能 オプション',
    'admin_domain_attribute_sysParam_TranslateEnable': '翻訳機能オプション',
    'admin_domain_attribute_sysParam_SecurePathEnable': 'Secure Path連携 オプション',
    'admin_domain_attribute_sysParam_EscalationManualEnable': 'エスカレーションを有効にする',
    'admin_domain_attribute_sysParam_TraceAlbLogEnable': 'ポート番号照会を有効にする',
    'admin_domain_attribute_sysParam_MfaEnable': '多要素認証機能を有効にする',
    'admin_domain_attribute_sysParam_GuestMfaEnable': 'ゲスト向け多要素認証機能を有効にする',
    'admin_domain_attribute_sysParam_LlmSummaryEnable': '対話内容要約機能を有効にする',
    'admin_domain_attribute_sysParam_ChatwindowEnable': 'Web小窓v2を有効にする',
    'admin_domain_attribute_sysParam_CrmConnectSfEnable': 'CRM Connect を有効にする',
    'admin_domain_attribute_sysParam_PrivacyMaskingAutoDeleteEnable': '個人情報自動削除予約 オプション',
    'admin_domain_attribute_sysParam_PrivacyMaskingPolicyEnable': '個人情報送信制御オプション',
    'admin_domain_attribute_sysParam_SentimentAnalysisEnable': '感情分析オプション',
    'admin_domain_attribute_sysParam_EmbedAutoReloadEnable': 'Web小窓自動リロード処理を有効にする',
    'admin_domain_attribute_sysParam_ForceAssignmentEnable': 'ルーム担当者割り当てオプション',
    'admin_domain_attribute_userDefaultExposeToDomainIds': '連携ドメインID',
    'admin_domain_attribute_userDefaultFileSizeLimit': 'ユーザー毎ファイルサイズ制限',
    'admin_domain_attribute_userLimit': 'ユーザー数上限',
    'admin_domain_attribute_weakPasswdCheckCount': 'パスワード脆弱性チェック回数',
    'admin_domain_attribute_weakPasswdCheckDay': 'パスワード脆弱性チェック期間',
    'admin_domain_config_title': 'ドメイン設定',
    'admin_domain_create_error': '追加に失敗しました',
    'admin_domain_create_successful': '追加に成功しました',

    'admin_reset_successful': '初期化に成功しました',

    'admin_domain_detail': '詳細情報',
    'admin_domain_dialog_alert_user_suspend': '一時停止',
    'admin_domain_dialog_create_domain_alert_title': 'ドメイン作成確認',
    'admin_domain_dialog_create_new_user': '#{domainId}の新規ユーザーを作成してもよろしいですか？',
    'admin_domain_dialog_create_setting_alert': '新規ドメインを作成してもよろしいですか？',
    'admin_domain_dialog_create_user_alert_title': 'ユーザー作成確認',
    'admin_domain_dialog_delete_user_alert_title': 'ユーザー削除確認',
    'admin_domain_dialog_delete_user_alert': '#{userId}を削除してもよろしいですか？',
    'admin_domain_dialog_reset_password_alert_title': 'パスワード再設定確認',
    'admin_domain_dialog_reset_password_alert': '#{userId}のパスワードをリセットしてもよろしいですか？',
    'admin_domain_dialog_suspend_user_alert_title': '一時停止確認',
    'admin_domain_dialog_update_domain_alert_title': 'ドメイン設定確認',
    'admin_domain_dialog_update_setting_alert': 'ドメイン設定を保存してもよろしいですか？',
    'admin_domain_dialog_update_user_alert_title': 'ユーザー更新確認',
    'admin_domain_error_invalid_account_lock_count': '最大ログイン失敗回数を入力してください',
    'admin_domain_error_invalid_filesize_limit': 'ファイルサイズの上限を入力して下さい',
    'admin_domain_error_invalid_length': 'ドメインIDの文字数は４字以上３４字以下です',
    'admin_domain_error_invalid_name_length': 'ドメイン名の文字数は100字以下です',
    'admin_domain_error_invalid_name_required': 'ドメイン名は必須項目です',
    'admin_domain_error_invalid_user_limit': 'ユーザー数を入力してください',
    'admin_domain_greeting_message': '挨拶メッセージ',
    'admin_domain_history_label_autoReplyModule': '優先自動応答モジュール',
    'admin_domain_history_label_botEnable': 'Bot機能',
    'admin_domain_history_label_facebookEnable': 'Facebook連携',
    'admin_domain_history_label_bedoreWebEnable': 'BEDORE for WEB連携',
    'admin_domain_history_label_groupingEnable': 'グループ分け機能',
    'admin_domain_history_label_knowledgeEnable': 'Knowledge機能',
    'admin_domain_history_label_lineccEnable': 'LINECC連携',
    'admin_domain_history_label_lineEnable': 'LINE連携',
    'admin_domain_history_label_lineworksEnable': 'LINEWorks連携',
    'admin_domain_history_label_maxFileSizeLimit': '利用可能ストレージ',
    'admin_domain_history_label_maxUserLimit': '利用可能アカウント数',
    'admin_domain_history_label_mobiagentEnable': 'モビエージェント'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'admin_domain_history_label_mobibotEnable': 'モビボット'.replace('モビボット', window.KonnectPlusServerJSON.service.mobibot || 'モビボット'),
    'admin_domain_history_label_mobiGuestEnable': '#{mobiguest}連携'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'admin_domain_history_label_mobicastEnable': 'モビキャスト'.replace('モビキャスト', window.KonnectPlusServerJSON.service.mobicast || 'モビキャスト'),
    'admin_domain_history_label_mobiconsoleEnable': 'モビコンソール'.replace('モビコンソール', window.KonnectPlusServerJSON.service.mobiconsole || 'モビコンソール'),
    'admin_domain_history_label_mobiworkEnable': 'モビワーク'.replace('モビワーク', window.KonnectPlusServerJSON.service.mobiwork || 'モビワーク'),
    'admin_domain_history_label_msBotEnable': 'Microsoft Bot Framework連携',
    'admin_domain_history_label_msg11': '1-1 ルーム',
    'admin_domain_history_label_msg1n': '1-N ルーム',
    'admin_domain_history_label_msgOperatorBotTotal': '送信通数合計（オペレーター＋自動応答）：',
    'admin_domain_history_label_msgPub': '問合せルーム',
    'admin_domain_history_label_msgPubAll': 'All',
    'admin_domain_history_label_msgPubBot': '自動応答',
    'admin_domain_history_label_msgPubContent': 'コンテンツ配信',
    'admin_domain_history_label_msgPubFacebook': 'Facebook Messenger',
    'admin_domain_history_label_msgPubBedoreWeb': 'BEDORE for WEB',
    'admin_domain_history_label_msgPubGuest': 'ゲストユーザー',
    'admin_domain_history_label_msgPubLine': 'LINE BC',
    'admin_domain_history_label_msgPubLine2': 'LINE',
    'admin_domain_history_label_msgPubLinecc': 'LINE CC',
    'admin_domain_history_label_msgPubLineworks': 'LINE Works',
    'admin_domain_history_label_msgPubMisc': 'Web小窓',
    'admin_domain_history_label_msgPubMobiGuest': '#{mobiguest}'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'admin_domain_history_label_msgPubMsBot': 'Microsoft Bot Framework',
    'admin_domain_history_label_msgPubOpe': 'オペレーター',
    'admin_domain_history_label_msgTotal': '合計',
    'admin_domain_history_label_roomTag26Update': '外部連携IDをルームの属性として保存',
    'admin_domain_history_label_translateEnable': '翻訳機能',
    'admin_domain_history_table_title_1': 'ご契約プラン',
    'admin_domain_history_table_title_2': 'メッセージ送信通数',
    'admin_domain_history_title': 'ドメイン利用実績を表示します',
    'admin_domain_id': 'ドメインID',
    'admin_domain_management_title': 'ドメイン管理',
    'admin_domain_name': 'ドメイン名',
    'admin_domain_overwrite_form_title': 'ドメイン運用を行っていない場合は、ドメインIDは入力せずに実行ボタンを押してください',
    'admin_domain_overwrite_page_title': 'ドメイン管理者としてドメイン毎の管理を行います',
    'admin_domain_requestLimit_form_desc_api': '値が設定されていない場合はdefault_apiの値が使用されます\nドメイン別APIキーを利用しての外部からの呼び出しは、ドメイン別APIキー認証利用時の値が適用されます',
    'admin_domain_requestLimit_form_desc_apiKeyEnable': 'CRMシステム連携など外部からAPIを呼び出す場合はウェブ/スマホアプリとのリクエストを区別するためドメイン別APIキー認証を有効にする必要があります',
    'admin_domain_requestLimit_form_desc_apiNoNeedAuth': '個別の値が設定されていない場合はdefault_apiの値が使用されます',
    'admin_domain_requestLimit_form_desc_default': '予期せぬ制限実施を回避するため、十分大きめの値推奨',
    'admin_domain_requestLimit_form_desc_limit': '各APIの利用可能回数 = 各API別の制限数(未設定の場合はシステム共通の設定値) * ドメイン別係数1 * ドメイン別利用可能アカウント数 となります\n判定は5分毎、1日毎それぞれで行われます',
    'admin_domain_requestLimit_form_desc_websocket': '個別の値が設定されていない場合はdefault_wsの値が使用されます',
    'admin_domain_requestLimit_form_floatingLabel_': '',
    'admin_domain_requestLimit_form_floatingLabel_api_': '通常認証利用時',
    'admin_domain_requestLimit_form_floatingLabel_apiNoNeedAuth_': '',
    'admin_domain_requestLimit_form_floatingLabel_extApi_': 'ドメイン別APIキー認証利用時',
    'admin_domain_requestLimit_form_floatingLabel_websocket_': '通常認証利用時',
    'admin_domain_requestLimit_form_title_api': 'API呼び出しの制限',
    'admin_domain_requestLimit_form_title_apiKeyEnable': 'ドメイン別APIキー認証',
    'admin_domain_requestLimit_form_title_apiNoNeedAuth': '認証不要API呼び出しの制限',
    'admin_domain_requestLimit_form_title_default': 'デフォルト設定',
    'admin_domain_requestLimit_form_title_limit': '制限値設定',
    'admin_domain_requestLimit_form_title_websocket': 'WebSocketのリクエスト呼び出し制限',
    'admin_domain_requestLimit_hint_apiKeyEnable': 'ドメイン別APIキーによる認証が利用可能になります',
    'admin_domain_requestLimit_hint_requestLimitMultiplier1': '',
    'admin_domain_requestLimit_key_apiKeyEnable': '有効',
    'admin_domain_requestLimit_key_requestLimitMultiplier1': '係数1',
    'admin_domain_requestLimit_label_1day': '1日',
    'admin_domain_requestLimit_label_5min': '5分',
    'admin_domain_requestLimit_page_title': 'ドメイン別リクエスト制限',
    'admin_domain_setting': 'ドメイン設定',
    'admin_domain_settings_hintText_accountLockCount': 'パスワード認証に失敗した時にアカウントロックするまでの回数 (0はチェックしない)',
    'admin_domain_settings_hintText_accountUnlockAtResetPasswd': 'パスワードリセットで新規パスワードが設定された時に、アカウントロック状態を解除するかどうか',
    'admin_domain_settings_hintText_clearFieldsOnMsgDel': 'trueにした場合管理画面からメッセージ削除時に本文やデータを空にします',
    'admin_domain_settings_hintText_fileSizeLimit': '同一ドメイン内ユーザー全員の合計ファイルサイズの制限。(-1、あるいはフィールドなしの場合は制限なし)(容量超過になったファイルは、最終アクセス日時が古い順でCron処理によって消されます)',
    'admin_domain_settings_hintText_fileSizeLimitWarnPercent': 'ファイルサイズ使用量がこの値（%）を超えた場合、警告ログが出力されます。1~99を設定してください。(0以下, 100以上は無視されます)',
    'admin_domain_settings_hintText_fileSizeNoLimit': '制限なし',
    'admin_domain_settings_hintText_greetingMessage': 'マイルームに初期表示されるメッセージ (改行2つはメッセージ区切りと認識される)',
    'admin_domain_settings_hintText_name': 'ドメイン名 (システム管理者のみ変更可)',
    'admin_domain_settings_hintText_passwdExpireDay': '最終パスワード更新以後、設定した日数を経過すると更新ダイアログが表示されます。(0以下は無期限)',
    'admin_domain_settings_hintText_passwordStrength': 'パスワード強度 ${最低文字長}:${必須文字種}, 文字種にはA(半角英字)、U(半角英大文字)、L(半角英小文字)、N(半角数値)、S(半角記号)が利用可能\n“8:ULN” を指定した場合、8文字以上で、半角大文字／半角小文字／半角数値が全て含まれている必要があります。',
    'admin_domain_settings_hintText_removeOldChatMsgsbySeconds': 'n日以前のメッセージが1日1回削除されます。削除しない場合は0を指定します。',
    'admin_domain_settings_hintText_removeOldExtraRoomTagsBySeconds': 'n日以前に完了したルームのルームタグが1日1回削除されます。削除しない場合は0を指定します。（最大2,000日）',


    'admin_domain_settings_hintText_samePasswdProhibitCount': '過去n回に利用したパスワードの再設定を禁止します(0以下は制限なし、最大10)',
    'admin_domain_settings_hintText_sysParam_BotEnable': 'Botの利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_ExternalUserIdTable': '外部連携ID変換テーブルの利用有無(システム管理者のみ変更可)',
    // TODO: Facebook連携機能の非表示（フェーズ1対応）v1.50.0 で削除
    // 'admin_domain_settings_hintText_sysParam_FacebookEnable': 'Facebook連携の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_BedoreWebEnable': 'BEDORE for WEB連携の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_GroupingEnable': 'グループ分け機能の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_HiddenAccountFlowComplete': 'アカウント登録ページで最後の処理まで行われた',
    'admin_domain_settings_hintText_sysParam_HiddenAccountMemo': 'メモ(お客様画面には表示されませんが、コンソール等からは参照可能です。)',
    'admin_domain_settings_hintText_sysParam_HiddenAccountPartner': 'ユーザーIDとは紐付きません',
    'admin_domain_settings_hintText_sysParam_HiddenCreatedAt': '手入力する場合は YYYY-MM-DD HH:mm',
    'admin_domain_settings_hintText_sysParam_KnowledgeEnable': 'Knowledge機能の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_LINECCEnable': 'LINE連携(Switcher APIあり)の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_LINEEnable': 'LINE連携の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_LINEWorksEnable': 'LINEWorks連携の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_MediaCaptureEnable': 'テレワーク機能の有効有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_MobiAgentEnable': 'モビエージェントの有効有無(システム管理者のみ変更可)'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'admin_domain_settings_hintText_sysParam_MobiBotEnable': 'モビボットの有効有無(システム管理者のみ変更可)'.replace('モビボット', window.KonnectPlusServerJSON.service.mobibot || 'モビボット'),
    'admin_domain_settings_hintText_sysParam_MobiCastEnable': 'モビキャストの有効有無(システム管理者のみ変更可)'.replace('モビキャスト', window.KonnectPlusServerJSON.service.mobicast || 'モビキャスト'),
    'admin_domain_settings_hintText_sysParam_MobiConsoleEnable': 'モビコンソールの有効有無(システム管理者のみ変更可)'.replace('モビコンソール', window.KonnectPlusServerJSON.service.mobiconsole || 'モビコンソール'),
    'admin_domain_settings_hintText_sysParam_MobiGuestEnable': '#{mobiguest}連携の有効有無(システム管理者のみ変更可)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'admin_domain_settings_hintText_sysParam_MobiWorkEnable': 'モビワークの有効有無(システム管理者のみ変更可)'.replace('モビワーク', window.KonnectPlusServerJSON.service.mobiwork || 'モビワーク'),
    'admin_domain_settings_hintText_sysParam_MsBotEnable': 'Microsoft Bot Framework連携の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_PQPAEnable': 'PQPA(主質問/主回答管理)機能を有効にする(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_PrivacyMaskingEnable': '個人情報検出アラート機能を有効にする(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_PrivacyMaskingAutoDeleteEnable': '個人情報自動削除予約機能を有効にする(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_PrivacyMaskingPolicyEnable': '個人情報送信制御オプションを有効にする(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_TextToSpeechEnable': '音声読み上げ機能の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_TranslateEnable': '翻訳機能の有効有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_SecurePathEnable': 'Secure Path連携機能の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_EscalationManualEnable': 'エスカレーション機能の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_TraceAlbLogEnable': 'ポート番号照会の利用有無(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_SentimentAnalysisEnable': '感情分析機能を有効にする(システム管理者のみ変更可)',
    'admin_domain_settings_hintText_sysParam_MfaEnable': 'ログイン時に多要素認証を利用する（システム管理者のみ変更可）',
    'admin_domain_settings_hintText_sysParam_GuestMfaEnable': 'ゲストに対するメールまたはSMSでの多要素認証機能を有効にする（システム管理者のみ変更可）',
    'admin_domain_settings_hintText_sysParam_LlmSummaryEnable': 'ルーム完了時にやり取りを要約する機能を有効にする（システム管理者のみ変更可）',
    'admin_domain_settings_hintText_sysParam_ChatwindowEnable': 'Web小窓v2を利用する（システム管理者のみ変更可）',
    'admin_domain_settings_hintText_sysParam_CrmConnectSfEnable': 'CRM Connect を有効にする（システム管理者のみ変更可）',
    'admin_domain_settings_hintText_sysParam_EmbedAutoReloadEnable': 'websocket復旧後のWeb小窓自動リロード処理機能を有効にする',
    'admin_domain_settings_hintText_sysParam_ForceAssignmentEnable': '機能拡張に関連するパーツオプションのためご提供はしておりません（システム管理者のみ変更可）',
    'admin_domain_settings_hintText_userDefaultExposeToDomainIds': '新規ユーザー作成時に設定されるplus_users.exposeToDomainIdsの値',
    'admin_domain_settings_hintText_userDefaultFileSizeLimit': '新規ユーザー作成時に設定されるplus_users.fileSizeLimitの値(原則-1)',
    'admin_domain_settings_hintText_userLimit': 'ユーザー数制限 (超えると作成できない) (システム管理者のみ変更可)',
    'admin_domain_settings_hintText_weakPasswdCheckCount': 'リバースブルートフォース攻撃で頻繁に使用されたパスワードでログインしようとしたユーザーのアカウントをロックする。(指定するのは、最近パスワードが攻撃された回数。0はチェックしない)',
    'admin_domain_settings_hintText_weakPasswdCheckDay': 'weakPasswdCheckCountのチェックする期間 (日)',
    'admin_domain_update_error': '更新に失敗しました',
    'admin_domain_update_successful': '更新に成功しました',
    'admin_domain_user_default_expose_to_domains': '連携ドメイン',
    'admin_domain_user_default_file_size_limmit': 'ファイルサイズ上限',
    'admin_domain_user_limit': 'ユーザー制限',
    'admin_files_form_title_search': 'ファイル検索',
    'admin_files_label_domainFileCount': 'ファイル総数',
    'admin_files_label_domainFileSize': 'ファイル総サイズ',
    'admin_files_label_domainFileSizeLimit_unlimit': '無制限',
    'admin_files_label_domainFileSizeLimit': '利用可能サイズ',
    'admin_files_label_domainFileTitle': '[ドメインストレージ使用状況]',
    'admin_files_label_download_count': 'ダウンロード数',
    'admin_files_label_last_access': '最終アクセス',
    'admin_files_label_owner': '所有者',
    'admin_files_label_size': 'ファイルサイズ',
    'admin_files_page_title': 'ファイル管理',
    'admin_files_mark_bulk_operation_dialog_body': '選択済みファイルを一括で削除します',
    'admin_guest_user_form_error_invalid_type': '入力値が不正です',
    'admin_guest_user_form_error_lower_number': '#{value}以上の数値を入力してください。',
    'admin_guest_user_form_error_greater_number': '#{value}以下の数値を入力してください。',
    'admin_guest_user_form_error_sort_text': '#{value}桁以上の文字を入力してください',
    'admin_guest_user_form_error_long_text': '#{value}桁以下の文字を入力してください',
    'admin_guest_user_form_title_search': 'ゲストユーザー検索',
    'admin_guest_user_form_title_search_by_attr': '拡張ゲスト属性検索',
    'admin_iconNicknames_desc': 'アイコン/ニックネームはLINEゲスト(オプション有効時)、webゲストにのみ適用されます',
    'admin_iconNicknames_example_desc': 'デフォルト用',
    'admin_iconNicknames_example_iconUrl': 'https://example.com/image.png',
    'admin_iconNicknames_example_name': 'オフィシャルアカウント',
    'admin_iconNicknames_form_title': 'アイコン/ニックネーム',
    'admin_iconNicknames_hint_desc': 'オペレーター向け説明文を登録してください。',
    'admin_iconNicknames_hint_iconUrl': '100文字以内で有効なHTTPSのURLを登録してください。デフォルトから変更しない場合は空欄で保存してください',
    'admin_iconNicknames_hint_name': '20文字以内。デフォルトから変更しない場合は空欄で保存してください',
    'admin_iconNicknames_label_desc': '説明',
    'admin_iconNicknames_label_iconUrl': 'アイコンURL',
    'admin_iconNicknames_label_name': 'ニックネーム',
    'admin_knowledge_approve_confirm_dialog_body': 'ナレッジ #{kTitle} を承認してもよろしいですか？',
    'admin_knowledge_approve_confirm_dialog_title': 'ナレッジ承認確認',
    'admin_knowledge_approved' : '承認済み',
    'admin_knowledge_comments' : 'コメント',
    'admin_knowledge_created_at' : '作成日時',
    'admin_knowledge_delete_confirm_dialog_body': 'ナレッジ #{kTitle} を削除してもよろしいですか？',
    'admin_knowledge_delete_confirm_dialog_title' : 'ナレッジ削除確認',
    'admin_knowledge_description': '説明',
    'admin_knowledge_detail_title': 'ナレッジ詳細情報',
    'admin_knowledge_dialog_tab_messages': 'メッセージ',
    'admin_knowledge_dialog_tab_overview': '概要',
    'admin_knowledge_error_access_denied': 'リクエストが許可されていません',
    'admin_knowledge_error_knowledge_not_found': '見つかりません',
    'admin_knowledge_error_knowledge_removed': '削除された',
    'admin_knowledge_external_link_label_required': 'ラベルは必須項目です',
    'admin_knowledge_external_link_setting': '外部リンク設定',
    'admin_knowledge_external_link_url_required': 'URLは必須項目です',
    'admin_knowledge_form_title_search': 'ナレッジ検索',
    'admin_knowledge_id': 'ID',
    'admin_knowledge_is_my_thanked': '獲得した「ありがとう！」数',
    'admin_knowledge_management_management_title': 'ナレッジ管理',
    'admin_knowledge_msg_sender_avatar' : 'アバター',
    'admin_knowledge_msg_sender_name' : '送信者',
    'admin_knowledge_msg' : 'メッセージ',
    'admin_knowledge_owner_id': '作成者',
    'admin_knowledge_removed': '削除済み',
    'admin_knowledge_shares': 'シェア',
    'admin_knowledge_tags': 'タグ',
    'admin_knowledge_thanks': 'ありがとう！',
    'admin_knowledge_title' : 'タイトル',
    'admin_knowledge_unapprove_confirm_dialog_body': 'ナレッジ #{kTitle} を否認してもよろしいですか？',
    'admin_knowledge_unapprove_confirm_dialog_title': 'ナレッジ否認確認',
    'admin_knowledge_views' : '閲覧',
    'admin_logs_form_filter_title': 'フィルターオプション',
    'admin_logs_form_title': 'ログ検索',
    'admin_logs_page_hint': 'システムログ出力例について詳しくはこちら',
    'admin_logs_page_title': 'ログ',
    'admin_actionlogs_page_title': 'アクションログ',
    'admin_maintenance_page_title': 'メンテナンス',
    'admin_menu_item_admHistory_basic_data': '基本項目',
    'admin_menu_item_admHistory_bot_data': '自動応答送信数',
    'admin_menu_item_admHistory_bot_message_count_public': 'Web小窓などへの送信数',
    'admin_menu_item_admHistory_bot_message_count': '自動応答送信数合計',
    'admin_menu_item_admHistory_domain_id': 'ドメインID',
    'admin_menu_item_admHistory_message_count': 'メッセージ送信数合計',
    'admin_menu_item_admHistory_operator_data': 'オペレーター送信数',
    'admin_menu_item_admHistory_operator_message_count_public': 'Web小窓などへの送信数',
    'admin_menu_item_admHistory_operator_message_count': 'オペレーター送信数合計',
    'admin_menu_item_admHistory_user_limit': 'ライセンスID数',
    'admin_menu_item_admHistory': 'ドメイン統計情報',
    'admin_menu_item_admUser': 'システム管理者',
    'admin_menu_item_admUserList': 'システム管理者2',
    'admin_menu_item_apps': 'モバイルアプリ',
    'admin_menu_item_appSignature': 'API署名キー管理',
    'admin_menu_item_appVersion': 'アプリバージョン管理',
    'admin_menu_item_callcenter_config': '設定',
    'admin_menu_item_callcenter_chatwindow_widget': 'Web小窓v2設定',
    'admin_menu_item_callcenter_external_url_reg': '外部連携先URL',
    'admin_menu_item_callcenter_external_userId_table': '外部連携ID変換テーブル',
    'admin_menu_item_callcenter_finishRooms': '完了ルーム検索',
    'admin_menu_item_callcenter_grouping' :'グループ分け',
    'admin_menu_item_callcenter_guests': 'ゲストユーザー管理',
    'admin_menu_item_callcenter_guests_search': 'ゲストユーザー検索',
    'admin_menu_item_callcenter_guests_search_attr': '拡張ゲスト属性検索',
    'admin_menu_item_callcenter_metrics_dailyReport': 'デイリーレポート',
    'admin_menu_item_callcenter_metrics_export': 'データ出力',
    'admin_menu_item_callcenter_metrics_graph': 'グラフ表示',
    'admin_menu_item_callcenter_metrics_mobistat': 'デイリーレポート表示'.replace('デイリーレポート表示', window.KonnectPlusServerJSON.service.mobistats || 'デイリーレポート表示'),
    'admin_menu_item_callcenter_metrics_report': 'デイリーレポート出力',
    'admin_menu_item_callcenter_metrics': '統計情報',
    'admin_menu_item_callcenter_operation_input': '送信設定',
    'admin_menu_item_callcenter_operation_misc': 'その他設定',
    'admin_menu_item_callcenter_operation_mode': '対応モード設定',
    'admin_menu_item_callcenter_operation_ui': '画面操作設定',
    'admin_menu_item_callcenter_operation_workingTime': '営業時間設定',
    'admin_menu_item_callcenter_operation_workingTime_warning_info': '23時59分を設定した場合のみ24時00分の設定となります',
    'admin_menu_item_callcenter_operation': 'オペレーション設定',
    'admin_menu_item_callcenter_rooms_finish': '完了ルーム検索',
    'admin_menu_item_callcenter_rooms_open': '対応中ルーム検索',
    'admin_menu_item_callcenter_rooms_search': 'あいまい検索',
    'admin_menu_item_callcenter_rooms_search_desc_line1': 'ルーム名やゲストユーザーIDなどの条件を指定して検索結果を絞り込むことができるようになりました。',
    'admin_menu_item_callcenter_rooms_search_desc_line2': 'これまでのあいまい検索に戻したい場合は、「新しいあいまい検索」をOFFにするだけで簡単に切り替えられます。',
    'admin_menu_item_callcenter_rooms_search_desc_line3': 'これまでのあいまい検索への切り替え（新しいあいまい検索をOFF）は次回のバージョンアップをもって不可（非表示）となります。',
    'admin_menu_item_callcenter_rooms_search_desc_line4': '以降は新しいあいまい検索のみご利用いただけます。',
    'admin_menu_item_callcenter_rooms_search_field_is_required': '${field}が必須です。',
    'admin_menu_item_callcenter_rooms_search_must_input': '${field}を入力してください',
    'admin_menu_item_callcenter_rooms_search_must_select': '条件を選択してください（複数選択可）',
    'admin_menu_item_callcenter_rooms_search_please_check_holding_flag': '保留中のルームを検索する場合はチェックしてください。',
    'admin_menu_item_callcenter_rooms_search_please_check_if_you_want_to_search_room_which_include_warning_message': '監視キーワードを含む場合はチェックしてください。',
    'admin_menu_item_callcenter_rooms_search_please_input_keyword_to_search': '検索したいキーワードを入力してください',
    'admin_menu_item_callcenter_rooms_search_please_input_keyword_which_include_in_room_close_reason': '終了理由で検索に使用したいワードを入力してください',
    'admin_menu_item_callcenter_rooms_search_please_input_user_id_of_external_guest_user': '外部連携IDを入力してください',
    'admin_menu_item_callcenter_rooms_search_please_input_user_id_of_sender_who_send_first_message': '初回メッセージを送信したオペレーターを入力してください',
    'admin_menu_item_callcenter_rooms_search_please_input_user_id_of_the_first_operator': '初回担当者を入力してください',
    'admin_menu_item_callcenter_rooms_search_please_input_word_to_search_in_user_memo': 'オペレータメモで検索したいワードを入力してください',
    'admin_menu_item_callcenter_rooms_search_please_input_word_to_search_in_guest_user_memo': 'ゲストコメントで検索したいワードを入力してください',
    'admin_menu_item_callcenter_rooms_search_title': '新しいあいまい検索を表示しています',
    'admin_menu_item_callcenter_rooms': '問い合わせルーム管理',
    'admin_menu_item_callcenter_widget': 'Web小窓設定',
    'admin_menu_item_custom_favicons': 'ファビコンのカスタマイズ',
    'admin_menu_item_custom_links': 'リンクのカスタマイズ',
    'admin_menu_item_custom_logWatcher': 'ログ監視のカスタマイズ',
    'admin_menu_item_custom_mail': 'メール通知のカスタマイズ',
    'admin_menu_item_custom_requestLimit': 'リクエスト制限のカスタマイズ',
    'admin_menu_item_custom_service': 'サービス名のカスタマイズ',
    'admin_menu_item_custom_style': 'スタイルのカスタマイズ',
    'admin_menu_item_custom_uploadLimit': '共通ファイルアップロード制限のカスタマイズ',
    'admin_menu_item_custom_userFilesRestriction': 'チャット時のファイルアップロード制限のカスタマイズ',
    'admin_menu_item_custom_widgetVersion': '選択可能な小窓バージョンのカスタマイズ',
    'admin_menu_item_custom_widgetVersion2': '選択可能なWeb小窓v2バージョンのカスタマイズ',
    'admin_menu_item_custom_securePath': 'SecurePathURL設定',
    'admin_menu_item_custom': 'カスタマイズ',
    'admin_menu_item_sentiment_analysis_editing': '感情分析結果管理',
    'admin_menu_item_sentiment_alert': '閾値・アラート設定',
    'admin_menu_item_domain_detail_account': '契約者情報（アカウント情報）',
    'admin_menu_item_domain_detail_options': 'ドメインオプション設定',
    'admin_menu_item_domain_detail_setting': 'ドメイン設定',
    'admin_menu_item_domain_detail': 'ドメイン設定',
    'admin_menu_item_domain_histories': 'ご利用実績',
    'admin_menu_item_domain_logs': 'ログ',
    'admin_menu_item_domain_actionlogs': 'アクションログ',
    'admin_menu_item_domain_preference_autoreply': '自動応答設定',
    'admin_menu_item_domain_preference_cors': 'CORS設定',
    'admin_menu_item_domain_preference_initial': '初期設定',
    'admin_menu_item_domain_preference_suggest': 'サジェストモジュール設定',
    'admin_menu_item_domain_preference_sysadmOnly': 'システム管理者専用設定',
    'admin_menu_item_domain_preference_webhook': 'Webhook設定',
    'admin_menu_item_domain_preference': 'モビエージェント設定'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'admin_menu_item_domain_sns_facebook': 'Facebook',
    'admin_menu_item_domain_sns_bedoreWeb': 'BEDORE for WEB',
    'admin_menu_item_domain_sns_line': 'LINE',
    'admin_menu_item_domain_sns_lineworks': 'LINE Works',
    'admin_menu_item_domain_sns_lineworks_api2': 'LINE WORKS API2.0',
    'admin_menu_item_domain_sns_mobiguest': 'オリジナルクライアントアプリ'.replace('オリジナルクライアントアプリ', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'admin_menu_item_domain_sns_msbot': 'Microsoft Bot Framework',
    'admin_menu_item_domain_sns': 'SNS',
    'admin_menu_item_domains': 'ドメイン管理',
    'admin_menu_item_files': 'ファイル管理',
    'admin_menu_item_input_iconNicknames': 'アイコン/ニックネーム',
    'admin_menu_item_input_ngwords': '禁止語',
    'admin_menu_item_input_snippets': '定型文',
    'admin_menu_item_input_stamps': 'スタンプ',
    'admin_menu_item_input_survey': '満足度アンケート',
    'admin_menu_item_input_templates': 'テンプレート',
    'admin_menu_item_input_warnwords': '監視キーワード',
    'admin_menu_item_knowledges': 'ナレッジ管理',
    'admin_menu_item_logs': 'ログ',
    'admin_menu_item_maintenance': 'メンテナンス',
    'admin_menu_item_messages': 'メッセージ管理',
    'admin_menu_item_news': 'お知らせ',
    'admin_menu_item_publicFiles': '共通ファイル管理',
    'admin_menu_item_rooms': 'ルーム管理',
    'admin_menu_item_sysstat': 'システム統計',
    'admin_menu_item_users': 'ユーザー管理',
    'admin_menu_subheader_basicAPIs': 'コミュニケーション情報管理',
    'admin_menu_subheader_callcenter': 'コールセンター管理',
    'admin_menu_subheader_data_statistics': 'データ・統計管理',
    'admin_menu_subheader_sentiment_analysis': '感情分析管理',
    'admin_menu_subheader_domain_config': 'ドメイン設定管理',
    'admin_menu_subheader_keyboard': '入力情報管理',
    'admin_messages_api_error_invalid_messageId': 'メッセージIDが不正です',
    'admin_messages_api_error_removed_message': 'メッセージはすでに削除されています',
    'admin_messages_api_error_system_message': 'システムメッセージは削除できません',
    'admin_messages_api_error_wrong_condition': '削除できないメッセージです',
    'admin_messages_api_error_wrong_message_sender': '送信者が不正です',
    'admin_messages_api_error_wrong_roomId': 'ルームIDが不正です',
    'admin_messages_changeRoomStatus_changeOperator': '@#{user1}@が@#{user2}@を担当者に変更しました。',
    'admin_messages_changeRoomStatus_changeGroup': '@#{user1}@がグループを@#{group}@に変更しました。',
    'admin_messages_changeRoomStatus_checkIn': '@#{user1}@がルームを対応しました。',
    'admin_messages_changeRoomStatus_close': '@#{user1}@がルームを終了しました。',
    'admin_messages_changeRoomStatus_finish': '@#{user1}@がルームを完了しました。',
    'admin_messages_changeRoomStatus_startAutoReply': '@#{user1}@が自動応答を開始しました。',
    'admin_messages_changeRoomStatus_startHold': '@#{user1}@が保留状態を開始しました。',
    'admin_messages_changeRoomStatus_stopAutoReply': '@#{user1}@が自動応答を終了しました。',
    'admin_messages_changeRoomStatus_stopHold': '@#{user1}@が保留状態を終了しました。',
    'admin_messages_changeRoomStatus_takeOver': '@#{user1}@がルームを引き継ぎました。',
    'admin_messages_delete_confirm_dialog_body': 'メッセージ #{msgId} を削除してもよろしいですか？\nLINE等外部システムに連携済みの内容は削除されません。',
    'admin_messages_delete_confirm_dialog_title' : 'メッセージ削除確認',
    'admin_messages_delete_keyword_confirm_dialog_title' : 'キーワード削除',
    'admin_messages_delete_keyword_confirm_dialog_desc' : '赤色で表記したテキストを削除します。削除したテキストは復元できません。よろしいですか？',
    'admin_messages_unmark_confirm_dialog_body': '却下してもよろしいですか？',
    'admin_messages_unmark_confirm_dialog_title' : 'メッセージ却下確認',
    'admin_messages_unmark_keyword_confirm_dialog_title' : 'キーワード削除却下確認',
    'admin_messages_fixed_text_joinedContent': 'ユーザーがルームに参加しました',
    'admin_messages_fixed_text_leftContent': 'ユーザーがルームから退出しました',
    'admin_messages_form_desc_search': '条件を指定して検索してください。ダウンロード時は期間以外の条件は適用されません',
    'admin_messages_form_label_fromDate': '検索開始期間',
    'admin_messages_form_label_keyword': 'キーワード',
    'admin_messages_form_label_roomId': 'ルームID',
    'admin_messages_form_label_toDate': '検索終了期間',
    'admin_messages_form_label_userId': 'ユーザーID',
    'admin_messages_form_title_search': 'メッセージ検索',
    'admin_messages_form_title_markedMessages': '削除予約メッセージ検索',
    'admin_messages_form_title_canceledMarkedMessages': '取り消された個人情報自動削除検索',
    'admin_messages_form_title_sentimentManagement': '感情分析結果管理 メッセージ一覧',
    'admin_messages_label_invite_user': '招待ユーザー',
    'admin_messages_label_deletedBy': 'このメッセージは #{user} によって削除されています',
    'admin_messages_label_maskedBy': 'このメッセージは #{user} によってキーワード削除されています',
    'admin_messages_label_markedBy': 'このメッセージは #{user} によって削除予約されています',
    'admin_messages_label_unmarkedBy': 'このメッセージは #{user} によって削除予約を取り消されています',
    'admin_messages_label_markedComment': 'コメント',
    'admin_messages_label_multi_message': 'このメッセージはマルチメッセージです',
    'admin_messages_label_privateInfoDetected': '個人情報が含まれている可能性があります',
    'admin_messages_label_show_source': 'ソース表示',
    'admin_messages_label_translated_message': 'このメッセージには翻訳があります',
    'admin_messages_label_translatedAt': '翻訳実行日時',
    'admin_messages_label_translatedBy': '翻訳実行ユーザー',
    'admin_messages_label_translatedDetected': '自動検出言語',
    'admin_messages_label_translatedFrom': 'From指定言語',
    'admin_messages_label_translatedTo': 'To指定言語',
    'admin_messages_label_truncated': '(翻訳元文章が長すぎるため、翻訳は途中までしか行われていません)',
    'admin_messages_mark_bulk_operation_dialog_body': '選択済みメッセージを一括で処理します',
    'admin_messages_mark_bulk_operation_dialog_title': '一括操作',
    'admin_messages_page_title': 'メッセージ管理',
    'admin_messages_snack_invalid_from_to': '検索対象期間の開始/終了の組合せが不正です',
    'admin_messages_snack_no_condition': '検索条件を指定してください',
    'admin_messages_table_label_createdAt': '作成日時',
    'admin_messages_table_label_deleteKeyword': '削除キーワード',
    'admin_messages_table_label_deleteType': '削除タイプ',
    'admin_messages_table_label_suggestedSentiment': '修正結果',
    'admin_messages_table_label_msgBody': '本文',
    'admin_messages_table_label_msgId': 'メッセージID',
    'admin_messages_table_label_msgType': 'タイプ',
    'admin_messages_table_label_senderId': '送信者',
    'admin_messages_table_label_sentiment': '判定結果',
    'admin_messages_table_label_sentiment_fixes': '修正内容',
    'admin_messages_table_label_sentiment_updatedAt': '申請日時',
    'admin_messages_table_label_sentiment_msgBody': 'メッセージ',
    'admin_messages_table_label_sentiment_suggestedBy': 'ユーザー名',
    'admin_messages_table_label_status': 'ステータス',
    'admin_messages_table_label_roomStatus': 'ルーム状態',
    'admin_messages_table_label_autoReplyMode': '応答モード',
    'admin_messages_table_header_deleteComment': 'コメント',
    'admin_messages_table_deleteType_msg': 'メッセージ',
    'admin_messages_table_deleteType_keyword': 'キーワード',
    'admin_metrics_rooms_form_desc_main': '条件を指定して検索してください',
    'admin_metrics_rooms_form_title_main': 'ルームタイプ毎の新規作成数をグラフ表示します',
    'admin_metrics_rooms_graph_title_count': '件数(全ユーザー合計)',
    'admin_metrics_rooms_graph_title_msg': 'メッセージ数',
    'admin_metrics_rooms_label_count_room11':'1 to 1 ルーム',
    'admin_metrics_rooms_label_count_room1N':'1 to N ルーム',
    'admin_metrics_rooms_label_count_roomPub':'パブリックルーム',
    'admin_metrics_rooms_label_count_total':'合計',
    'admin_metrics_rooms_label_count': '件数',
    'admin_metrics_rooms_label_pluscc_time_avg': '平均時間',
    'admin_metrics_rooms_label_pluscc_time_max': '最大時間',
    'admin_metrics_rooms_label_pluscc_time_min': '最小時間',
    'admin_metrics_rooms_label_totalCount': '合計',
    'admin_metrics_rooms_title': 'ルーム統計情報',
    'admin_news_error_label_empty_content': 'ラベルコンテンツが入力されていません',
    'admin_news_error_url_empty_content': 'URLコンテンツが入力されていません',
    'admin_news_label_display_finish_date': '表示終了日',
    'admin_news_label_display_start_date': '表示開始日',
    'admin_news_label_title': 'タイトル',
    'admin_news_label_url': 'URL',
    'admin_news_page_title': 'お知らせ',
    'admin_ngword_management': '禁止語',
    'admin_ngword_management_title_operator': 'オペレーター向け禁止語管理',
    'admin_ngword_management_title_guest': 'ゲストユーザー向け禁止語管理',
    'admin_preference_accountInfo_param_accountInfo_company': '会社名/団体名',
    'admin_preference_accountInfo_param_accountInfo_companyKana': '会社名/団体名フリガナ',
    'admin_preference_accountInfo_param_accountInfo_zipCode': '郵便番号',
    'admin_preference_accountInfo_param_accountInfo_address': '住所',
    'admin_preference_accountInfo_param_accountInfo_department': '部署',
    'admin_preference_accountInfo_param_accountInfo_mail': 'メールアドレス',
    'admin_preference_accountInfo_param_accountInfo_phoneNumber': '電話番号',
    'admin_preference_auto_reply_autoReply_http_url': '自動応答外部連携URL',
    'admin_preference_auto_reply_autoReply_http_useResume': 'onResume 要求を送る',
    'admin_preference_auto_reply_autoReply_http_withMsgExtra': 'msgExtraフィールドを含める',
    'admin_preference_auto_reply_autoReply_http_withUserAttribute': 'userAttributeフィールドを含める',
    'admin_preference_auto_reply_autoReply_messageBranch_delimiter': '自動応答の分岐項目の文字列',
    'admin_preference_auto_reply_autoReply_messageBranch_numDelimiter': '自動応答の番号分岐項目の文字列',
    'admin_preference_auto_reply_autoReply_messageBranch_prefix': '自動応答の分岐項目の接頭辞',
    'admin_preference_auto_reply_autoReply_simple_branchIdPerRoom': '分岐位置のリセット',
    'admin_preference_auto_reply_autoReply_simple_debugPrint': 'デバッグ通知',
    'admin_preference_auto_reply_autoReply_simple_keywordMatchMode': 'キーワード判定モード',
    'admin_preference_auto_reply_autoReply_simple_keywordMatchMode_options_find': 'find: 入力文字列全体がキーワードに含まれる',
    'admin_preference_auto_reply_autoReply_simple_keywordMatchMode_options_fuzzy': 'fuzzy: 入力文字列の一部がキーワードに含まれる',
    'admin_preference_auto_reply_autoReply_simple_keywordMatchMode_options_match': 'match: 入力文字列全体がキーワードと一致する',
    'admin_preference_auto_reply_autoReplyDefaultSenderProfileAvatar': 'デフォルトアバター',
    'admin_preference_auto_reply_autoReplyDefaultSenderProfileName': 'デフォルトユーザー名',
    'admin_preference_auto_reply_autoReplyEndMsg': '自動応答完了時メッセージ',
    'admin_preference_auto_reply_autoReplyFailMsg': '自動応答失敗時メッセージ',
    'admin_preference_auto_reply_autoReplyInitialMode': 'ルーム作成時自動応答モード',
    'admin_preference_auto_reply_autoReplyMaintenanceMsg': 'メンテナンスメッセージ',
    'admin_preference_auto_reply_autoReplyModule': '自動応答モジュール',
    'admin_preference_auto_reply_autoReplyModule_options_konnect_module_PlusccAutoReplyModuleNull': '利用しない',
    'admin_preference_auto_reply_autoReplyModule_options_konnect_module_PlusccAutoReplyModuleHttp': 'HTTPモジュール',
    'admin_preference_auto_reply_autoReplyModule_options_konnect_module_PlusccAutoReplyModuleSimple': 'Simpleモジュール',
    'admin_preference_auto_reply_autoReplyModule_options_konnect_module_PlusccAutoReplyModuleLinecc': 'Lineccモジュール',
    'admin_preference_auto_reply_autoReplySorryMsg': '有人対応切替失敗時のメッセージ デフォルト',
    'admin_preference_auto_reply_autoReplySorryMsgBusy': '有人対応切替失敗時のメッセージ 閾値超過',
    'admin_preference_auto_reply_autoReplySorryMsgHoliday': '有人対応切替失敗時のメッセージ 休日',
    'admin_preference_auto_reply_autoReplySorryMsgNg': '有人対応切替失敗時のメッセージ 有人対応モード無効時',
    'admin_preference_auto_reply_autoReplySorryMsgOffHours': '有人対応切替失敗時のメッセージ 営業時間外',
    'admin_preference_auto_reply_autoReplySorryMsgWait': '待機状態開始メッセージ',
    'admin_preference_auto_reply_autoReplyStartMsg': '自動応答開始時メッセージ',
    'admin_preference_auto_reply_callbackSignatureSecret': '署名用キー文字列',
    'admin_preference_auto_reply_maxOperatorsRoom': 'オペレーター対応可能問い合わせ数',
    'admin_preference_auto_reply_operatorsEnable': 'オペレーター対応を有効にする',
    'admin_preference_auto_reply_operatorsEnable_AsDomain': 'ドメイン共通の設定を使用',
    'admin_preference_auto_reply_operatorsEnable_On': 'オン',
    'admin_preference_auto_reply_operatorsEnable_Off': 'オフ',
    'admin_preference_auto_reply_roomAutoCloseMsg_wait': 'タイムアウトメッセージ(有人対応切替待機時)',
    'admin_preference_auto_reply_roomAutoCloseTimeout_wait': '無応答ルームタイムアウト秒(有人対応切替待機時)',
    'admin_preference_auto_reply_waitEndAutoReply_autoSwitchMsg': '有人対応切替待機完了時メッセージ',
    'admin_preference_auto_reply_waitEndAutoReply_enable': '有人対応切替待機を有効にする',
    'admin_preference_auto_reply_waitEndAutoReply_roomLimit': '有人対応切替待機状態にできる最大ルーム数',
    'admin_preference_auto_reply_setting_by_condition': '有人対応中の条件別自動応答設定',
    'admin_preference_auto_reply_setting_operator_conditional': '有人対応中の条件別自動応答設定',
    'admin_preference_autoReplyModule_form_desc_modHTTP': 'HTTP接続モジュールを利用する場合の設定を記載して下さい\n自動応答動作切り替えはボット内部コマンドを利用して下さい',
    'admin_preference_autoReplyModule_form_desc_modSimple': 'モビエージェント組み込みのモビボットモジュールを利用する場合の設定を記載して下さい\n自動応答動作切り替えはボット内部コマンドを利用して下さい\n文言登録は[ドメイン設定] > [モビエージェント 詳細設定] > [自動応答文言登録]から行います'.replace(/モビエージェント/g, window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント').replace('モビボット', window.KonnectPlusServerJSON.service.mobibot || 'モビボット'),
    'admin_preference_autoReplyModule_form_desc_module': '優先する自動応答モジュールを選択して下さい\nモジュール切替えは各モジュール内部コマンドで行います',
    'admin_preference_autoReplyModule_form_title_modHTTP': 'HTTPモジュール設定',
    'admin_preference_autoReplyModule_form_title_modSimple': 'モビエージェント組み込みのモビボットモジュール設定'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント').replace('モビボット', window.KonnectPlusServerJSON.service.mobibot || 'モビボット'),
    'admin_preference_autoReplyModule_form_title_module': '優先自動応答モジュール',
    'admin_preference_autoReplyModuleBySns_label_selectSnsType': '流入元を指定してください',
    'admin_preference_autoReplyModuleBySns_snackMessage_duplicate_snsType': '指定された流入元が重複しています',
    'admin_preference_autoReplyModuleBySns_snackMessage_fileLoaded': '内容を確認して保存してください',
    'admin_preference_autoReplyModuleBySns_snackMessage_maxLength': '設定可能な流入元別自動応答モジュールは最大200件です',
    'admin_preference_autoReplyModuleBySns_snackMessage_unavailable_module': '指定されたモジュールは利用できません',
    'admin_preference_autoReplyModuleBySns_snackMessage_unavailable_snsType': '指定された流入元は利用できません',
    'admin_preference_reply_resume_max_length': '自動応答開始位置は最大#{param}で入力してください.',
    'admin_preference_reply_resume_point_is_empty': '自動応答開始位置は空欄保存できません',
    'admin_preference_callcenterMode_endAutoReplyCheckWithMaxInteraction': 'ドメイン全体の対応上限を超えるルームを有人切替する',
    'admin_preference_callcenterMode_guestReviewEnable': 'ゲストによる満足度評価',
    'admin_preference_callcenterMode_maxOperatorsRoom': 'オペレーター対応可能問い合せ数',
    'admin_preference_callcenterMode_maxOperatorsRoomPerUser': '1オペレーターの対応可能問い合わせ数',
    'admin_preference_callcenterMode_operatorAutoAssign': '担当者自動割り当て',
    'admin_preference_callcenterMode_operatorsEnable': 'オペレーター対応を有効にする',
    'admin_preference_callcenterMode_param_operatorStatusEnable': 'ステータス設定',
    'admin_preference_callcenterMode_param_operatorStatusList': '',
    'admin_preference_callcenterMode_operatorAutoAssign_off': '無効',
    'admin_preference_callcenterMode_operatorAutoAssign_on': '有効',
    'admin_preference_desc_lineMessageAPI': '11チャンネル以上設定する場合は、CSV設定ファイルをアップロードしてください。',
    'admin_preference_desc_mobiGuest': '10チャネルまで設定することができます。',
    'admin_preference_desc_msBot': '10チャネルまで設定することができます。',
    'admin_preference_example_corsAllowOrigins': 'https://example.com',
    'admin_preference_externalCmsLink_desc': '説明',
    'admin_preference_externalCmsLink_icon': 'アイコン',
    'admin_preference_externalCmsLink_label': 'ラベル',
    'admin_preference_externalCmsLink_systemlinks_mobibot': 'モビボット'.replace('モビボット', window.KonnectPlusServerJSON.service.mobibot || 'モビボット'),
    'admin_preference_externalCmsLink_systemlinks_mobicast': 'モビキャスト'.replace('モビキャスト', window.KonnectPlusServerJSON.service.mobicast || 'モビキャスト'),
    'admin_preference_externalCmsLink_systemlinks_mobiconsole': 'モビコンソール'.replace('モビコンソール', window.KonnectPlusServerJSON.service.mobiconsole || 'モビコンソール'),
    'admin_preference_externalCmsLink_systemlinks_policy': '個人情報保護方針',
    'admin_preference_externalCmsLink_systemlinks_privacyInformation': '個人情報の取扱いについて',
    'admin_preference_externalCmsLink_systemlinks_registration': '新規ドメイン申し込み',
    'admin_preference_externalCmsLink_systemlinks_rule': '利用規約',
    'admin_preference_externalCmsLink_systemlinks_manual': 'マニュアル',
    'admin_preference_externalCmsLink_systemlinks_help': 'ヘルプ',
    'admin_preference_externalCmsLink_systemlinks_emergencyContact': '緊急連絡先',
    'admin_preference_externalCmsLink_systemlinks_llmSummarySupportLink': '対話内容要約機能 サポートリンク',
    'admin_preference_externalCmsLink_title': 'リンク設定',
    'admin_preference_externalCmsLink_url': 'URL',
    'admin_preference_facebook_facebookForeignUserCheckUrl': 'Facebookユーザー外部連携変換URL',
    'admin_preference_facebook_facebookWebhookAppSecret': 'App Secret',
    'admin_preference_facebook_facebookWebhookAccessToken': 'Access Token',
    'admin_preference_facebook_facebookWebhookVerifyToken': 'Verify token',
    'admin_preference_facebook_test_token': 'テストトークン',
    'admin_preference_bedoreWeb_bedoreWebEndpoint': 'エンドポイント',
    'admin_preference_bedoreWeb_bedoreWebSecretKey': 'Secret Key',
    'admin_preference_bedoreWeb_bedoreWebForeignUserCheckUrl': 'BEDORE for WEBユーザー外部連携ID変換URL',
    'admin_preference_form_title_editor': '編集',
    'admin_preference_form_title_token_editor': '編集',
    'admin_preference_form_title': 'ドメイン設定管理',
    'admin_preference_hintLink_corsAllowOrigins': 'CORSについてはこちら',
    'admin_preference_hintLink_autoReplyEndGlobalLock': '',
    'admin_preference_hintLink_lineAutoReplyBranchButton': 'クイックリプライについてはこちら',
    'admin_preference_hintLink_lineUserDataClearAtBlock': '',
    'admin_preference_manualLink_line2Comment': 'デフォルトの流入元の表示についてはこちら',
    'admin_preference_manualLink_mobiGuestComment': 'デフォルトの流入元の表示についてはこちら',
    'admin_preference_manualLink_msbotComment': 'デフォルトの流入元の表示についてはこちら',
    //https://mobilus.backlog.jp/view/MBA-14811 ルームタグ31から60に加えてルームタグ200から249を追加
    //allowGuestUpdateRoomTag31_60はDBカラム名なので、そのままにします。
    'admin_preference_hintText_allowGuestUpdateRoomTag31_60': 'ゲストWeb小窓でoptions.roomTag31~60,200~249を利用して、起動時に拡張ルームタグを指定することができるようにします',
    'admin_preference_hintText_miscStartChatUser': '外部連携IDを利用したweb小窓に対して、チャットを開始することができます',
    'admin_preference_hintText_autoReply_http_url': 'HTTP自動応答のURL\n デフォルト: ""',
    'admin_preference_hintText_autoReply_http_useResume': 'trueの場合オペレーター対応から自動応答に切り替えられた際に onResume 要求を送ることになります\n デフォルト: false',
    'admin_preference_hintText_autoReply_http_withMsgExtra': 'trueの場合自動応答のリクエストにmsgExtraが含まれます。\nmsgExtraに記載されたファイル等のURLは認証不要でアクセスできるようになります\n デフォルト: false',
    'admin_preference_hintText_autoReply_http_withUserAttribute': 'trueの場合自動応答のリクエストにuserAttributeが含まれます。\n デフォルト: false',
    'admin_preference_hintText_autoReply_messageBranch_delimiter': '自動応答の分岐項目を分割するための文字列\nデフォルト: 未設定',
    'admin_preference_hintText_autoReply_messageBranch_numDelimiter': '自動応答の番号分岐項目を分割するための文字列\n例: “2: いいい” の場合、”2”が分岐項目になる\nただし、分岐項目は3桁までの数値に制限される\nデフォルト: 未設定',
    'admin_preference_hintText_autoReply_messageBranch_prefix': '自動応答の分岐項目を見つけるための接頭辞\nデフォルト: 未設定',
    'admin_preference_hintText_autoReply_simple_branchIdPerRoom': '新規ルームが作成されたときに応答の分岐位置をリセットする\n デフォルト: false',
    'admin_preference_hintText_autoReply_simple_debugPrint': 'モビボットの動作確認用デバッグログを問合せルームに投稿する。\n(LINEやFacebookには送信されません)\n デフォルト: false'.replace('モビボット', window.KonnectPlusServerJSON.service.mobibot || 'モビボット'),
    'admin_preference_hintText_autoReply_simple_keywordMatchMode': 'ユーザーの入力した文字列をキーワードと一致させる場合の挙動\nデフォルト: "fuzzy"',
    'admin_preference_hintText_autoReplyDefaultSenderProfileAvatar': '自動応答Botのデフォルトアバター 100文字以内の有効なHTTPSのURL\n個別のメッセージにプロフィール付加情報が設定されていない場合にデフォルト値として設定されます\nLINEゲストに送信する場合はIcon/Nickname Switch APIを有効にする必要があります\nWebゲストに送信する場合はWeb小窓設定より優先されます\nその他のSNSには適用されません\nデフォルト: 未設定',
    'admin_preference_hintText_autoReplyDefaultSenderProfileName': '自動応答Botのデフォルトユーザー名 20文字以内\n個別のメッセージにプロフィール付加情報が設定されていない場合にデフォルト値として設定されます\nLINEゲストに送信する場合はIcon/Nickname Switch APIを有効にする必要があります\nWebゲストに送信する場合はWeb小窓設定より優先されます\nその他のSNSには適用されません\nデフォルト: 未設定',
    'admin_preference_hintText_autoReplyEndGlobalLock': 'クラスターをロックしオペレーターが対応中のルーム数の計算とルーム状態の変更を同時に行う',
    'admin_preference_hintText_autoReplyEndMsg': '自動応答から有人対応に切り替える直前に、ゲストに対してこのメッセージを送信する\n未指定時は送信されない\nデフォルト: ""\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_autoReplyFailMsg': '自動応答モジュールが通信エラーなどで失敗した時に送信するメッセージ\n未指定時は送信されない\nデフォルト: ""',
    'admin_preference_hintText_autoReplyInitialMode': 'ルームが作成された時に、自動応答モードにするかどうか\nfalse にすると初期状態が有人対応になります\nデフォルト: true',
    'admin_preference_hintText_autoReplyMaintenanceMsg': 'サーバーがメンテナンスモード時に送信するメッセージ\nデフォルト: "メンテナンス中のためご利用いただけません"',
    'admin_preference_hintText_autoReplyModule_cant_change': '自動応答モジュールをご利用できません。システム管理者にお問合せください。',
    'admin_preference_hintText_autoReplyModule': '自動応答モジュール名\n未指定の場合は、応答不能モードが使用されます。\nデフォルト: "利用しない"',
    'admin_preference_hintText_autoReplySorryMsg': '自動応答から有人対応に切り替えようとしたがオペレーターが対応できない場合に送信されるメッセージ\n デフォルト: "申し訳ございません。ただいま、オペレータにお繋ぎできません。"\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_autoReplySorryMsgBusy': '閾値超過が原因で自動応答から有人対応に切り替えようとしたがオペレーターが対応できない場合に送信されるメッセージ\n未指定の場合有人対応切替失敗時のメッセージが適用\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_autoReplySorryMsgHoliday': '休日が原因で自動応答から有人対応に切り替えようとしたがオペレーターが対応できない場合に送信されるメッセージ\n未指定の場合有人対応切替失敗時のメッセージが適用\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_autoReplySorryMsgNg': '有人対応モード無効が原因で自動応答から有人対応に切り替えようとしたがオペレーターが対応できない場合に送信されるメッセージ\n未指定の場合有人対応切替失敗時のメッセージが適用\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_autoReplySorryMsgOffHours': '営業時間外が原因で自動応答から有人対応に切り替えようとしたがオペレーターが対応できない場合に送信されるメッセージ\n未指定の場合有人対応切替失敗時のメッセージが適用\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_autoReplySorryMsgWait': '自動応答から有人対応に切り替えようとしたが、閾値超過が原因で有人対応切替待機状態になった場合に送信されるメッセージ\n未指定の場合有人対応切替失敗時のメッセージが適用\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%、%%WAIT_NUM%%',
    'admin_preference_hintText_autoReplyStartMsg': '自動応答を開始するときにゲストに送信されるメッセージ\n未指定時は送信されない\nデフォルト: ""\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_callbackSignatureSecret': '外部サーバーにHTTPリクエストを送る場合の署名キー\n自動応答やサジェスト、完了通知などすべてのHTTPリクエストで共通\nデフォルト: ""',
    'admin_preference_hintText_closeDays': '全休日を年月日で指定する',
    'admin_preference_hintText_corsAllowOrigins': '未指定の場合はすべてのオリジンからのリクエストを許可します\nweb小窓を設置するオリジンを記載してください',
    'admin_preference_hintText_end': '終了時刻',
    'admin_preference_hintText_facebookForeignUserCheckUrl': 'FacebookユーザーのIDを元に外部サーバーから外部連携IDを取得するURL\n例: "https://example.com/getLineUserId"\nデフォルト: ""',
    'admin_preference_hintText_facebookWebhookAppSecret': 'Facebookから送られたデータの署名チェックに使用する。(未指定の場合は署名をチェックしない)',
    'admin_preference_hintText_facebookWebhookAccessToken': 'アクセストークンを指定する',
    'admin_preference_hintText_facebookWebhookVerifyToken': 'WebHookを登録するときの初回確認用文字列',
    'admin_preference_hintText_bedoreWebEndpoint': 'BEDORE for WEBの環境に接続するエンドポイント',
    'admin_preference_hintText_bedoreWebSecretKey': 'BEDORE for WEBの環境に接続するためのシークレットキー',
    'admin_preference_hintText_bedoreWebForeignUserCheckUrl': 'UIDを元に外部サーバーから外部連携IDを取得するURL\n例: "https://xxx:8000/getBedoreWebUserId"',
    'admin_preference_hintText_foreignGuestUserServerUrl': 'Web小窓のゲストユーザーの外部連携ログインチェックURL\nデフォルト: ""',
    'admin_preference_hintText_guestReviewEnable': '',
    'admin_preference_hintText_guestUserGreetingMsg': 'ゲストユーザーがルームを作成した時の最初のメッセージ\n未指定時は送信されない\nデフォルト: ""\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_line2AccessToken': 'Channel IDとChannel Secretを用いて自動更新されます',
    'admin_preference_hintText_line2AccessTokenExpire': 'アクセストークン有効期限',
    'admin_preference_hintText_line2AccessTokenUpdateDate': 'アクセストークン更新日時',
    'admin_preference_hintText_line2AccessTokenUpdateEnable': 'Business Connectの場合は自動更新をONにしてください',
    'admin_preference_hintText_line2ChannelId': '連携するLINEアカウントのChannel ID',
    'admin_preference_hintText_line2ChannelSecret': '連携するLINEアカウントのChannel Secret',
    'admin_preference_hintText_line2Comment': 'チャネルの説明を記載してください\nここに登録された文言がオペレーター画面の流入元に表示されます',
    'admin_preference_hintText_line2CreateGenericPostback': '有効にすると"message", "follow", "unfollow" , "postback"以外のwebhookイベントがpostbackメッセージに変換されます',
    'admin_preference_hintText_line2ForeignUserCheckUrl': 'UIDを元に外部サーバーから外部連携IDを取得するURL\n例: "https://example.com/getLineUserId"\nデフォルト: ""',
    'admin_preference_hintText_line2SenderProfileEnable': 'Icon/Nickname Switch APIを使用する場合TRUEにして下さい。',
    'admin_preference_hintText_line2ServiceCode': 'チャットAPIオプションを申込んだ場合に、LINE社から発行されます。チャットAPIオプションを利用しないLINEアカウントでは、この欄に値を設定しないでください',
    'admin_preference_hintText_lineAutoReplyBranchButton': 'LINEでの自動応答の分岐をクイックリプライにする\nデフォルト: false',
    'admin_preference_hintText_lineccAccessToken': 'Channel IDとChannel Secretを用いて自動更新されます',
    'admin_preference_hintText_lineccAccessTokenExpire': 'アクセストークン有効期限',
    'admin_preference_hintText_lineccAccessTokenUpdateDate': 'アクセストークン更新日時',
    'admin_preference_hintText_lineccAutoReplyInitialMode_modeBot': 'Botモードの場合はルーム作成時自動応答モードはtrueである必要があります。',
    'admin_preference_hintText_lineccAutoReplyInitialMode_modeBoth': 'ルームが作成された時に、自動応答モードにするかどうか。\nfalse にすると初期状態が有人対応になります\nデフォルト: true',
    'admin_preference_hintText_lineccAutoReplyInitialMode_modeOperator': 'オペレーターモードの場合はルーム作成時自動応答モードはtrueである必要があります。',
    'admin_preference_hintText_lineccAutoReplyModule_modeBot': 'Botモードの場合は"Linecc モジュール"以外を選択してください',
    'admin_preference_hintText_lineccAutoReplyModule_modeBoth': '両方モードの場合は"Linecc モジュール"以外を選択してください',
    'admin_preference_hintText_lineccAutoReplyModule_modeOperator': 'オペレーターモードの場合は原則として"Linecc モジュール"を選択する必要があります',
    'admin_preference_hintText_lineccChannelId': '連携するLINEアカウントのChannel ID',
    'admin_preference_hintText_lineccChannelSecret': '連携するLINEアカウントのChannel Secret',
    'admin_preference_hintText_lineccCreateGenericPostback': '有効にすると"message", "follow", "unfollow" , "postback"以外のwebhookイベントがpostbackメッセージに変換されます',
    'admin_preference_hintText_lineccDestinationId': 'ルーム完了後などにLINEのWebhookを切り替える相手先のDestination IDを指定してください。\n設定値はLINE Developers画面のSwitcherAPIタブの値を参照してください。\nデフォルト: ""',
    'admin_preference_hintText_lineccMyDestinationId': '管理画面から有人チャット開始を実施した場合に、LINEのWebhookを自システムに切り替えるため自身のDestination IDを指定してください。\n設定値はLINE Developers画面のSwitcherAPIタブの値を参照してください。\nデフォルト: ""',
    'admin_preference_hintText_lineccForeignUserCheckUrl': 'UIDを元に外部サーバーから外部連携IDを取得するURL\n例: "https://example.com/getLineUserId"\nデフォルト: ""',
    'admin_preference_hintText_lineccMode': 'モードを選択してください。モードによって以下の項目を正しく設定する必要があります',
    'admin_preference_hintText_lineccRoomAutoCloseTag5_modeBot': 'タイムアウトによる自動完了時のクローズ理由\nデフォルト: ""',
    'admin_preference_hintText_lineccRoomAutoCloseTag5_modeBoth': 'タイムアウトによる自動完了時のクローズ理由\nデフォルト: ""',
    'admin_preference_hintText_lineccRoomAutoCloseTag5_modeOperator': 'オペレーターモードの場合は原則として"timeout"を指定してください',
    'admin_preference_hintText_lineccSenderProfileEnable': 'Icon/Nickname Switch APIを使用する場合TRUEにして下さい',
    'admin_preference_hintText_lineccServiceCode': '※LINE developers記載されておりません。LINE社から別途取得下さい',
    'admin_preference_hintText_lineccSwitcherSecret': 'WebHookのシグネチャーチェックのための値',
    'admin_preference_hintText_lineccSwitchNoteModule': 'モードを選択してください。',
    'admin_preference_hintText_lineChannelId': 'LINE Business ConnectのチャネルID\nデフォルト: ""',
    'admin_preference_hintText_lineChannelSecret': 'LINEから受信したデータのシグネチャーをチェックするためのキー\nデフォルト: ""',
    'admin_preference_hintText_lineCheckSignature': 'LINEから受信したデータのシグネチャーをチェックするかどうか \nデバッグ用です。本番運用時はtrueにしてください\nデフォルト: true',
    'admin_preference_hintText_lineContentMaxTestUser': 'LINE配信コンテンツのテスト送信先の最大ユーザー数\nデフォルト: 10',
    'admin_preference_hintText_lineForeignUserCheckUrl': 'LINE Business ConnectユーザーのIDを元に外部サーバーから外部連携IDを取得するURL\n例: "https://example.com/getLineUserId"\nデフォルト: ""',
    'admin_preference_hintText_lineUserDataClearAtBlock':'LINEユーザーがブロックしたらデータを削除する\nデフォルト: true',
    'admin_preference_hintText_lineworksClientId': 'https://developers.worksmobile.com/jp/console/openapi/v2/app/list/viewでアプリの詳細を確認してください',
    'admin_preference_hintText_lineworksServiceAccount': 'https://developers.worksmobile.com/jp/console/openapi/v2/app/list/viewでアプリの詳細を確認してください',
    'admin_preference_hintText_lineworksPrivateKeyForApi2': 'https://developers.worksmobile.com/jp/console/openapi/v2/app/list/viewでアプリの詳細を確認してください',
    'admin_preference_hintText_lineworksClientSecret': 'https://developers.worksmobile.com/jp/console/openapi/v2/app/list/viewでアプリの詳細を確認してください',
    'admin_preference_hintText_lineworksBotSecret': 'https://developers.worksmobile.com/jp/console/bot/info/{botId}でBotの詳細を確認してください',
    'admin_preference_hintText_list_joinGroups': '割り当てグループを指定してください',
    'admin_preference_hintText_list_joinOperators': '参加させるユーザーを指定してください',
    'admin_preference_hintText_list_joinPermitLevels': '参加させる権限レベルを指定してください',
    'admin_preference_hintText_maxOperatorsRoom': 'グループ割り当てされていない状態で有人対応可能な最大ルーム数\nグループ割り当てされる場合はグループ毎の設定に従います\nこの設定で上限を無制限にしたい場合は-1を指定してください\nこの項目は[オペレーション設定 > 対応モード設定]と[モビエージェント設定 > 自動応答設定]で共通です\nデフォルト: -1'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'admin_preference_hintText_maxOperatorsRoomPerUser': 'オペレーターのステータスに応じて有人対応可能なルーム数を変化させる場合に利用します。\nグループ割り当てされる場合はグループ毎の設定に従います\nこの設定で上限を無制限にしたい場合は-1を指定してください\nデフォルト: -1',
    'admin_preference_hintText_mobiGuestApiUrl': '呼び出し先URL',
    'admin_preference_hintText_mobiGuestComment': 'チャネルの説明を記載してください\nここに登録された文言がオペレーター画面の流入元に表示されます',
    'admin_preference_hintText_mobiGuestForeignUserCheckUrl': '外部サーバーから外部連携IDを取得するURL\n例: "https://example.com/getLineUserId"\nデフォルト: ""',
    'admin_preference_hintText_mobiGuestSecretKey': '署名チェック用キー',
    'admin_preference_hintText_msbotAccessToken': 'アクセストークン',
    'admin_preference_hintText_msbotAccessTokenExpire': 'アクセストークン有効期限',
    'admin_preference_hintText_msbotAccessTokenUpdateDate': 'アクセストークン更新日時',
    'admin_preference_hintText_msbotAppId': 'アプリケーション ID',
    'admin_preference_hintText_msbotAppSecret': 'アプリケーション シークレット',
    'admin_preference_hintText_msbotComment': 'チャネルの説明を記載してください\nここに登録された文言がオペレーター画面の流入元に表示されます',
    'admin_preference_hintText_msbotForeignUserCheckUrl': '外部サーバーから外部連携IDを取得するURL\n例: "https://example.com/getLineUserId"\nデフォルト: ""',
    'admin_preference_hintText_msbotSendMessageTextFormat': '送信先チャネルに応じたメッセージ変換方式の設定\n（システム管理者に指示された場合のみ設定してください）',
    'admin_preference_hintText_operatorsEnable': 'オフを選択すると新規ルームは強制的に自動応答になります\nグループ割り当てされる場合はグループ毎の設定に従います',
    'admin_preference_hintText_param_operatorStatusEnable': '',
    'admin_preference_hintText_param_operatorStatusList': '',
    'admin_preference_hintText_roomAutoCloseMsg_autoReply': '自動応答中にルームがタイムアウトしたときに送られるメッセージ\nデフォルト: 未設定\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_roomAutoCloseMsg_operator': '有人対応中にルームがタイムアウトしたときに送られるメッセージ\nデフォルト: 未設定\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_roomAutoCloseMsg_wait': '有人対応切替待機中にルームがタイムアウトしたときに送られるメッセージ\nデフォルト: 未設定\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%、%%WAIT_NUM%%',
    'admin_preference_hintText_roomAutoCloseTag2': 'タイムアウト自動完了時の分類区分1(コード)\n未設定時は更新されない\nデフォルト: 未設定\ntag2として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomAutoCloseTag3': 'タイムアウト自動完了時の分類区分2(コード)\n未設定時は更新されない\nデフォルト: 未設定\ntag3として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomAutoCloseTag5': 'タイムアウト自動完了時のクローズ理由\n未設定時は更新されない\nデフォルト: timeout',
    'admin_preference_hintText_roomAutoCloseTag6': 'タイムアウト自動完了時の分類区分3(コード)\n未設定時は更新されない\nデフォルト: 未設定\ntag6として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomAutoCloseTag66': 'タイムアウト自動完了時の分類区分1(ラベル)\n未設定時は更新されない\nデフォルト: 未設定\ntag66として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomAutoCloseTag67': 'タイムアウト自動完了時の分類区分2(ラベル)\n未設定時は更新されない\nデフォルト: 未設定\ntag67として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomAutoCloseTag68': 'タイムアウト自動完了時の分類区分3(ラベル)\n未設定時は更新されない\nデフォルト: 未設定\ntag68として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomAutoCloseTag7': 'タイムアウト自動完了時のオペレーターメモ\n未設定時は更新されない\nデフォルト: 未設定',
    'admin_preference_hintText_roomAutoCloseTimeout_auto': 'サーバー側でのルームタイムアウト自動完了を行う時間 (秒)\n最大 = 604800(7日間) 未設定および0は最大と同じ\nタイムアウトは、最後のメッセージ時刻、保留終了時刻、ルームクローズ時刻、ルーム作成時刻を基準にして判定する。\nまた、保留中は完了しない。',
    'admin_preference_hintText_roomAutoCloseTimeout_operator': 'サーバー側でのルームタイムアウト自動完了を行う時間 (秒)\n最大 = 604800(7日間) 未設定および0は最大と同じ\nタイムアウトは、最後のメッセージ時刻、保留終了時刻、ルームクローズ時刻、ルーム作成時刻を基準にして判定する。\nまた、保留中は完了しない。',
    'admin_preference_hintText_roomAutoCloseTimeout_wait': 'サーバー側でのルームタイムアウト自動完了を行う時間 (秒)\n最大 = 604800(7日間) 未設定および0は最大と同じ\nタイムアウトは、最後のメッセージ時刻、有人対応切替待機開始時刻を基準にして判定する。',
    'admin_preference_hintText_roomExtraTag2InitialValue': '問合せルーム作成時の分類区分1の初期値(コード)\nデフォルト: ""\ntag2として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomExtraTag3InitialValue': '問合せルーム作成時の分類区分2の初期値(コード)\nデフォルト: ""\ntag3として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomExtraTag6InitialValue': '問合せルーム作成時の分類区分3の初期値(コード)\nデフォルト: ""\ntag6として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomExtraTag66InitialValue': '問合せルーム作成時の分類区分1の初期値(ラベル)\nデフォルト: ""\ntag66として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomExtraTag67InitialValue': '問合せルーム作成時の分類区分2の初期値(ラベル)\nデフォルト: ""\ntag67として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomExtraTag68InitialValue': '問合せルーム作成時の分類区分3の初期値(ラベル)\nデフォルト: ""\ntag68として保存されます\n分類区分設定と整合性のとれた値を設定して下さい',
    'admin_preference_hintText_roomIncidentNumberFormat': '以下の変数が利用可能です:\n%%YYYY%%(西暦4桁), %%YY%%(西暦2桁), %%MM%%(月2桁), %%DD%%(日2桁)\n%%hh%%(時2桁), %%mm%%(分2桁), %%ss%%(秒2桁)\n%%SEQNUM3%%(連番3桁), %%SEQNUM4%%(連番4桁), %%SEQNUM5%%(連番5桁)\n連番はルーム名に使用する連番と共通の連番を使用します',
    'admin_preference_hintText_roomSequenceNumberResetByDay': 'ルームインシデント番号やルーム名に使用するシーケンス番号を毎日リセットする',
    'admin_preference_hintText_roomTag26Update': 'trueにした場合問い合わせルームが完了した時に\nゲストユーザーの外部連携IDをルームの属性として保存します\nデフォルト: false',
    'admin_preference_hintText_snsRawUserIdSearchEnable': 'trueにした場合SNSのIDで検索することができるようになります\nデフォルト: false',
    'admin_preference_hintText_snsUserProfileNullAvatar': 'trueにした場合SNSユーザーのアバター画像を保存しません\nデフォルト: false',
    'admin_preference_hintText_snsUserProfileNullName': 'trueにした場合SNSユーザーの名前を保存しません\nデフォルト: false',
    'admin_preference_hintText_start': '開始時刻 (00:00 ～ 24:00。現在時刻が開始時刻以上で終了時刻未満の場合にオペレーターが対応可能になる)',
    'admin_preference_hintText_useElasticSearch': 'ドメイン全体での新しいあいまい検索のON/OFFの設定です',
    'admin_preference_hintText_waitEndAutoReply_autoSwitchMsg': '有人対応切替待機状態から有人対応になった場合に送信されるメッセージ\n利用可能変数: %%ROOM_ID%%、%%GUEST_ID%%、%%URL_ENCODED_GUEST_ID%%',
    'admin_preference_hintText_waitEndAutoReply_enable': '有効にした場合、閾値超過が原因で有人対応切替失敗時にルームを有人対応切替待機状態にします\nデフォルト: false',
    'admin_preference_hintText_waitEndAutoReply_roomLimit': '有人対応切替待機状態にすることができるルーム数\nデフォルト(未指定時): 100、最大: 100',
    'admin_preference_hintText_webhookUrlRoomClose': 'onRoomCloseイベントがPOSTされます',
    'admin_preference_hintText_webhookUrlRoomEndAutoReply': 'onRoomEndAutoReplyイベントがPOSTされます',
    'admin_preference_hintText_webhookUrlRoomFinish': 'onRoomFinishイベントがPOSTされます',
    'admin_preference_hintText_webhookUrlRoomStart': 'onRoomStartイベントがPOSTされます',
    'admin_preference_hintText_weeks': '曜日 (月曜:1 ～ 日曜:7)',
    //https://mobilus.backlog.jp/view/MBA-14811 ルームタグ31から60に加えてルームタグ200から249を追加
    //allowGuestUpdateRoomTag31_60はDBカラム名なので、そのままにします。
    'admin_preference_init_allowGuestUpdateRoomTag31_60': 'ゲストユーザーによる拡張ルームタグ更新',
    'admin_preference_init_callbackSignatureSecret': '署名用キー文字列',
    'admin_preference_init_foreignGuestUserServerUrl': '外部連携ログインチェックURL',
    'admin_preference_init_guestUserGreetingMsg': '挨拶メッセージ',
    'admin_preference_init_roomAutoCloseMsg_autoReply': 'タイムアウトメッセージ(自動応答時)',
    'admin_preference_init_roomAutoCloseMsg_operator': 'タイムアウトメッセージ(有人対応時)',
    'admin_preference_init_roomAutoCloseTag2': 'タイムアウト時の分類区分1(コード)',
    'admin_preference_init_roomAutoCloseTag3': 'タイムアウト時の分類区分2(コード)',
    'admin_preference_init_roomAutoCloseTag5': 'タイムアウト時のクローズ理由',
    'admin_preference_init_roomAutoCloseTag6': 'タイムアウト時の分類区分3(コード)',
    'admin_preference_init_roomAutoCloseTag66': 'タイムアウト時の分類区分1(ラベル)',
    'admin_preference_init_roomAutoCloseTag67': 'タイムアウト時の分類区分2(ラベル)',
    'admin_preference_init_roomAutoCloseTag68': 'タイムアウト時の分類区分3(ラベル)',
    'admin_preference_init_roomAutoCloseTag7': 'タイムアウト時のオペレーターメモ',
    'admin_preference_init_roomAutoCloseTimeout_auto': '無応答ルームタイムアウト秒(自動応答時)',
    'admin_preference_init_roomAutoCloseTimeout_operator': '無応答ルームタイムアウト秒(有人対応時)',
    'admin_preference_init_roomExtraTag2InitialValue': '分類区分1の初期値(コード)',
    'admin_preference_init_roomExtraTag3InitialValue': '分類区分2の初期値(コード)',
    'admin_preference_init_roomExtraTag6InitialValue': '分類区分3の初期値(コード)',
    'admin_preference_init_roomExtraTag66InitialValue': '分類区分1の初期値(ラベル)',
    'admin_preference_init_roomExtraTag67InitialValue': '分類区分2の初期値(ラベル)',
    'admin_preference_init_roomExtraTag68InitialValue': '分類区分3の初期値(ラベル)',
    'admin_preference_init_roomIncidentNumberFormat': 'ルームインシデント番号のフォーマット',
    'admin_preference_init_roomSequenceNumberResetByDay': 'シーケンス番号のリセット',
    'admin_preference_init_snsRawUserIdSearchEnable': '外部SNS ID保存',
    'admin_preference_init_snsUserProfileNullAvatar': '外部SNS アバター保存無効化',
    'admin_preference_init_snsUserProfileNullName': '外部SNS 名前保存無効化',
    'admin_preference_init_useElasticSearch': '新しいあいまい検索',
    'admin_preference_init_webSocketCorsNoCheck': 'CORS利用時にブラウザのCORSエラー無効設定でもチャット可能にする',
    'admin_preference_init_maxOperatorsRoomByWorkingTime': '営業時間ごとにオペレーター対応可能数設定可能にする',
    'admin_preference_init_miscStartChatUser': 'web小窓に有人対応を開始する',

    'admin_preference_label_autoReplyEndGlobalLock': 'オペレーター対応ルーム数計算の厳密化',
    'admin_preference_label_corsAllowOrigins': '許可オリジン',
    'admin_preference_label_lineAutoReplyBranchButton': 'クイックリプライによる自動応答分岐',
    'admin_preference_label_lineUserDataClearAtBlock': 'ブロック時のデータ消去',
    'admin_preference_label_param_privacyMaskingPreference': '名前',
    'admin_preference_label_param_privacyMaskingPreference_name': '名前',
    'admin_preference_label_param_privacyMaskingPreference_address': '住所',
    'admin_preference_label_param_privacyMaskingPreference_email': 'メールアドレス',
    'admin_preference_label_param_privacyMaskingPreference_creditCard': 'クレジットカード番号',
    'admin_preference_label_param_privacyMaskingPreference_phone': '電話番号',
    'admin_preference_label_param_privacyMaskingPreference_myNumber': 'マイナンバー',
    'admin_preference_label_param_privacyMaskingPreference_zipCode': '郵便番号',
    'admin_preference_label_param_captureDisplayMedia': 'ディスプレイキャプチャを取得する',
    'admin_preference_label_param_captureUserMedia': 'カメラキャプチャを取得する',
    'admin_preference_label_param_mfa_endpoint_url': 'APIエンドポイントURL',
    'admin_preference_label_param_llm_room_summary_endpoint_url': 'APIエンドポイントURL',
    'admin_preference_label_param_mfa_domain_id': 'ドメインID',
    'admin_preference_label_param_mfa_api_key': 'API キー',
    'admin_preference_label_param_llm_room_summary_key': 'API キー',
    'admin_preference_label_param_crm_connect_sf_domain_id': 'ドメインID',
    'admin_preference_label_param_crm_connect_sf_api_key': 'APIキー',
    'admin_preference_label_param_crm_connect_sf_endpoint_url': 'APIエンドポイントURL',
    'admin_preference_crm_connect_sf_endpoint_url_hint': '※ 末尾のスラッシュは記入しないでください',
    'admin_preference_label_param_crm_connect_sf_iframe_endpoint_url': 'iframeエンドポイントURL',
    'admin_preference_crm_connect_sf_iframe_endpoint_url_hint': '※ 末尾のスラッシュは記入しないでください',
    'admin_preference_label_param_crm_connect_sf_mail': 'APIユーザーメールアドレス',
    'admin_preference_label_param_crm_connect_sf_passwd': 'APIユーザーパスワード',
    'admin_preference_label_param_crm_connect_sf_default_group_id': 'デフォルト設定 グループID',
    'admin_preference_label_param_crm_connect_sf_domain_id_min_length_error': '4文字以上入力してください。',
    'admin_preference_label_param_mfa_type': '認証通知方法',
    'admin_preference_label_param_mfa_permits': '権限別多要素認証設定',
    'admin_preference_label_roomTag26Update': '外部連携IDをルームの属性として保存',
    'admin_preference_label_bedoreWebEndpoint': 'エンドポイント',
    'admin_preference_label_bedoreWebSecretKey': 'Secret Key',
    'admin_preference_label_bedoreWebForeignUserCheckUrl': 'BEDORE for WEBユーザー外部連携ID変換URL',
    'admin_preference_lineCustomerConnect_lineccAccessToken': 'アクセストークン',
    'admin_preference_lineCustomerConnect_lineccAccessTokenExpire': 'アクセストークン 有効期限',
    'admin_preference_lineCustomerConnect_lineccAccessTokenUpdateDate': 'アクセストークン 更新日時',
    'admin_preference_lineCustomerConnect_lineccChannelId': 'Channel ID',
    'admin_preference_lineCustomerConnect_lineccChannelSecret': 'Channel Secret',
    'admin_preference_lineCustomerConnect_lineccCreateGenericPostback': '汎用Postback変換を有効にする',
    'admin_preference_lineCustomerConnect_lineccDestinationId': '切替先のDestination ID',
    'admin_preference_lineCustomerConnect_lineccMyDestinationId': '自分のDestination ID',
    'admin_preference_lineCustomerConnect_lineccForeignUserCheckUrl': 'ユーザー外部連携ID変換URL',
    'admin_preference_lineCustomerConnect_lineccMode_option_bot': 'Botモード',
    'admin_preference_lineCustomerConnect_lineccMode_option_both': '両方モード',
    'admin_preference_lineCustomerConnect_lineccMode_option_operator': 'オペレーターモード',
    'admin_preference_lineCustomerConnect_lineccMode': 'Switcher API動作モード',
    'admin_preference_lineCustomerConnect_lineccSenderProfileEnable': 'Icon/Nickname Switch API',
    'admin_preference_lineCustomerConnect_lineccServiceCode': 'Service Code',
    'admin_preference_lineCustomerConnect_lineccSwitcherSecret': 'Switcher Secret',
    'admin_preference_lineCustomerConnect_lineccSwitchNoteModule_option_default': 'デフォルト',
    'admin_preference_lineCustomerConnect_lineccSwitchNoteModule_option_bedoreV2': 'BEDORE V2',
    'admin_preference_lineCustomerConnect_lineccSwitchNoteModule': 'Switcher API Noteフォーマット',
    'admin_preference_lineMessageAPI_line2AccessToken': 'アクセストークン',
    'admin_preference_lineMessageAPI_line2AccessTokenExpire': 'アクセストークン 有効期限',
    'admin_preference_lineMessageAPI_line2AccessTokenUpdateDate': 'アクセストークン 更新日時',
    'admin_preference_lineMessageAPI_line2AccessTokenUpdateEnable': 'アクセストークン 自動更新',
    'admin_preference_lineMessageAPI_line2ChannelId': 'Channel ID',
    'admin_preference_lineMessageAPI_line2ChannelSecret': 'Channel Secret',
    'admin_preference_lineMessageAPI_line2Comment': 'コメント',
    'admin_preference_lineMessageAPI_line2CreateGenericPostback': '汎用Postback変換を有効にする',
    'admin_preference_lineMessageAPI_line2ForeignUserCheckUrl': 'ユーザー外部連携ID変換URL',
    'admin_preference_lineMessageAPI_line2SenderProfileEnable': 'Icon/Nickname Switch API',
    'admin_preference_lineMessageAPI_line2ServiceCode': 'Service Code',
    'admin_preference_lineMessageAPI_upload_dialog_title': 'LINE公式アカウント連携設定アップロード',
    'admin_preference_lineMessageAPI_upload_dialog_desc': 'アップロードする設定ファイルは以下のようなフォーマットです\n\nファイル形式 : csv TAB区切り (UTF-16 BOM付)\nファイル例 :',
    'admin_preference_lineMessageAPI_upload_dialog_error_empty': 'エラー: row[{index}].{key}を指定してください',
    'admin_preference_lineMessageAPI_upload_dialog_error_bool': 'エラー: row[{index}].{key}は "true" または "false" である必要があります',
    'admin_preference_lineMessageAPI_upload_dialog_error_required': 'エラー: {key}は必須項目です',
    'admin_preference_lineWorks_status': 'ステータス',
    'admin_preference_lineWorks_erroroMsg': '値を入力してください',
    'admin_preference_lineWorks_lineworksServerId': 'Server ID',
    'admin_preference_lineWorks_lineworksClientId': 'Client ID',
    'admin_preference_lineWorks_lineworksClientSecret': 'Client Secret',
    'admin_preference_lineWorks_lineworksServiceAccount': 'Service Account',
    'admin_preference_lineWorks_lineworksBotSecret': 'Bot Secret',
    'admin_preference_lineWorks_lineworksPrivateKeyForApi2': '認証キー',
    'admin_preference_mobiGuest_mobiGuestApiUrl': 'Api url',
    'admin_preference_mobiGuest_mobiGuestComment': 'コメント',
    'admin_preference_mobiGuest_mobiGuestForeignUserCheckUrl': '#{mobiguest}ユーザー外部連携ID変換URL'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'admin_preference_mobiGuest_mobiGuestSecretKey': '署名キー',
    'admin_preference_msBot_msbotAccessToken': 'アクセストークン',
    'admin_preference_msBot_msbotAccessTokenExpire': 'アクセストークン 有効期限',
    'admin_preference_msBot_msbotAccessTokenUpdateDate': 'アクセストークン 更新日時',
    'admin_preference_msBot_msbotAppId': 'アプリケーション ID',
    'admin_preference_msBot_msbotAppSecret': 'アプリケーション シークレット',
    'admin_preference_msBot_msbotComment': 'コメント',
    'admin_preference_msBot_msbotForeignUserCheckUrl': 'MicroSoft Bot Framework ユーザー外部連携ID変換URL',
    'admin_preference_msBot_msbotSendMessageTextFormat': '送信フォーマット',
    'admin_preference_page_desc_externalCmsLink': 'モビシリーズの各システムへのURLはシステム共通です'.replace('モビシリーズ', window.KonnectPlusServerJSON.service.mobiseries || 'モビシリーズ'),
    'admin_preference_page_title_allowIps': 'IPアドレス制限',
    'admin_preference_page_title_allowIps_api': '外部からのAPI呼び出しに関するIP制限',
    'admin_preference_page_title_allowIps_login': 'Webログインに関するIP制限',
    'admin_preference_page_title_apiKeys': 'APIキー管理',
    'admin_preference_page_title_apiLimits': 'API呼び出し制限',
    'admin_preference_page_title_accountInfo': '契約者情報（アカウント情報）',
    'admin_preference_page_title_agent': 'モビエージェント 詳細設定'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'admin_preference_page_title_autoReply': '自動応答設定',
    'admin_preference_page_title_autoReplyModule': '自動応答モジュール設定',
    'admin_preference_page_title_autoReplyModuleBySns': '流入元別自動応答モジュール設定',
    'admin_preference_page_title_cors': 'CORS設定',
    'admin_preference_page_title_domain': 'ドメイン設定',
    'admin_preference_page_title_externalCmsLink': '外部CMSリンク',
    'admin_preference_page_title_facebook': 'Facebook連携に関する設定',
    'admin_preference_page_title_initData': '初期設定',
    'admin_preference_page_title_bedoreWeb': 'BEDORE for WEB連携に関する設定',
    'admin_preference_page_title_line': 'LINE連携に関する設定',
    'admin_preference_page_title_lineCommon': 'LINE共通設定',
    'admin_preference_page_title_lineCustomerConnect': 'LINE公式アカウント（Switcher API利用あり）連携に関する設定',
    'admin_preference_page_title_lineMessageAPI': 'LINE公式アカウント連携に関する設定',
    'admin_preference_page_title_lineStamp': 'LINEスタンプ管理',
    'admin_preference_page_title_lineToken_add': 'トークン追加',
    'admin_preference_page_title_lineWorks': 'LINE Works2.0連携に関する設定',
    'admin_preference_page_title_mediaCapture': 'テレワーク機能',
    'admin_preference_page_title_mobiGuest': '#{mobiguest}連携に関する設定'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'admin_preference_page_title_msBot': 'Microsoft Bot Framework連携に関する設定',
    'admin_preference_page_title_style': 'スタイル設定',
    'admin_preference_page_title_suggestModule': 'サジェストモジュール設定',
    'admin_preference_page_title_sysadmOnly': 'システム管理者向け設定',
    'admin_preference_page_title_systemNotification': 'システム通知',
    'admin_preference_page_title_privacyMasking': '個人情報検出アラート',
    'admin_preference_page_title_mfa': '多要素認証接続情報',
    'admin_preference_page_title_guestMfa': 'ゲスト向け多要素認証接続情報',
    'admin_preference_page_title_llmSummary': '対話内容要約機能接続情報',
    'admin_preference_page_title_crmConnectSf': 'CRM Connect 設定',
    'admin_preference_page_title_webhook': 'Webhook設定',
    'admin_preference_page_title_work': 'モビワーク 詳細設定'.replace('モビワーク', window.KonnectPlusServerJSON.service.mobiwork || 'モビワーク'),
    'admin_preference_page_title_securePathURL': 'Secure Path連携先URL設定',
    'admin_preference_page_destination_URL': '連携先URL',
    'admin_preference_privacyMaskingPolicy_item': '個人情報の送信',
    'admin_preference_privacyMaskingPolicy_item_mark': '個人情報をマスキングして送信する',
    'admin_preference_privacyMaskingPolicy_item_block': '個人情報の送信を許可しない',
    'admin_preference_subtitle_privacyMaskingPreference': '抽出対象',
    'admin_preference_subtitle_privacyMaskingPolicy': '送信制御',
    'admin_preference_suggest_dialog_body_disable': 'サジェスト機能を無効にする',
    'admin_preference_suggest_dialog_body_enable': 'サジェスト機能を有効にする',
    'admin_callcenter_suggest_enable_dialog_title': 'サジェスト機能設定変更',
    'admin_preference_suggest_suggestDisable': 'サジェスト機能無効化',
    'admin_preference_suggestModuleList_dialog_title_delete_suggestModule': 'サジェストモジュール設定を削除してもよろしいですか。',
    'admin_preference_suggestModuleList_guestTypes_hint': 'このモジュールを実行する対象の流入元を選択してください。未指定の場合すべての流入元に適用されます。',
    'admin_preference_suggestModuleList_httpModule_url': 'URL',
    'admin_preference_suggestModuleList_httpModule_url_error': 'URLフォーマットが正しくありません',
    'admin_preference_suggestModuleList_httpModule_key': 'Key',
    'admin_preference_suggestModuleList_httpModule_key_error': 'キーは必須項目です',
    'admin_preference_suggestModuleList_moduleAvatar': 'アバター',
    'admin_preference_suggestModuleList_moduleAvatar_error': '正しいURL(SSL必須)もしくは空欄を設定してください',
    'admin_preference_suggestModuleList_moduleAvator_hint': '管理用のアバターを設定します',
    'admin_preference_suggestModuleList_moduleLabel': 'ラベル',
    'admin_preference_suggestModuleList_moduleLabel_error': 'ラベルは必須項目です',
    'admin_preference_suggestModuleList_moduleLabel_hint': '管理用のラベルを設定します',
    'admin_preference_suggestModuleList_moduleName': 'モジュール',
    'admin_preference_suggestModuleList_moduleName_hint': 'モジュールを選択してください',
    'admin_preference_suggestModuleList_operatorGroups': 'グループ',
    'admin_preference_suggestModuleList_operatorGroups_hint': 'この設定を適用するグループを選択してください。未指定の場合すべてのグループに適用されます。',
    'admin_preference_suggestModuleList_subHeader_condition': '詳細設定',
    'admin_preference_suggestModuleList_subHeader_module': 'モジュール設定',
    'admin_preference_suggestModuleList_types': 'タイプ',
    'admin_preference_suggestModuleList_types_guest': 'ゲスト入力時のサジェスト',
    'admin_preference_suggestModuleList_types_hint': 'この設定を適用するタイプを選択してください',
    'admin_preference_suggestModuleList_types_operator': 'オペレーター入力時のサジェスト',
    'admin_preference_suggestModuleList_types_operator2': 'オペレーター回答候補のサジェスト',
    'admin_preference_webhook_callbackSignatureSecret': '署名用キー文字列',
    'admin_preference_webhook_webhookUrlRoomClose': 'ルーム終了通知URL',
    'admin_preference_webhook_webhookUrlRoomEndAutoReply': '自動応答終了通知URL',
    'admin_preference_webhook_webhookUrlRoomFinish': 'ルーム完了通知URL',
    'admin_preference_webhook_webhookUrlRoomStart': 'ルーム開始通知URL',
    'admin_publicFile_floatingLabel_fileName': 'ファイル名には/を含めて階層を作ることができます',
    'admin_publicFile_form_desc_search_public_file': '入力値はフルパスに対して前方一致検索されます',
    'admin_publicFile_form_title_search_public_file': '共通ファイル検索',
    'admin_publicFile_label_exposeToAll': 'exposeToAllを有効にする',
    'admin_publicFile_label_file': 'ファイル',
    'admin_publicFile_label_noNeedAuth': 'noNeedAuthを有効にする',
    'admin_publicFile_page_title_new': '共通ファイル追加',
    'admin_room_bgr': '背景画像',
    'admin_room_create_date': '作成時刻',
    'admin_room_detail_title': 'ルーム詳細情報',
    'admin_room_form_desc_closed_room_search': 'クローズ済みルーム検索を有効にした場合、指定したユーザーIDがOPENしたルームを検索します',
    'admin_room_form_desc_search': '条件を入力して検索してください',
    'admin_room_form_desc_show': 'LINE連携ルーム情報を変更することはできません',
    'admin_room_form_title_public_room_extra_info': '問合せ情報詳細',
    'admin_room_form_title_search': 'ユーザー毎の所属ルームを検索します',
    'admin_room_form_title_show': 'ルーム情報',
    'admin_room_set_pq_button': '主質問にコピーする',
    'admin_room_set_pa_button': '主回答にコピーする',
    'admin_room_hintText_guest_user_selected': 'ゲストユーザーの問合せは最新のルームのみが表示されます。すでに完了したルームを検索する場合は完了案件検索を使用してください。',
    'admin_room_icon': 'アイコン',
    'admin_room_include_closed_rooms': '終了済みルームを検索する',
    'admin_room_management_management_title': 'ルーム管理',
    'admin_room_members': 'メンバー',
    'admin_room_msg_management': 'メッセージ一覧',
    'admin_room_primary_answer': '主回答',
    'admin_room_primary_question': '主質問',
    'admin_room_room_id': 'ルーム ID',
    'admin_room_room_title': 'ルームタイトル',
    'admin_room_room_type_11': '1-1 チャット',
    'admin_room_room_type_1n': 'グループ チャット',
    'admin_room_room_type_public': 'パブリックルーム',
    'admin_room_room_type': 'タイプ',
    'admin_room_search_closed_comment': 'Closed Comment',
    'admin_room_search_closed_reason': 'Closed Reason',
    'admin_room_search_first_active_datetime': 'First Active Datetime',
    'admin_room_search_from_date': 'From Datetime',
    'admin_room_search_last_active_datetime': 'Last Active Datetime',
    'admin_room_search_room_name': 'Room Name',
    'admin_room_search_to_date': 'To Datetime',
    'admin_room_search_total_msg': 'Total of Messages',
    'admin_room_snack_plusId_required': 'ユーザーが指定されていません',
    'admin_room_snack_roomId_required': 'ルームIDが指定されていません',
    'admin_room_system_message_create_success': '追加に成功しました',
    'admin_room_update_error': '更新に失敗しました',
    'admin_room_update_management_title': 'ルーム変更',
    'admin_room_update_successfull': '更新に成功しました',
    'admin_room_user_id': 'ユーザ ID',
    'admin_room_operator_change': 'ルーム担当者変更',
    'admin_sentiment_approve_successful': '承認しました',
    'admin_sentiment_approve_explanation': '下記のメッセージに設定された感情を承認してもよろしいですか？',
    'admin_sentiment_reject_successful': '却下しました',
    'admin_sentiment_reject_explanation': '下記のメッセージに設定された感情を却下してもよろしいですか？',
    'admin_sentiment_modify_successful': '修正しました',
    'admin_sentiment_modify_explanation': '一覧の感情アイコンをクリックして、メッセージの感情を修正してください。\n選択肢から感情を選び、一括で変更することもできます。',
    'admin_edit_alert_config_title': '閾値・アラート設定',
    'admin_edit_alert_config_edit_guest_keyword': 'アラートキーワード設定(ゲスト)',
    'admin_edit_alert_config_edit_guest_keyword_title': 'ゲストのメッセージで特定のキーワードが指定回数発言されたら、アラート通知します。',
    'admin_edit_alert_config_edit_operator_keyword': 'アラートキーワード設定(オペレーター)',
    'admin_edit_alert_config_edit_operator_keyword_title': 'オペレーターのメッセージで特定のキーワードが指定回数発言されたら、アラート通知します。',
    'admin_edit_alert_config_edit_keyword_keywords': 'キーワードリスト',
    'admin_edit_alert_config_edit_sentiment_threshold': '閾値設定',
    'admin_edit_alert_config_edit_sentiment_threshold_title': 'ルーム感情が閾値を超えたら、アラート通知します。',
    'admin_edit_alert_config_edit_sentiment_threshold_tooltip': 'ルームのメッセージの感情を分析しそのルーム全体を通したゲストの感情分析スコアを基に\nモニタリング画面該当ルームにアラート通知します。',
    'admin_edit_alert_config_edit_sentiment_threshold_score': 'ルーム全体の感情分析スコア',
    'admin_edit_alert_config_file_encoding': '文字コード : UTF-8',
    'admin_sentiment_alert_title': '閾値・アラート設定',
    'admin_snippets_label_add_category': 'カテゴリー追加',
    'admin_snippets_label_add_line': '文言追加',
    'admin_snippets_label_body': '本文',
    'admin_snippets_label_label': 'ラベル',
    'admin_snippets_label_remove_line': '文言削除',
    'admin_snippets_label_variable_example': '表示例',
    'admin_snippets_label_variable_key': '変数表記',
    'admin_snippets_label_variable_name': '変数名',
    'admin_snippets_label_variables_date_YYYY/MM/DD': '現在日（西暦）',
    'admin_snippets_label_variables_date_HH:mm:ss': '現在時刻',
    'admin_snippets_label_variables_guest_name': 'ゲストユーザー名',
    'admin_snippets_label_variables_operator_name': 'オペレーター名(フルネーム)',
    'admin_snippets_label_variables_operator_profile_firstName': 'オペレーター 名',
    'admin_snippets_label_variables_operator_profile_firstNameKana': 'オペレーター 名(カナ) ',
    'admin_snippets_label_variables_operator_profile_firstNameAlphabet': 'オペレーター 名(アルファベット)',
    'admin_snippets_label_variables_operator_profile_lastName': 'オペレーター 姓',
    'admin_snippets_label_variables_operator_profile_lastNameKana': 'オペレーター 姓(カナ)',
    'admin_snippets_label_variables_operator_profile_lastNameAlphabet': 'オペレーター 姓(アルファベット)',
    'admin_snippets_label_variables_operator_profile_organization': 'オペレーター 組織',
    'admin_snippets_label_variables_operator_profile_department': 'オペレーター 部署',
    'admin_snippets_label_variables_operator_profile_departmentKana': 'オペレーター 部署(カナ)',
    'admin_snippets_label_variables_operator_profile_departmentPhone': 'オペレーター 部署TEL',
    'admin_snippets_label_variables_operator_profile_departmentFax': 'オペレーター 部署FAX',
    'admin_snippets_label_variables_operator_profile_position': 'オペレーター 役職',
    'admin_snippets_label_variables_operator_profile_personalPhone': 'オペレーター 個人連絡先',
    'admin_snippets_label_variables_room_name': 'ルーム名',
    'admin_snippets_label_variables_room_id': 'ルームID',
    'admin_snippets_label_variables_room_tag72': 'インシデント番号',
    'admin_snippets_label_variables_extra_roomTags': '拡張ルームタグ',
    'admin_snippets_label_variables_extra_guestAttributes': '拡張ゲスト属性',
    'admin_snippets_label_greeting_msg': 'あいさつメッセージ',
    'admin_snippets_label_greeting_msg_desc' : 'あいさつメッセージを登録すると、オペレータがルーム対応開始した直後にメッセージを送信することができます',
    'admin_snippets_sub_title': '編集',
    'admin_snippets_title': '定型文',
    'admin_snippets_usage_desc': '定型文の本文で変数をご利用いただけます',
    'admin_snippets_usage_link': '変数一覧',
    'admin_stamps_dialog_desc': '幅400 高さ300が望ましいサイズです。pngフォーマット以外は正常に出力されない可能性があります',
    'admin_stamps_dialog_title': '画像を選択してください',
    'admin_stamps_error_file_must_be_png': 'スタンプ画像はpng形式を使用してください',
    'admin_stamps_error_file_must_be_zip': 'アップロードにはzipファイルを使用してください',
    'admin_stamps_error_file_notfound': 'スタンプ定義ファイルが見つかりません',
    'admin_stamps_form_title_edit': 'スタンプ編集',
    'admin_stamps_notice_tmp_import_successfull': '一時アップロードに成功しました。プレビューを確認し問題なければ一括追加を行ってください',
    'admin_stamps_page_title_edit': 'スタンプを1つずつ編集する',
    'admin_stamps_page_title_import': 'スタンプをアップロードする',
    'admin_stamps_page_title_index': 'スタンプ',
    'admin_statistic_average_contact_time': '平均対応時間',
    'admin_statistic_contact_time': '対応時間',
    'admin_statistic_max_contact_time': '最大対応時間',
    'admin_statistic_min_contact_time': '最小対応時間',
    'admin_statistic_num_of_contacts': '問合せ数',
    'admin_statistic_num_of_messages': 'メッセージ数',
    'admin_statistic_num_of_rooms': 'ルーム数',
    'admin_statistic_target_domain': '全体',
    'admin_statistic_target': '集計対象',
    'admin_statistic_total_average_messages': '平均問合せメッセージ数',
    'admin_statistic_total_average_time': '平均対応時間',
    'admin_statistic_total_contact_messages': '総コンタクトメッセージ数',
    'admin_statistic_total_contact_rooms': '総コンタクトルーム数',
    'admin_statistic_total_contact_time': '総対応時間',
    'admin_statistic_total_contacts': '総問合せ数',
    'admin_statistic_total_group_messages': '総グループメッセージ数',
    'admin_statistic_total_group_rooms': '総グループルーム数',
    'admin_statistic_total_messages': '総メッセージ数',
    'admin_statistic_total_public_messages': '総問合せメッセージ数',
    'admin_statistic_total_rooms': '総ルーム数',
    'admin_statistic': '統計情報',
    'admin_style_form_title_edit': '直接編集',
    'admin_style_form_title_upload': 'ファイルをアップロードして登録',
    'admin_style_management': 'スタイル編集',
    'admin_sysconf_hint_logWatcherMailTo': '3つまで登録できます',
    'admin_sysconf_hint_logWatcherMailTitle': 'デフォルト: ""',
    'admin_sysconf_hint_logWatcherMailBodyForDomain': 'デフォルト: "", 利用可能変数: %%LOG_COUNT%%',
    'admin_sysconf_hint_logWatcherMailBody': 'デフォルト: "", 利用可能変数: %%DOMAIN_ID%%, %%LOG_COUNT%%',
    'admin_sysconf_hint_logWatcherKeyLevels': '',
    'admin_sysconf_hint_logWatcherExcludeFilter:': '正規表現で指定します',
    'admin_sysconf_hint_mailSmtpStarttls': 'SMTP接続にSTARTTLSを使用します デフォルト: true',
    'admin_sysconf_hint_mailSmtpPort': 'SMTP接続に使用するポート デフォルト 587',
    'admin_sysconf_hint_mailSmtpHost': 'SMTP接続先のホスト名 未指定の場合はメール送信機能が無効 デフォルト ""',
    'admin_sysconf_hint_mailSmtpDebug': 'デバッグ出力の設定 デフォルト false',
    'admin_sysconf_hint_mailSmtpAuthUsername': '認証を使用する場合のユーザー名 デフォルト: ""',
    'admin_sysconf_hint_mailSmtpAuthPassword': '認証を使用する場合のパスワード デフォルト: ""',
    'admin_sysconf_hint_mailSmtpAuth': 'SMTP接続に認証を使用します デフォルト true',
    'admin_sysconf_hint_mailFromName': 'メール送信時の送信元ユーザーの名前 デフォルト: ""',
    'admin_sysconf_hint_mailFromAddress': 'メール送信時の送信元アドレス デフォルト: ""',
    'admin_sysconf_hint_mailCharset': 'メール送信時のタイトルと本文に使用する文字コード デフォルト: ISO-2022-JP',
    'admin_sysconf_hint_plus_passwdResetIntervalSec': '次回のパスワードリセットができるまでの秒数',
    'admin_sysconf_hint_plus_passwdResetLimitSec': '一時パスワードの有効秒',
    'admin_sysconf_hint_plus_passwdResetMailBody': 'パスワードリセットメールの本文',
    'admin_sysconf_hint_plus_passwdResetMailTitle': 'パスワードリセットメールのタイトル',
    'admin_system_json_hint_securePathURL': 'Secure Path 連携先環境のURLを入力します。\n例：https://user.securepath.mobilus.me（末尾のスラッシュは記入しないでください）',
    'admin_sysconf_key_apiAppCheckEnable': '署名チェックの有効化',
    'admin_sysconf_key_apiAppCheckIgnorePermitLevels': 'チェックをしないユーザーのパーミットレベル(csv指定)',
    'admin_sysconf_key_apiAppCheckSignatureKeys': '署名キー',
    'admin_sysconf_key_apiAppCheckSignatureKeys_key': 'ID',
    'admin_sysconf_key_apiAppCheckSignatureKeys_value': '署名キー',
    'admin_sysconf_key_logWatcherMailTo': '通知先メールアドレス',
    'admin_sysconf_key_logWatcherMailTitle': 'メールタイトル',
    'admin_sysconf_key_logWatcherMailBodyForDomain': 'メール本文(ドメイン管理者向け)',
    'admin_sysconf_key_logWatcherMailBody': 'メール本文(システム管理者向け)',
    'admin_sysconf_key_logWatcherKeyLevels': '監視レベル',
    'admin_sysconf_key_logWatcherExcludeFilter': '除外フィルター',
    'admin_sysconf_key_logWatcherExcludeFilter_regex': '正規表現',
    'admin_sysconf_key_logWatcherExcludeFilter_regexIgnoreCase': '正規表現で大文字小文字を無視する',
    'admin_sysconf_key_mailSmtpStarttls': 'STARTTLSを使用する',
    'admin_sysconf_key_mailSmtpPort': 'SMTP接続先ポート',
    'admin_sysconf_key_mailSmtpHost': 'SMTP接続先のホスト',
    'admin_sysconf_key_mailSmtpDebug': 'デバッグ出力',
    'admin_sysconf_key_mailSmtpAuthUsername': '認証用username',
    'admin_sysconf_key_mailSmtpAuthPassword': '認証用password',
    'admin_sysconf_key_mailSmtpAuth': '認証を使用する',
    'admin_sysconf_key_mailFromName': 'From名',
    'admin_sysconf_key_mailFromAddress': 'Fromメールアドレス',
    'admin_sysconf_key_mailCharset': '文字エンコーディング',
    'admin_sysconf_key_plus_passwdResetIntervalSec': 'パスワードリセット有効間隔',
    'admin_sysconf_key_plus_passwdResetLimitSec': '一時パスワード有効秒',
    'admin_sysconf_key_plus_passwdResetMailBody': 'パスワードリセットメール本文',
    'admin_sysconf_key_plus_passwdResetMailTitle': 'パスワードリセットメールタイトル',
    'admin_sysconf_key_publicFileContentTypeCheck': '',
    'admin_sysconf_key_publicFileContentTypeCheck_allowCharset': 'Charset指定を許可する',
    'admin_sysconf_key_publicFileContentTypeCheck_charsetList': '許可するCharset',
    'admin_sysconf_key_publicFileContentTypeCheck_extList': '許可する拡張子',
    'admin_sysconf_key_publicFileContentTypeCheck_sizeLimit': 'サイズ制限',
    'admin_sysconf_key_userFilesRestriction_allowedExtensions': '許可する拡張子',
    'admin_sysconf_key_requestLimitterDebugLog': 'デバッグログ',
    'admin_sysconf_key_selectableWidgetVersions': '選択可能な小窓のバージョン',
    'admin_system_json_key_securePathURL': 'Secure Path URL',
    'admin_sysconf_hint_selectableWidgetVersions': 'カンマ（,）区切りで指定する',
    'admin_sysconf_requestLimit_form_desc_root': '機能を有効にするにはkonnect.confの設定が必要です。',
    'admin_sysconf_uploadLimit_form_desc1': '制限を有効にしない場合はすべてのファイルアップロードが許可されます\n有効化した場合は以下に示すcontentTypeのみアップロード可能となります',
    'admin_sysconf_uploadLimit_form_desc2': 'アップロード時に指定するcontentType毎に\nサイズ制限(未指定時は制限なし)\n許可する拡張子(カンマ区切りで複数指定可能、未指定時は制限なし)を指定してください\n"application/json; charset=utf-8"のようにcharset指定を許可する場合は\n対象のcharset(カンマ区切りで複数指定可能、未指定時は制限なし)を指定してください',
    'admin_sysconf_userFilesRestriction_form_desc1': '制限を有効にしない場合はすべてのファイルアップロードが許可されます\n有効化した場合は以下に示す拡張子のファイルのアップロードを許可し、それ以外を禁止します',
    'admin_sysconf_userFilesRestriction_form_desc2': 'アップロードを許可する拡張子をコンマ区切りで入力してください',
    'admin_sysstat_form_desc': 'システム統計参照',
    'admin_sysstat_form_title': '',
    'admin_sysstat_page_title': 'システム統計',
    'admin_systemNotification_hintText_checkLogNotifyMailAddress': 'エラー発生時通知先メールアドレスを指定して下さい',
    'admin_systemNotification_hintText_checkLogPrintDetail': '',
    'admin_systemNotification_hintText_notfiyLogLevel_AUTH': '認証（ログイン失敗など）に関するログイベントを通知するミニマムログレベルを選択して下さい',
    'admin_systemNotification_hintText_notfiyLogLevel_CLIENT': 'クライアント操作(管理画面でのファイルダウンロードなど)に関するログイベントを通知するミニマムログレベルを選択して下さい',
    'admin_systemNotification_hintText_notfiyLogLevel_EVENT': 'その他ログイベントを通知するミニマムログレベルを選択して下さい',
    'admin_systemNotification_hintText_notfiyLogLevel_SYSTEM': 'システムに関するログイベントを通知するミニマムログレベルを選択して下さい',
    'admin_systemNotification_hintText_notfiyLogLevel_USER': 'ユーザーに関するログイベントを通知するミニマムログレベルを選択して下さい',
    'admin_template_column': 'カラム設定',
    'admin_template_column_hint': '画像の有無、タイトルの有無、アクションの数は、全てのカラムで統一してください',
    'admin_template_dialog_title_audio_add': 'オーディオメッセージ作成',
    'admin_template_dialog_title_audio_edit': 'オーディオメッセージ編集',
    'admin_template_dialog_title_file_add': 'ファイルメッセージ作成',
    'admin_template_dialog_title_file_edit': 'ファイルメッセージ編集',
    'admin_template_dialog_title_image_add': '画像メッセージ作成',
    'admin_template_dialog_title_image_edit': '画像メッセージ編集',
    'admin_template_dialog_title_map_add': 'ロケーションメッセージ作成',
    'admin_template_dialog_title_map_edit': 'ロケーションメッセージ編集',
    'admin_template_dialog_title_misc_add': 'その他メッセージ作成',
    'admin_template_dialog_title_misc_edit': 'その他メッセージ編集',
    'admin_template_dialog_title_template_add': 'テンプレートメッセージ作成',
    'admin_template_dialog_title_template_edit': 'テンプレートメッセージ編集',
    'admin_template_dialog_title_video_add': '動画メッセージ作成',
    'admin_template_dialog_title_video_edit': '動画メッセージ編集',
    'admin_template_errorText_altText': '代替テキストフィールド1 ~ 400字で入力して下さい',
    'admin_template_errorText_bData': 'ボタンデータフィールドは1 ~ 300字で入力して下さい',
    'admin_template_errorText_bText': 'ボタンテキストフィールドは1 ~ 300字で入力して下さい',
    'admin_template_errorText_bTitle': 'ボタンタイトルフィールドは1 ~ 20字で入力して下さい',
    'admin_template_errorText_bUrl': 'ボタンURLフィールドは1000字までのURL(http, https, line, telスキーマが利用可能)を入力して下さい',
    'admin_template_errorText_cImageUrl': 'カラム画像URLは1000字までのURL(SSL必須)または未指定で入力して下さい',
    'admin_template_errorText_contentAlternatives_must_be_array': '"contentAlternatives"フィールドは配列で指定する必要があります',
    'admin_template_errorText_cText': 'カラムテキストは1 ~ 60字で入力して下さい',
    'admin_template_errorText_cTitle': 'カラムタイトルは0 ~ 40字で入力して下さい',
    'admin_template_errorText_cUrl': 'カラムURLは1000字までのURL(SSL必須)または未指定で入力して下さい',
    'admin_template_errorText_t_must_be_snsSendMessage': '"t"フィールドは"snsSendMessage"としてください',
    'admin_template_errorText_title_required': 'タイトルは必須項目です',
    'admin_template_form_title_column_button': 'ボタン設定',
    'admin_template_form_title_column': 'カラム設定',
    'admin_template_label_address': 'アドレス',
    'admin_template_label_altText': '代替テキスト',
    'admin_template_label_columns_button_data': 'データ',
    'admin_template_label_columns_button_text': 'テキスト',
    'admin_template_label_columns_button_title': 'タイトル',
    'admin_template_label_columns_button_type_message': 'Message',
    'admin_template_label_columns_button_type_postback': 'Postback',
    'admin_template_label_columns_button_type_url': 'Url',
    'admin_template_label_columns_button_type': 'タイプ',
    'admin_template_label_columns_button_url': 'Url',
    'admin_template_label_columns_imageUrl': '画像URL',
    'admin_template_label_columns_imageUrl_hint': 'JPEGまたはPNG, 縦横比 1:1.51, 縦横最大1024px, 最大1MB',
    'admin_template_label_columns_text': 'テキスト',
    'admin_template_label_columns_title': 'タイトル',
    'admin_template_label_columns_url': 'URL',
    'admin_template_label_content': 'コンテンツ',
    'admin_template_label_desc': '説明',
    'admin_template_label_durationMs': '音声ファイルの時間長さ(ms)',
    'admin_template_label_hint_facebook_generic': 'Facebook Generic',
    'admin_template_label_hint_line_buttons': 'LINE Buttons',
    'admin_template_label_hint_line_carousel': 'LINE Carousel',
    'admin_template_label_hint_line_confirm': 'LINE Confirm',
    'admin_template_label_hint': 'ヒント',
    'admin_template_label_lat': '緯度',
    'admin_template_label_lon': '経度',
    'admin_template_label_mapTitle': '地図タイトル',
    'admin_template_label_previewUrl': 'プレビューURL',
    'admin_template_label_title': 'タイトル',
    'admin_template_label_url': 'URL',
    'admin_user_accountLockMemo_default': 'その他の理由によりアカウントロックされました',
    'admin_user_accountLockMemo_weak': 'パスワードが弱いためアカウントロックされました',
    'admin_user_accountLockMemo_wrong': 'パスワード間違いが多いためアカウントロックされました',
    'admin_user_add': 'ユーザー新規作成',
    'admin_user_detail_info': 'ユーザー詳細情報',
    'admin_user_detail': 'ユーザー詳細情報',
    'admin_user_floatingLabel_confirmPasssword': '確認用パスワードを入力してください',
    'admin_user_floatingLabel_email': 'メールアドレスを入力してください',
    'admin_user_floatingLabel_mfa_email': '多要素認証通知用メールアドレスを入力してください',
    'admin_user_floatingLabel_mfa_sms': '多要素認証通知用電話番号(SMS)を入力してください',
    'admin_user_floatingLabel_name': '名前は1文字以上で入力してください',
    'admin_user_floatingLabel_password': 'パスワードは半角英数8文字以上で入力してください',
    'admin_user_floatingLabel_plusId': 'ログインIDは1文字以上で入力してください',
    'admin_user_floatingLabel_reset_password_comment': 'このコメントは対象ユーザーにメール送信されます',
    'admin_user_floatingLabel_searchWord': '検索用キーワードはカンマ区切りで入力してください',
    'admin_user_floatingLabel_update_confirm_password_if_neccessary': 'パスワードを強制変更する場合のみ入力してください',
    'admin_user_floatingLabel_update_password_if_neccessary': 'パスワードを強制変更する場合のみ入力してください',
    'admin_user_form_desc_search': '条件を入力して検索してください',
    'admin_user_form_desc_update': 'ユーザー基本情報を更新します',
    'admin_user_form_title_accountLocked': 'このユーザーはアカウントロックされています',
    'admin_user_form_title_attributes_extra': '拡張ユーザー属性',
    'admin_user_form_title_attributes_memo': 'その他ユーザー属性',
    'admin_user_form_title_attributes_mobiagent': 'モビエージェント ゲスト属性'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'admin_user_form_title_attributes_mobiwork': 'モビワーク 一般属性'.replace('モビワーク', window.KonnectPlusServerJSON.service.mobiwork || 'モビワーク'),
    'admin_user_form_title_attributes': 'ユーザー属性',
    'admin_user_form_title_directories': '所属情報',
    'admin_user_form_title_search': 'ユーザー検索',
    'admin_user_form_title_search_by_password_update_date': 'パスワード更新日時でのユーザー検索',
    'admin_user_form_title_update': 'ユーザー情報更新',
    'admin_user_form_title_user_basics': '基本情報',
    'admin_user_get_token_desc': 'パスワードを入力してください。ダミーユーザーのパスワードが不明/未設定の場合はユーザー編集画面から設定してください。',
    'admin_user_hint_password_update_date_as_search_condition': '指定日以降にパスワード変更を行っていないユーザーを検索します',
    'admin_user_import_csv_has_error': 'CSVファイルのデータに不正な箇所があります。',
    'admin_user_import_desc_csv_format': "CSV フォーマットにはヘッダー行を含む必要があります: 'ログインId'(必須), 'name'(必須), 'mail'(必須), 'password', 'permitLevel', 'namePublic', 'nameSearchWords', 'nameSearchWordsPublic'",
    'admin_user_import_progress_label_update_fail': '失敗',
    'admin_user_import_progress_label_update_progress': '登録中 ...',
    'admin_user_import_progress_label_update_success': '完了',
    'admin_user_import': 'ユーザー一括追加',
    'admin_user_label_password_update_date_as_search_condition': '最終パスワード更新日',
    'admin_user_management': 'ユーザー管理',
    'admin_user_not_logged_in': 'ユーザーは一度もログインしていません',
    'admin_user_page_title_new': 'ユーザー新規作成',
    'admin_user_passwordReset_error_invalid_mailBody_length': 'メール本文に利用可能な文字数は#{maxLength}です',
    'admin_user_passwordReset_error_invalid_mailBody_required': 'メール本文を入力してください',
    'admin_user_passwordReset_error_invalid_mailTitle_length': 'メールタイトルに利用可能な文字数は#{maxLength}です',
    'admin_user_passwordReset_error_invalid_mailTitle_required': 'メールタイトルを入力してください',
    'admin_user_passwordReset_form_desc': 'パスワードリセットを行う対象ユーザーのIDと名前を確認してください',
    'admin_user_passwordReset_form_title': 'パスワードリセットを行います',
    'admin_user_passwordReset_label_bcc': 'BCC',
    'admin_user_passwordReset_label_cc': 'CC',
    'admin_user_passwordReset_label_limitSec_12h': '12時間',
    'admin_user_passwordReset_label_limitSec_1d': '1日',
    'admin_user_passwordReset_label_limitSec_1h': '1時間',
    'admin_user_passwordReset_label_limitSec_1w': '1週間',
    'admin_user_passwordReset_label_limitSec_3d': '3日',
    'admin_user_passwordReset_label_limitSec': '一時パスワード有効期間',
    'admin_user_passwordReset_label_mailBody': 'メール本文',
    'admin_user_passwordReset_label_mailTitle': 'メールタイトル',
    'admin_user_passwordReset_mailBody_default_sysadm':  'こんにちは %%USER_NAME%%さん\n\n新規ユーザーを作成しました。\n次のURLにアクセスしてパスワードをリセットしてください。\n',
    'admin_user_passwordReset_mailBody_default': 'こんにちは %%USER_NAME%%さん\n\n次のURLにアクセスしてパスワードをリセットしてください\n',
    'admin_user_passwordReset_mailTitle_default_sysadm':  'MobiSeries開通案内のお知らせ'.replace('MobiSeries', window.KonnectPlusServerJSON.service.mobiseries || 'MobiSeries'),
    'admin_user_passwordReset_mailTitle_default': 'MobiSeries管理者からのお知らせ'.replace('MobiSeries', window.KonnectPlusServerJSON.service.mobiseries || 'MobiSeries'),
    'admin_user_passwordReset_page_title': 'パスワードリセット',
    'admin_user_snack_invalid_permitLevels': '権限レベルが選択されていません',
    'admin_user_update': 'ユーザー情報更新',
    'admin_warnwords_management': '監視キーワード',
    'admin_warnwords_management_title_guest': 'ゲストユーザー向け監視キーワード管理',
    'admin_warnwords_management_title_operator': 'オペレーター向け監視キーワード管理',
    'alert_error_account_may_be_locked': 'アカウントがロックされている可能性があります。正しいパスワードでログイン出来ない場合は管理者に連絡してください',
    'alert_error_force_logout': 'エラーが発生しました。アプリケーションをログアウトします',
    'alert_error_force_reload': 'エラーが発生しました。ブラウザをリロードします',
    'alert_error_maintenance': 'サーバはメンテナンス中です',
    'alert_error_session_expired': 'セッションが無効になりました。ログインしなおしてください',
    'button_add': '追加',
    'button_addCategory': 'カテゴリー追加',
    'button_approve': '承認',
    'button_auto_resize': '画像を自動でリサイズする',
    'button_back': '前画面に戻る',
    'button_backup': 'バックアップ',
    'button_bulk_add': '一括追加',
    'button_bulk_export': '一括ダウンロード',
    'button_bulk_operation': '一括操作',
    'button_bulk_delete': '一括削除',
    'button_cancel': 'キャンセル',
    'button_check': 'チェック',
    'button_close_talk_tab': 'トークを画面を閉じる',
    'button_close': '閉じる',
    'button_copy': 'コピー',
    'button_create_account': 'アカウント作成',
    'button_csv_save': 'CSVで保存',
    'button_delete_all': '全て削除',
    'button_delete': '削除',
    'button_download': 'ダウンロード',
    'button_edit_mode': '編集モード',
    'button_edit': '編集',
    'button_exec': '実行',
    'button_export': 'ダウンロード',
    'button_exportMenu': '出力メニュー',
    'button_file_import': 'ファイルインポート',
    'button_file_owner_change': 'ファイル所有権移動',
    'button_generate': '生成',
    'button_get_token': 'トークン取得',
    'button_import': '一括追加',
    'button_inquiry': '照会',
    'button_invite': '招待',
    'button_leave': '退室',
    'button_list': '一覧表示',
    'button_login': 'ログイン',
    'button_modify': '修正',
    'button_apply': '適用',
    'button_ok': 'OK',
    'button_overwriteDomain': 'ドメイン管理者なりすまし',
    'button_preview': 'プレビュー',
    'button_readMore': 'もっと見る',
    'button_refresh': '更新',
    'button_register': '登録',
    'button_reject': '却下',
    'button_reset': '初期化',
    'button_reset_password': 'パスワードリセット',
    'button_save': '保存',
    'button_search': '検索',
    'button_select_file': 'ファイル選択',
    'button_select': '選択',
    'button_send': '送信',
    'button_start_chatting_sns_user': '有人対応開始',
    'button_start': '開始',
    'button_status': 'ステータス',
    'button_stop': '終了',
    'button_testSend': 'テスト送信',
    'button_unlock': '解除',
    'button_unmark': '却下',
    'button_up': '上へ',
    'button_update_room_name': 'ルーム名を変更する',
    'button_update': '更新',
    'button_updateFile': 'ファイル更新',
    'button_upload': 'アップロード',
    'button_change': '変更',
    'common_label_accountLockMemo': 'アカウントロック原因',
    'common_label_add_member': 'メンバー追加',
    'common_label_add': '追加',
    'common_label_administrator': '管理者',
    'common_label_all_domain': 'ドメイン全体',
    'common_label_android': 'Android',
    'common_label_anonymous_user': '匿名ユーザー',
    'common_label_apiLimit': 'API制限',
    'common_label_apiKey_updateDate': 'APIキー更新日時',
    'common_label_auto_reflesh': '自動更新',
    'common_label_autoReply_mode_auto': '自動応答',
    'common_label_autoReply_mode_human': '有人対応',
    'common_label_autoReply_mode': '応答モード',
    'common_label_autoReply': '自動応答',
    'common_label_blocked_sns_user': 'ブロックされたSNSユーザーです',
    'common_label_checkLogNotifyMailAddress': '通知先メールアドレス',
    'common_label_checkLogPrintDetail': '通知メールにログ本文の情報を含む',
    'common_label_clear_db_cache': 'DBキャッシュクリア',
    'common_label_comment': 'コメント',
    'common_label_condition': '条件',
    'common_label_confirm_password': 'パスワード確認',
    'common_label_createdAt': '作成日時',
    'common_label_days': '曜日',
    'common_label_daysOfWeek_friday': '金曜日',
    'common_label_daysOfWeek_monday': '月曜日',
    'common_label_daysOfWeek_saturday': '土曜日',
    'common_label_daysOfWeek_sunday': '日曜日',
    'common_label_daysOfWeek_thursday': '木曜日',
    'common_label_daysOfWeek_tuesday': '火曜日',
    'common_label_daysOfWeek_wednesday': '水曜日',
    'common_label_debug_user': 'デバッグユーザー',
    'common_label_default': 'デフォルト',
    'common_label_delete': '削除',
    'common_label_deleted_user': '削除済みユーザー',
    'common_label_deleted': '削除済み',
    'common_label_unsend': 'メッセージの送信を取り消しました',
    'common_label_deleteType': 'Delete Type',
    'common_label_deleteType_auto': '自動削除',
    'common_label_deleteType_manual': '手動削除',
    'common_label_deleteType_message': 'メッセージ削除',
    'common_label_detail': '詳細',
    'common_label_message_detail': 'メッセージ詳細',
    'common_label_directory_department': '部署',
    'common_label_directory_departmentFax': '部署Fax',
    'common_label_directory_departmentKana': '部署カナ',
    'common_label_directory_departmentPhone': '部署TEL',
    'common_label_directory_firstName': '名',
    'common_label_directory_firstNameAlphabet': '名アルファベット',
    'common_label_directory_firstNameKana': '名カナ',
    'common_label_directory_lastName': '姓',
    'common_label_directory_lastNameAlphabet': '姓アルファベット',
    'common_label_directory_lastNameKana': '姓カナ',
    'common_label_directory_nameAlphabet': '名前アルファベット',
    'common_label_directory_nameKana': '名前カナ',
    'common_label_directory_organization': '組織',
    'common_label_directory_personalPhone': '個人連絡先',
    'common_label_directory_position': '役職',
    'common_label_disable': '無効化',
    'common_label_disabled' : '無効',
    'common_label_domain' : 'ドメイン',
    'common_label_domain_id_search' : 'ドメインID検索',
    'common_label_dontNotify': '通知しない',
    'common_label_duration_from' : '期間.開始日時',
    'common_label_duration_option_LAST_1MONTH' : '1ヶ月前から今日',
    'common_label_duration_option_LAST_1WEEK' : '1週間前から今日',
    'common_label_duration_option_LAST_3DAYS' : '3日前から今日',
    'common_label_duration_option_LAST_MONTH' : '先月',
    'common_label_duration_option_NOT_SPECIFIED' : '未指定',
    'common_label_duration_option_SPECIFY_DAYS' : '日時指定',
    'common_label_duration_option_THIS_MONTH' : '今月',
    'common_label_duration_option_TODAY' : '今日',
    'common_label_duration_option_YESTERDAY' : '昨日',
    'common_label_duration_option_LAST_1MONTH_only_category_form' : '昨日までの１ヶ月間',
    'common_label_duration_option_LAST_1WEEK_only_category_form' : '昨日までの１週間',
    'common_label_duration_option_LAST_3DAYS_only_category_form' : '昨日までの３日間',
    'common_label_duration_option_LAST_MONTH_only_category_form' : '先月',
    'common_label_duration_option_NOT_SPECIFIED_only_category_form' : '未指定',
    'common_label_duration_option_SPECIFY_DAYS_only_category_form' : '日時指定',
    'common_label_duration_option_THIS_MONTH_only_category_form' : '今月',
    'common_label_duration_option_YESTERDAY_only_category_form' : '昨日',
    'common_label_duration_to' : '期間.終了日時',
    'common_label_duration' : '期間',
    'common_label_edit': '編集',
    'common_label_maxInteractionUser': '1オペレーター同時対応上限\n(ユーザー別)',
    'common_label_maxInteractionUser_csvImport': '1オペレーター同時対応上限\n(ユーザー別)',
    'common_label_hintText_maxInteractionUser': 'ユーザーごとに同時に担当できるopen状態の有人対応ルーム数',
    'common_label_email': 'メールアドレス',
    'common_label_mfa_email': '多要素認証通知用メールアドレス',
    'common_label_mfa_sms': '多要素認証通知用電話番号(SMS)',
    'common_label_enable': '有効化',
    'common_label_enabled': '有効',
    'common_label_enabled_disabled': '有効/無効',
    'common_label_equals': '等しい',
    'common_label_export_day' : '出力日',
    'common_label_ext_csv': 'CSV',
    'common_label_ext_json': 'JSON',
    'common_label_ext_pdf': 'PDF',
    'common_label_ext_svg': 'SVG',
    'common_label_ext_text': 'TEXT',
    'common_label_ext_zip': 'ZIP',
    'common_label_facebook_pageId' : 'Page ID',
    'common_label_facebook_userId' : 'User ID',
    'common_label_facebook' : 'Facebook Messenger',
    'common_label_failed' : '失敗',
    'common_label_favorite_contacts': 'コンタクト',
    'common_label_favorite_rooms': 'お気に入りルーム',
    'common_label_file_name': 'ファイル名',
    'common_label_finish_rooms': '完了案件',
    'common_label_first_login': '初回ログイン時刻',
    'common_label_foreign_userId': '外部連携ID',
    'common_label_fromDateTime': '開始日時',
    'common_label_from': 'から',
    'common_label_general_user': '一般ユーザ',
    'common_label_generatedAt': '生成日時',
    'common_label_greater_than': '〜より大きい',
    'common_label_greater_than_or_equals': '以上',
    'common_label_groundTotal': '累計',
    'common_label_group': 'グループ',
    'common_label_guest_alert': '要注意ゲスト',
    'common_label_guest_memo': 'ゲストメモ',
    'common_label_guest_type': 'ゲストタイプ',
    'common_label_guest_assigned_operator_id': '登録した担当者ユーザーID',
    'common_label_guest_assigned_operator_name': '登録した担当者ユーザー名',
    'common_label_hide_detail': '詳細非表示',
    'common_label_hide': '隠す',
    'common_label_hours': '時間',
    'common_label_id' : 'ID',
    'common_label_ios' : 'iOS',
    'common_label_ipAddr' : 'IPアドレス',
    'common_label_keyword_or_tag': 'タグ検索には＃を入力してください',
    'common_label_knowledge_keyword': 'キーワード',
    'common_label_last_login': '最終ログイン時刻',
    'common_label_lastWindowLoadTime': '最終ウィンドウロード時刻',
    'common_label_lastWindowUnLoadTime': '最終ウィンドウアンロード時刻',
    'common_label_less_than': '〜より小さい',
    'common_label_less_than_or_equals': '以下',
    'common_label_line_bc' : 'LINE Business Connect',
    'common_label_line_messageApi' : 'LINE',
    'common_label_line_mid' : 'MID',
    'common_label_line_uid' : 'userId',
    'common_label_line' : 'LINE',
    'common_label_list' : '一覧',
    'common_label_logClass': 'Class',
    'common_label_logDate': 'Date',
    'common_label_logDomainId': 'ドメインID',
    'common_label_excludeLogDomainIds': '検索から除外するドメイン',
    'common_label_excludeLogDomainIds_desc': '検索対象外とするドメインIDをカンマ区切りで入力してください。',
    'common_label_logHost': 'Host',
    'common_label_logHostaddr': 'Host address',
    'common_label_logHostname': 'Host name',
    'common_label_logKey': 'Log Key',
    'common_label_logKey1': 'Log Key1',
    'common_label_logKey2': 'Log Key2',
    'common_label_logData': 'Data',
    'common_label_logLevel': 'Log Level',
    'common_label_logText': 'Text',
    'common_label_logTime': 'Time',
    'common_label_members' : 'メンバー',
    'common_label_memo' : 'メモ',
    'common_label_messages': 'メッセージ',
    'common_label_internal_messages': '内部チャット',
    'common_label_internal_room_chat_history': '内部チャット履歴',
    'common_label_metrics' : '統計',
    'common_label_minlogLevel': 'ミニマムログレベル',
    'common_label_minutes': '分',
    'common_label_mobiagent' : 'モビエージェント'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'common_label_mobidash' : 'ダッシュボード',
    'common_label_mobidash_data_download' : '実績データ',
    'common_label_mobidash_operator_management' : 'オペレーター管理',
    'common_label_mobidash_monitoring': 'モニタリングタブ',
    'common_label_mobistats' : 'モビスタッツ'.replace('モビスタッツ', window.KonnectPlusServerJSON.service.mobistats || 'モビスタッツ'),
    'common_label_mobiwork' : 'モビワーク'.replace('モビワーク', window.KonnectPlusServerJSON.service.mobiwork || 'モビワーク'),
    'common_label_name' : '名前',
    'common_label_number' : 'No.',
    'common_label_not_selected': '未指定',
    'common_label_notfiyLogLevel': '通知レベル',
    'common_label_other': 'その他',
    'common_label_all_group': '全グループ',
    'common_label_password': 'パスワード',
    'common_label_password_update_date': 'パスワード更新日時',
    'common_label_period': '期間',
    'common_label_permission_level': '権限レベル',
    'common_label_permitlevel_bot': 'Bot(自動応答)',
    'common_label_permitlevel_content_operator': 'コンテンツオペレーター',
    'common_label_permitlevel_domain_admin': '管理者',
    'common_label_permitlevel_guest': 'ゲストユーザー',
    'common_label_permitlevel_operator': 'オペレーター',
    'common_label_permitlevel_supervisor': 'スーパーバイザー',
    'common_label_permitlevel_unknown': 'その他',
    'common_label_permitlevel_user': '一般ユーザー',
    'common_label_plus_user_id': 'ログインID',
    'common_label_plusId_domainId': 'ログインIDまたはドメインID',
    'common_label_preview': 'プレビュー',
    'common_label_priority': '優先度',
    'common_label_publicRoom_assignee': '担当者',
    'common_label_publicRoom_date': '期間',
    'common_label_publicRoom_time': '時間帯',
    'common_label_publicRoom_autoReplyWorkingTime': '自動応答の対応時間 (秒)',
    'common_label_publicRoom_category3': '分類区分3',
    'common_label_publicRoom_closeDate': '終了日時',
    'common_label_publicRoom_closeReason': '終了理由',
    'common_label_publicRoom_closeDateTime': '終了日時',
    'common_label_publicRoom_endDateTime': '終了日時',
    'common_label_publicRoom_finishDateTime': '完了日時',
    'common_label_publicRoom_finishStatus_00': 'オペレーター初期状態',
    'common_label_publicRoom_finishStatus_01': 'オペレーター通常完了',
    'common_label_publicRoom_finishStatus_02': 'ユーザーからの応答なし',
    'common_label_publicRoom_finishStatus_03': '対応未完了',
    'common_label_publicRoom_finishStatus_04': '後処理未完了',
    'common_label_publicRoom_finishStatus_05': '未対応（あふれ）',
    'common_label_publicRoom_finishStatus_10': '自動応答初期状態',
    'common_label_publicRoom_finishStatus_11': '自動応答通常完了',
    'common_label_publicRoom_finishStatus_12': '自動応答でタイムアウト完了',
    'common_label_publicRoom_finishStatus_13': '自動応答でタイムアウト (オペレーター対応数超過)',
    'common_label_publicRoom_finishStatus_14': '自動応答でタイムアウト (時間外)',
    'common_label_publicRoom_finishStatus_15': '自動応答でタイムアウト (休日)',
    'common_label_publicRoom_finishStatus_16': '自動応答でタイムアウト (その他理由)',
    'common_label_publicRoom_finishStatus': '完了ステータス',
    'common_label_publicRoom_firstAcceptOperator' : '初回担当者',
    'common_label_publicRoom_firstAcceptTime' : '初回担当時刻',
    'common_label_publicRoom_firstHumanModeTime' : '有人対応モード開始時刻',
    'common_label_publicRoom_firstMessageOperator' : '初回メッセージ送信オペレーター',
    'common_label_publicRoom_firstMessageTime' : '初回メッセージ時刻',
    'common_label_publicRoom_group': '担当グループ',
    'common_label_publicRoom_guest': 'ゲスト',
    'common_label_publicRoom_guestComment': 'ゲストコメント',
    'common_label_publicRoom_guestRemoteIp': 'アクセス元IP',
    'common_label_publicRoom_guestClientPort': 'ポート番号',
    'common_label_publicRoom_guestTraceId': 'TraceId',
    'common_label_publicRoom_guestReview': 'ゲスト評価',
    'common_label_publicRoom_historyOfOperators': '担当者履歴',
    'common_label_publicRoom_holdCount' : '保留回数',
    'common_label_publicRoom_holdFlg' : '保留フラグ',
    'common_label_publicRoom_holdStartTime' : '保留開始時刻',
    'common_label_publicRoom_holdTotalTime' : '合計保留時間(秒)',
    'common_label_publicRoom_holdAvelageTime' : '平均保留時間(秒)',
    'common_label_publicRoom_incidentNo': 'インシデント番号',
    'common_label_publicRoom_info': '問合せ元情報',
    'common_label_publicRoom_metrics_guestMsgCount': 'メッセージ数(ゲスト)',
    'common_label_publicRoom_metrics_lastMsgType': '最終発言タイプ',
    'common_label_publicRoom_metrics_lastMsgTime': '最終発言時刻',
    'common_label_publicRoom_metrics_lastMsgStandby': 'OPレスポンス/スタンバイ開始日時',
    'common_label_publicRoom_metrics_msgCount': 'メッセージ数(合計)',
    'common_label_publicRoom_metrics_operatorMsgCount': 'メッセージ数(オペレーター)',
    'common_label_publicRoom_operationType': '自動応答状態(0: 有人対応 1: 自動応答)',
    'common_label_publicRoom_operatorMemo': 'オペレーターメモ',
    'common_label_publicRoom_llmSummaryMemo': '要約',
    'common_label_publicRoom_secureFor': '要約',
    'common_label_publicRoom_operatorWorkingTime': 'オペレーターの対応時間 (秒)',
    'common_label_publicRoom_pqpa': '主質問主回答',
    'common_label_publicRoom_pqpa_questions' : '主質問',
    'common_label_publicRoom_pqpa_answers' : '主回答',
    'common_label_publicRoom_pqpa_too_long': '主質問/主回答の登録内容が多いため保存できません',
    'common_label_publicRoom_select_messages' : 'メッセージ選択',
    'common_label_publicRoom_questionnaireType': '問合せ種別',
    'common_label_publicRoom_startDate': 'ルーム開始日',
    'common_label_publicRoom_startDateTime': '開始日時',
    'common_label_publicRoom_translation': '翻訳',
    'common_label_publicRoom_urgencyDegree': '緊急度',
    'common_label_publicRoom_waitOperator': '有人対応切替待機(0: 待機なし 1: 待機あり)',
    'common_label_publicRoom_waitOperatorTime': '有人対応切替待機開始時刻',
    'common_label_publicRoom_waitOperatorGroup': '有人対応切替候補グループ',
    'common_label_read_only': '[読み取り専用]',
    'common_label_range': '〜から〜まで',
    'common_label_recordCount': '#{num} 件見つかりました',
    'common_label_reference': '詳しくはこちら',
    'common_label_required': '必須',
    'common_label_required_item': '必須項目',
    'common_label_result': '件',
    'common_label_room_id': 'ルームID',
    'common_label_room_name': 'ルーム名',
    'common_label_room_type_11' : 'ダイレクト',
    'common_label_room_type_nn' : 'グループ',
    'common_label_room_type_pub' : 'パブリック',
    'common_label_room_type': 'ルームタイプ',
    'common_label_rooms': 'ルーム',
    'common_label_sample': 'サンプル',
    'common_label_saved': '保存しました',
    'common_label_search_from_date': '検索開始期間',
    'common_label_search_keyword': '検索キーワード',
    'common_label_search_to_date': '検索終了期間',
    'common_label_seconds': '秒',
    'common_label_sentiment': '判定結果',
    'common_label_show_detail': '詳細表示',
    'common_label_sns_id': 'SNS ID',
    'common_label_sns_index': 'SNS Index',
    'common_label_sns_type_facebook': 'Facebook Messenger',
    'common_label_sns_type_bedoreWeb': 'BEDORE for WEB',
    'common_label_sns_type_line': 'LINE Business Connect',
    'common_label_sns_type_line2': 'LINE',
    'common_label_sns_type_line2-1': 'LINE(1)',
    'common_label_sns_type_line2-2': 'LINE(2)',
    'common_label_sns_type_line2-3': 'LINE(3)',
    'common_label_sns_type_line2-4': 'LINE(4)',
    'common_label_sns_type_line2-5': 'LINE(5)',
    'common_label_sns_type_line2-6': 'LINE(6)',
    'common_label_sns_type_line2-7': 'LINE(7)',
    'common_label_sns_type_line2-8': 'LINE(8)',
    'common_label_sns_type_line2-9': 'LINE(9)',
    'common_label_sns_type_linecc': 'LINE（Switcher API）',
    'common_label_sns_type_lineWorks': 'LINE Works',
    'common_label_sns_type_misc': 'Web',
    'common_label_sns_type_mobiGuest': '#{mobiguest}'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナルクライアントアプリ'),
    'common_label_sns_type_mobiGuest-1': '#{mobiguest} (1)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_mobiGuest-2': '#{mobiguest} (2)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_mobiGuest-3': '#{mobiguest} (3)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_mobiGuest-4': '#{mobiguest} (4)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_mobiGuest-5': '#{mobiguest} (5)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_mobiGuest-6': '#{mobiguest} (6)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_mobiGuest-7': '#{mobiguest} (7)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_mobiGuest-8': '#{mobiguest} (8)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_mobiGuest-9': '#{mobiguest} (9)'.replace('#{mobiguest}', window.KonnectPlusServerJSON.service.mobiguest || 'オリジナル クライアントアプリ'),
    'common_label_sns_type_msbot': 'Microsoft Bot Framework',
    'common_label_sns_types': '流入元',
    'common_label_sns_types_detail': '流入元詳細',
    'common_label_sort_asc': '昇順',
    'common_label_sort_desc': '降順',
    'common_label_sort_order': '並び替え',
    'common_label_specify_day_and_time': '曜日時間指定',
    'common_label_specify_date_and_time': '日付時間指定',
    'common_label_status': '状態',
    'common_label_status_open': 'オープン',
    'common_label_status_close': 'クローズ',
    'common_label_status_finish': '完了',
    'common_label_status_none': '未処理',
    'common_label_status_review': '未承認',
    'common_label_status_approved': '承認済み',
    'common_label_status_modified': '修正済み',
    'common_label_sentiment_positive': '喜び',
    'common_label_sentiment_neutral': '普通',
    'common_label_sentiment_problem': '困り',
    'common_label_sentiment_angry': '怒り',
    'common_label_setting': '設定',   // MBA-13493
    'common_label_success': '成功',
    'common_label_suggested_sentiment': '修正結果',
    'common_label_sum_daily': '日毎',
    'common_label_sum_hourly': '時間毎',
    'common_label_sum_option': '集計オプション',
    'common_label_suspended': '利用一時停止',
    'common_label_tabeditor_desc': 'カテゴリー名を変更するには各カテゴリーのタブをダブルクリックしてください',
    'common_label_tag': 'タグ',
    'common_label_this_is_a_file': 'こちらがファイルです。',
    'common_label_this_is_a_stamp': 'こちらがスタンプです。',
    'common_label_this_is_an_image': 'こちらが画像です。',
    'common_label_to': 'まで',
    'common_label_toDateTime': '終了日時',
    'common_label_token': 'トークン',
    'common_label_to_period': 'から',
    'common_label_try_new_search': '新しいあいまい検索',
    'common_label_type': '種別',
    'common_label_unknown': '不明',
    'common_label_unmask_password': 'パスワードを表示',
    'common_label_updateDate': '更新日時',
    'common_label_user_attribute_memos_field': 'フィールド',
    'common_label_user_attribute_memos_name': '名前',
    'common_label_user_attribute_memos_questionKey': 'Question key',
    'common_label_user_attribute_memos_refField': 'Ref field',
    'common_label_user_attribute_memos_type': 'タイプ',
    'common_label_user_attribute_memos_values': 'Values',
    'common_label_user_id': 'ユーザーID',
    'common_label_user_mail': 'メールアドレス',
    'common_label_user_name_can_search': '検索キーワード',
    'common_label_user_name': 'ユーザー名',
    'common_label_user_namePublic': 'ユーザー名(公開)',
    'common_label_user_nameSearchWords': '検索キーワード',
    'common_label_user_nameSearchWordsPublic': '検索キーワード(公開)',
    'common_label_user': 'ユーザー',
    'common_label_value': '値',
    'common_label_waiting': '実行待ち',
    'common_label_web': 'ウェブ',
    'confirm_message_leave_room': '退出してもよろしいですか',
    'confirm_title_leave_room': 'ルーム退出',
    'dialog_apply_all_sentiments': '一括で「${sentiment}」に変更',
    'dialog_admin_knowledge_msgs': 'メッセージ詳細',
    'dialog_body_news_no_content': '現在お知らせはありません。',
    'dialog_change_pass_desc_password_expired': '${passwdExpireDay}日間以上パスワードが変更されていません',
    'dialog_change_pass_error_pass_mismatch': 'パスワードが一致しません',
    'dialog_change_pass_error_pass_required': 'パスワードは必須項目です',
    'dialog_change_pass_label_new_password': '新しいパスワード',
    'dialog_change_pass_label_new_password_reenter': 'パスワード確認',
    'dialog_change_pass_label_original_password': '現在のパスワード',
    'dialog_change_pass_success': 'パスワード変更されました',
    'dialog_change_pass_title': 'パスワード変更',
    'dialog_desc_line_user_search': '全ユーザーを検索するには*を入力します',
    'dialog_domain_list': 'ドメイン',
    'dialog_notice_add_user': 'ユーザー情報を追加してもよろしいですか？',
    'dialog_notice_callcenter_option': '問合せ時間を設定してもよろしいですか？',
    'dialog_notice_change_file_owner': 'ファイル所有権を移動します。この操作はユーザー削除の時のみ実施してください。',
    'dialog_notice_delete': '削除してもよろしいですか？',
    'dialog_notice_delete_domain': 'ドメインを削除してもよろしいですか？',
    'dialog_notice_delete_file': '該当ファイルを削除してもよろしいですか？',
    'dialog_notice_delete_group': '該当グループを削除してもよろしいですか？',
    'dialog_notice_delete_list': '該当リストを削除してもよろしいですか？',
    'dialog_notice_delete_setting': '削除してもよろしいですか？',
    'dialog_notice_delete_time': '該当時間を削除してもよろしいですか？',
    'dialog_notice_delete_user': 'ユーザー情報を削除してもよろしいですか？',
    'dialog_notice_plus_option': 'オプションを設定してもよろしいですか？',
    'dialog_notice_replace_file': '既存のファイルと置き換えますか?',
    'dialog_notice_reset_password': 'パスワードをリセットして一時パスワードを送信しますか？',
    'dialog_notice_suspend_user': '該当ユーザーを一時停止してもよろしいですか？',
    'dialog_notice_unlockAccount': '該当ユーザーをアカウントロック解除してもよろしいですか？',
    'dialog_notice_unsuspend_user': '該当ユーザーを一時停止を解除してもよろしいですか？',
    'dialog_notice_update_room': 'ルーム詳細情報を更新してもよろしいですか？',
    'dialog_notice_update_user_directory':  'ユーザー所属情報を更新してもよろしいですか？',
    'dialog_notice_update_user': 'ユーザー情報を更新してもよろしいですか？',
    'dialog_notice_delete_sysUserPermit': '以下の管理者権限を削除してもよろしいですか？',
    'dialog_stamp_confirm_content': 'スタンプ情報を更新してもよろしいですか？',
    'dialog_title_add_category': 'カテゴリー追加',
    'dialog_title_add_user': 'ユーザー追加',
    'dialog_title_callcenter_option': '問合せ時間設定',
    'dialog_title_change_category': 'カテゴリー変更',
    'dialog_title_change_file_owner': 'ファイル所有権移動',
    'dialog_title_delete_domain': 'ドメイン削除',
    'dialog_title_delete_file': 'ファイル削除',
    'dialog_title_delete_group': 'グループ削除',
    'dialog_title_delete_list': 'リスト削除',
    'dialog_title_delete_period_specified_response': '期間指定応答メッセージ設定削除',
    'dialog_title_delete_time': '時間削除',
    'dialog_title_delete_user': 'ユーザー削除',
    'dialog_title_file_upload': 'ファイルアップロード',
    'dialog_title_get_token': 'トークン取得',
    'dialog_title_line_cwa': 'LINE CWA コンテンツ選択',
    'dialog_title_line_emoji': '絵文字選択',
    'dialog_title_line_sticker': 'スタンプ選択',
    'dialog_title_line_user_search': 'ユーザー検索',
    'dialog_title_news_no_content': 'お知らせ',
    'dialog_title_news_start_date': '投稿日',
    'dialog_title_news_title': 'タイトル',
    'dialog_title_news': 'お知らせ 最終更新: #{date}',
    'dialog_title_plus_option': 'オプション設定',
    'dialog_title_replace_file': '重複したファイル名を指定されました',
    'dialog_title_reset_password': 'パスワードリセット',
    'dialog_title_select_domain': 'ドメインID選択',
    'dialog_title_stamp_confirm': 'スタンプ更新確認',
    'dialog_title_sentiment_approve': '感情分析結果 承認確認',
    'dialog_title_sentiment_reject': '感情分析結果 却下確認',
    'dialog_title_sentiment_modify': '感情分析結果 修正確認',
    'dialog_title_suspend_user': 'ユーザー一時停止',
    'dialog_title_unlockAccount': 'アカウントロック解除',
    'dialog_title_unsuspend_user': 'ユーザー一時停止解除',
    'dialog_title_update_avatar': 'アバター更新',
    'dialog_title_update_password': 'パスワード変更',
    'dialog_title_update_profile': 'プロフィール変更',
    'dialog_title_update_roomInfo': 'ルーム情報変更',
    'dialog_title_update_user_directory': 'ユーザー所属情報更新',
    'dialog_title_update_user': 'ユーザー情報更新',
    'dialog_title_user_search': 'ユーザー検索',
    'dialog_title_delete_sysUserPermit': '管理者権限削除確認',
    'dialog_user_search_find_in_anonyous': 'アノニマスユーザーを含む',
    'dialog_user_search_find_other_domain': '他ドメインを含む',
    'dialog_user_search_inclued_line': 'ゲストユーザーを含む',
    'dialog_user_search_members': 'メンバー',
    'dialog_user_search_search_text': '検索キーワード',
    'error_apiKey_required': 'APIキーは必須項目です',
    'error_confirm_password_is_required': '確認用パスワードは必須項目です',
    'error_contain_ng_word': '禁止語が含まれています',
    'error_data_not_found': '対象のデータは見つかりませんでした',
    'error_empty_content': '未入力の項目があります',
    'error_invalid_input': '不正な入力があります',
    'error_fail_to_create_user': 'ユーザー登録に失敗しました。',
    'error_failed_to_load_image': '画像のロードに失敗しました',
    'error_failed_to_resize_image': '画像のリサイズに失敗しました',
    'error_failed_to_upload_files_due_to_img_extention': '画像ファイルを指定してください',
    'error_failed_to_upload_files_due_to_size_limit': 'ファイルサイズが大きいため画像のアップロードに失敗しました',
    'error_failed_to_upload_files_due_to_total_count_limit': '合計アップロードファイル数が制限に達したため画像のアップロードに失敗しました',
    'error_failed_to_upload_files_due_to_total_size_limit': '合計アップロードファイルサイズが制限に達したため画像のアップロードに失敗しました',
    'error_failed_to_upload_files': '画像のアップロードに失敗しました',
    'error_failed_to_upload_files_size': 'ご利用可能なファイル容量は、#{maxSizeKB}kb までです。',
    'error_failed_to_upload_image_size': 'ご利用可能な画像サイズは、縦 #{maxHeight}px ✕ 横 #{maxWidth}px までです。',
    'error_field_is_required': '必須項目が入力されていません',
    'error_format_maxLength': '最大桁数は#{param}で入力してください',
    'error_format_maxValue': '最大値は#{param}で入力してください',
    'error_format_minLength': '最小桁数は#{param}で入力してください',
    'error_format_minValue': '最小値は#{param}で入力してください',
    'error_format': 'フォーマットエラー',
    'error_format_ngwords': '1つのキーワードは20文字以下にしてください。',
    'error_from_to_both_required': '開始日と終了日は両方入力してください',
    'error_integer_required': '整数で入力してください',
    'error_invalid_alert_config_keyword_csv_format': '"{キーワード},{カウント}" の形式で入力してください',
    'error_invalid_alert_config_keyword_max_length': '#{maxLength}文字以内の長さの単語を指定してください',
    'error_invalid_alert_config_keyword_threshold_is_number': 'カウントが数値ではありません',
    'error_invalid_alert_config_keyword_threshold_is_positive': 'カウントには0より大きい数値を入力してください',
    'error_invalid_alert_config_keyword_threshold_max': 'カウントには#{maxThreshold}以下の数値を入力してください',
    'error_invalid_alert_config_keywords_max_size': '単語数の最大は#{maxSize}です',
    'error_invalid_alert_config_keywords_duplicated': '重複しているキーワードがあります#{dupWord}',
    'error_invalid_apiKey': 'APIキーにタブを含めることはできません',
    'error_invalid_base_file_name': 'ベースファイル名は半角英数字で入力してください',
    'error_invalid_bitMask_format': 'ビットマスク指定が不正です',
    'error_invalid_callcenter_finishRooms_queryParam': '検索条件を入力してください',
    'error_invalid_callcenter_option_ed_time': '対応終了時刻のフォーマットが不正です',
    'error_invalid_callcenter_option_maxInteraction_required': '1オペレーター同時対応上限(デフォルト)を入力してください。',
    'error_invalid_callcenter_option_maxInteractionAdmin_required': '1オペレーター同時対応上限(管理者権限)を入力してください。',
    'error_invalid_callcenter_option_maxInteractionOpe_required': '1オペレーター同時対応上限(一般オペレーター)を入力してください。',
    'error_invalid_callcenter_option_maxInteractionSv_required': '1オペレーター同時対応上限(スーパーバイザー)を入力してください。',
    'error_invalid_callcenter_option_maxInteraction': '1オペレーター同時対応上限(デフォルト)は1以上の値を指定してください',
    'error_invalid_callcenter_option_maxInteractionAdmin': '1オペレーター同時対応上限(管理者権限)は1以上の値を指定してください',
    'error_invalid_callcenter_option_maxInteractionOpe': '1オペレーター同時対応上限(一般オペレーター)は1以上の値を指定してください',
    'error_invalid_callcenter_option_maxInteractionSv': '1オペレーター同時対応上限(スーパーバイザー)は1以上の値を指定してください',
    'error_invalid_callcenter_option_replyRemindTimer_required': '返信アラーム点灯までの時間はを入力してください。',
    'error_invalid_callcenter_option_replyRemindTimer': '設定可能範囲は、1～10,079（7日未満）です',
    'error_invalid_callcenter_option_startPointForCheckReply_required': '返信アラーム状態判定に用いるメッセージ数はを入力してください。',
    'error_invalid_callcenter_option_startPointForCheckReply': '返信アラーム状態判定に用いるメッセージ数は1以上5以下の数値を指定してください',
    'error_invalid_callcenter_option_st_time': '対応開始時刻のフォーマットが不正です',
    'error_invalid_callcenter_option_timeout_required': 'タイムアウトを入力してください。',
    'error_invalid_callcenter_option_timeout': 'タイムアウトは0以上の値を指定してください',
    'error_invalid_callcenter_option_esscalation_types_required': '種別フラグは、1つ以上選択してください。',
    'error_invalid_callcenter_option_timeout_type_label_required': '種別フラグのラベル名を入力してください。',
    'error_invalid_callcenter_option_escalation_timeAlert_required': '時間超過アラート表示までの時間（分）を入力してください。',
    'error_invalid_comment_length': 'コメントの文字数は#{maxLength}以下です',
    'error_invalid_comment_required': 'コメントは必須項目です',
    'error_invalid_csv_file_name' : 'ファイル形式が不正です。.csvファイル指定してください。',
    'error_invalid_csv_format': 'CSVフォーマットが不正です',
    'error_invalid_dictionaryVal_length': '所属情報は#{maxLength}以下で入力してください',
    'error_invalid_domainId_exist': 'ドメインIDが既に利用されています',
    'error_invalid_domainId_required': 'ドメインIDは必須項目です',
    'error_invalid_domainId': 'ドメインIDは先頭英小文字で半角英数字4～28文字',
    'error_invalid_domainId_duplicate': '同じドメインIDが重複しています',
    'error_invalid_domainName_length': 'ドメイン名は#{maxLength}文字以上で指定してください',
    'error_invalid_domainName_required': 'ドメイン名は必須項目です',
    'error_invalid_email_invalid_length': 'メールアドレスの文字数は#{maxLength}以下です',
    'error_invalid_email_pattern': 'メールアドレスが不正です',
    'error_invalid_email_required': 'メールアドレスは必須項目です',
    'error_invalid_mfa_email_required': '多要素認証通知用メールアドレスは必須項目です',
    'error_invalid_mfa_sms_required': '多要素認証通知用電話番号（SMS）は必須項目です',
    'error_invalid_mobile_number_pattern': '正しい携帯番号を入力してください。',
    'error_invalid_from_to': '開始終了日付が不正です',
    'error_invalid_fromDate_required': '開始日時は必須項目です',
    'error_invalid_groupId_already_exist': 'すでにグループIDが存在しています',
    'error_invalid_guestAttributes_contain_comma': 'カンマを含むことはできません',
    'error_invalid_image_dimension': '画像サイズが不正です',
    'error_invalid_image_loading': '画像の読み込みに失敗しました',
    'error_invalid_input_character_range': '入力文字数の範囲を超えています。',
    'error_invalid_input_max_length': '#{maxLength}文字以内で入力してください',
    'error_invalid_ipAddress_format': 'IPアドレス指定が不正です',
    'error_invalid_json_format': 'JSONフォーマットが不正です。',
    'error_invalid_mode_required': '権限は必須項目です',
    'error_invalid_name_alphabet_required': '氏名英字は必須項目です',
    'error_invalid_name_kana_required': '氏名カナは必須項目です',
    'error_invalid_name_required': '名前は必須項目です',
    'error_invalid_namePublic_length':'パブリックな名前の文字数は#{maxLength}以下です',
    'error_invalid_nameSearchWords_required': '検索キーワードは必須項目です',
    'error_invalid_operatorAssignmentTable_maxLength': 'グループ/振分条件設定が大きすぎるため保存できません',
    'error_invalid_defalutCrmConnectSfGroupId_validation_error': 'デフォルト設定 グループIDは3文字半角英数字のみです。',
    'error_invalid_CrmConnectSfGroupId_validation_error': 'CRM Connect 設定グループIDは3文字半角英数字のみです。',
    'error_invalid_password_length': 'パスワードの長さが不正です #{minLength} 文字以上入力してください',
    'error_invalid_password_pattern': 'パスワードは半角英数字で8文字以上入力してください',
    'error_invalid_password_required': 'パスワードは必須項目です',
    'error_invalid_permitLevel_pattern': '権限が不正です',
    'error_invalid_permitLevel_type': '権限は数値で指定してください',
    'error_invalid_csvValidate_maxInteractionUser': '同時対応上限は整数で入力してください(#{minThreshold} ~ #{maxThreshold})',
    'error_invalid_phone_number_required': '電話番号は必須項目です',
    'error_invalid_plusId_alphabeta': 'ログインIDは半角英数字です',
    'error_invalid_plusId_already_exist': 'すでにユーザーが存在しています',
    'error_invalid_plusId_ascii': 'ログインIDはASCII数字です',
    'error_invalid_plusId_length': 'ログインIDの文字数は#{maxLength}以下です',
    'error_invalid_plusId_required': 'ログインIDは必須項目です',
    'error_invalid_row': '不正な行が見つかりました: #{row}行目',
    'error_invalid_searchKeyword_required': '検索キーワードは必須項目です',
    'error_invalid_searchWords_length': '検索キーワードは#{maxLength}以下です',
    'error_invalid_searchWordsPublic_length': 'パブリックな検索キーワードは#{maxLength}以下です',
    'error_invalid_timeout': '問合せタイムアウトは0以上を指定してください',
    'error_invalid_toDate_required': '終了日時は必須項目です',
    'error_invalid_txt_file_name' : 'ファイル形式が不正です。.txtファイル指定してください',
    'error_invalid_url_pattern': 'URLが不正です',
    'error_invalid_url_pattern_needs_ssl': 'HTTPSで始まるURLを指定してください',
    'error_invalid_user_exist_in_room': 'ユーザーはすでにルームに参加しています',
    'error_invalid_userName_length': 'ユーザー名は、#{maxLength}文字以下で入力してください',
    'error_invalid_username_required': 'ユーザー名は必須項目です',
    'error_invalid_userNamePublic_length': 'パブリックなユーザー名の文字数は#{maxLength}以下です',
    'error_invalid_usernamePublic_required': 'パブリックなユーザー名は必須項目です',
    'error_missing_files': 'ファイルがセットされていません',
    'error_mobiagent_version_match': 'モビエージェントバージョンは「/^[0-9]+.[0-9]+$/」のように入力してください。'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'error_mobiagent_version_required': 'モビエージェントバージョンは必須項目です'.replace('モビエージェント', window.KonnectPlusServerJSON.service.mobiagent || 'モビエージェント'),
    'error_mobiwork_version_match': 'モビワークバージョンは「/^[0-9]+.[0-9]+$/」のように入力してください。'.replace('モビワーク', window.KonnectPlusServerJSON.service.mobiwork || 'モビワーク'),
    'error_mobiwork_version_required': 'モビワークバージョンは必須項目です'.replace('モビワーク', window.KonnectPlusServerJSON.service.mobiwork || 'モビワーク'),
    'error_no_api_or_no_permission': '利用できるAPIが見つかりません。あるいは権限がありません',
    'error_no_basefile_to_resize': 'ベースファイルが指定されていません',
    'error_no_date_condition': '検索対象期間を指定してください',
    'error_password_not_match': 'パスワードが一致しません',
    'error_password_reset_failed': 'パスワードリセットに失敗しました',
    'error_password_rule': 'パスワードが弱すぎます',
    'error_password_wrong_tmpPass': '一時パスワードが間違っています',
    'error_password_reset_server_fail': 'パスワード設定できませんでした。\n以下のケースが考えられます。 (#{errorCode})\n ・メールアドレスが間違っている\n ・一時パスワードが間違っている\n ・新規パスワードが強度の条件を満たしていない',
    'error_reserved_base_file_name': 'このベースファイル名はシステムで利用しているため、ご利用頂けません',
    'error_timeout_not_is_number': '問合せタイムアウトは数値で指定してください',
    'error_timeout_required': '問合せタイムアウトは必須項目です',
    'error_too_many_retry_force_logout': 'サーバーとの接続を確立できません。アプリケーションをログアウトします',
    'error_user_count_limit_reached': '登録数超過',
    'error_user_has_files_cannot_delete': 'ファイルを持っているため削除できませんでした',
    'error_user_invalid': 'ユーザーを選んでください',
    'error_http_protocol_invalid': 'httpから始まるURLを入力してください。',
    'error_common_past_date_is_set': '過去日が設定されています',
    'error_common_required': '必須項目です。',
    'error_common_has_field_error': '入力項目にエラーがあります。',
    'file_delete': '削除',
    'file_download': 'ダウンロード',
    'file_name': 'ファイル名',
    'file_type': '種別',
    'file_up_time': 'アップロード時刻',
    'floatingLabel_email': 'メールアドレス',
    'floatingLabel_name': '名前',
    'floatingLabel_newPassword': '新規パスワード',
    'floatingLabel_password': 'パスワード',
    'floatingLabel_plusId': 'ログインID',
    'floatingLabel_room_desc': '説明',
    'floatingLabel_room_name': 'ルーム名',
    'floatingLabel_tempPassword': '一時パスワード',
    'floatingLabel_username': 'ユーザー名',
    'form_title_login': 'ログイン',
    'form_title_reset_password1':'パスワードリセット',
    'form_title_reset_password2':'新規パスワード設定',
    'form_title_reset_password3':'パスワードリセットが完了しました',
    'form_title_signup': 'サインアップ',
    'header_files': 'ファイル一覧',
    'header_issues': '問合せ一覧',
    'header_members': 'メンバー',
    'header_private': 'プライベート',
    'header_public': 'パブリック',
    'header_room_info': 'ルームインフォ',
    'header_setting': '設定',
    'issue_detail_action': '処理',
    'issue_detail_close': 'クローズ',
    'issue_detail_count': 'メッセージ数',
    'issue_detail_end_time': '終了時刻',
    'issue_detail_room': 'ルーム名',
    'issue_detail_start_time': '開始時刻',
    'issue_detail_status': '状態',
    'label_1NRooms': 'ダイレクトチャットルーム',
    'label_admin': 'アドミニストレーター',
    'label_change_password': 'パスワード',
    'label_domainid': 'ドメイン ID',
    'label_found_result': '#{resultCount} 件が検索されました',
    'label_issues': '問合せ',
    'label_loginid': 'ログイン ID',
    'label_logout': 'ログアウト',
    'label_more_than_#{number}_result_found': '検索結果が #{number} 件以上見つかりました。条件を絞り込んでください',
    'label_news': 'お知らせ',
    'label_NNRooms': 'グループチャットルーム',
    'label_password_rule_A': '半角英字を含めてください',
    'label_password_rule_L': '半角英小文字を含めてください',
    'label_password_rule_length': '#{length} 文字以上',
    'label_password_rule_N': '半角数字を含めてください',
    'label_password_rule_S': '半角記号を含めてください',
    'label_password_rule_U': '半角英大文字を含めてください',
    'label_password': 'パスワード',
    'label_profile': 'プロフィール',
    'label_room_not_selected': 'ルームタブを一つ選択してください',
    'label_search_end_date': '終了日付',
    'label_search_end_time': '終了時間',
    'label_search_keyword': '検索対象',
    'label_search_range': '期間',
    'label_search_start_date': '開始日付',
    'label_search_start_time': '開始時間',
    'label_send_on_enter': 'Enterキーで送信',
    'label_speaker_operator': 'オペレーター',
    'label_speaker_you': 'あなた',
    'label_sysadm_login_as_domain_adm': 'なりすまし管理者としてログイン( #{domain_id} )',
    'label_reply_message_title': '#{userName}への返信',
    'label_you': 'あなた',
    'label_reply_message_view_more': 'もっと表示する',
    'label_reply_message_view_less': '少なく表示する',
    'username_suffix': 'さん',
    'link_login_title': 'ログインページ',
    'link_passwordReset_title': 'パスワードを忘れた方',
    'link_restartChatService': '問合せを再開する',
    'loader_connecting_to_server': '接続中...',
    'member_comment': 'コメント',
    'member_department_blank_item': 'なし',
    'member_department_kana': '部署カナ',
    'member_department_phone_number_title': '内線/携帯番号',
    'member_department_phone_number': '内線番号',
    'member_department_title': '部署/役職/部署カナ',
    'member_department': '部署',
    'member_email_infor': 'Email',
    'member_email': 'Email',
    'member_fax': 'FAX番号',
    'member_mobile_ext_number': '携帯番号',
    'member_name_alphabet': '氏名英字',
    'member_name_kana': '氏名カナ',
    'member_name_title': 'ユーザー名/名前カナ/英字',
    'member_name': '名前',
    'member_phone_fax_title': '電話番号/FAX番号',
    'member_phone': '電話番号',
    'member_position': '役職',
    'notice_add_failed': '追加に失敗しました',
    'notice_add_successfull': '追加に成功しました',
    'notice_copied_successfull': 'コピーに成功しました',
    'notice_bulk_message_operation_failed_delError': 'いくつかのメッセージ削除に失敗しました。再検索してください',
    'notice_bulk_message_operation_failed_unmarkError': 'いくつかのメッセージ削除予約却下に失敗しました。再検索してください',
    'notice_delete_failed': '削除に失敗しました',
    'notice_delete_successfull': '削除に成功しました',
    'notice_import_failed': '一括登録に失敗しました',
    'notice_import_successfull': '一括登録に成功しました',
    'notice_message_passwordReset1': '一時パスワードをご登録メールアドレスにお送りします。',
    'notice_no_result': '該当するデータが存在しません',
    'notice_tracelog_not_found': 'データがまだ反映されていません。しばらくしてから再度実行をしてください',
    'notice_update_failed': '更新に失敗しました',
    'notice_updated_successfull': '更新に成功しました',
    'notice_unmark_successfull': '却下に成功しました',
    'notice_widget_version_not_exist': '入力された小窓のバージョンが存在しません',
    'notification_message_received': 'メッセージを受信しました',
    'placeholder_base_file_name': 'ファイル名を入力してください',
    'placeholder_email': 'メールアドレスを入力してください',
    'placeholder_memo': 'メモを入力してください',
    'placeholder_newPassword': '新規パスワードを入力してください',
    'placeholder_password': 'パスワードを入力してください',
    'placeholder_plusId': 'ログインIDを入力してください',
    'placeholder_tempPassword': '一時パスワードを入力してください',
    'placeholder_textInput_with_file' : 'メッセージを入力（ファイルをドロップ）してください',
    'placeholder_textInput': 'メッセージを入力してください',
    'placeholder_username': 'ユーザー名を入力してください',
    'snack_error_unexpected_error': '予期せぬエラーが発生しました。',
    'snack_error_another_operator_is_corresponding' : '別のオペレーターが対応中です',
    'snack_error_failed_to_create_11room': 'API Error: プライベートルーム作成に失敗しました',
    'snack_error_failed_to_create_NNroom': 'API Error: グループプライベートルーム作成に失敗しました',
    'snack_error_failed_to_fetch_data': 'API Error: データ取得に失敗しました',
    'snack_error_failed_to_file_upload': 'API Error: ファイルアップロードに失敗しました',
    'snack_error_failed_to_invite_user': 'API Error: 招待に失敗しました',
    'snack_error_failed_to_send_message': 'API Error: メッセージ送信に失敗しました',
    'snack_error_failed_to_update_data': 'API Error: データ更新に失敗しました',
    'snack_error_failed_to_update_profile': 'API Error: プロフィール更新に失敗しました',
    'snack_error_offline_detected': 'インターネットに接続できていません',
    'snack_error_online_detected': 'インターネットに再接続しました。ブラウザをリロードして下さい',
    'snack_error_only_executed_sns_user' : '対象ユーザーがSNSユーザーではないため実行できません',
    'snack_error_other_error': 'API Error: エラーが発生しました',
    'success_message_passwordReset1': '入力されたメールアドレスに一時パスワードを送信しました。\n期限内に現在のパスワードを更新してください。\nメールが届かない場合はドメイン管理者にお問い合わせください。',
    'success_message_passwordReset2': 'パスワードが更新されました。新しいパスワードでログインしてください',
    'success_message_userCreated': 'アカウントが作成されました。登録メールアドレスに送信された一時パスワードを使用して1時間以内にパスワードを設定してください',
    'success_password_reset': 'パスワードリセットに成功して一時パスワードを送信しました',
    'system_message_joined_by_other': '#{userName} が #{inviterName} によって招待されました',
    'system_message_joined_by_self': '#{userName} が入室しました',
    'system_message_joined': '#{userName} が入室しました',
    'system_message_left_by_other': '#{userName} が #{inviterName} によって退室させられました',
    'system_message_left_by_self': '#{userName} が退室しました',
    'system_message_left': '#{userName} が退室しました',
    'system_message_title_joined': '入室',
    'system_message_title_left': '退室',
    'template_msg_err_cannot_be_empty': '"{key}"を指定してください',
    'template_msg_err_duplicated_key': '"{key}"が重複しています。',
    'template_msg_err_must_be_empty': '"{key}"は空文字を指定してください',
    'template_msg_err_must_be_in_range_characters': '"{key1}"は{key2}文字で指定してください',
    'template_msg_err_must_be_in_range_items': '"{key1}"は{key2}のitemsを指定する必要があります',
    'template_msg_err_must_be_xxx': '"{key1}" は "{key2}" である必要があります',
    'template_msg_err_durationMs_must_be_in_range': '"durationMs" は {key1} ~ {key2}の間で指定してください',
    'template_msg_err_template_btn_type': '"type" は "message", "postback", "url"のいずれかを指定してください',
    'system_admin_user': 'システム管理者',
    'system_admin_user_register': '権限登録',
    'system_admin_authority': '権限',
    'system_admin_authority_w': '読み書き',
    'system_admin_authority_r': '読み取りのみ',
    'system_admin_authority_noPermission': '未設定',

    // MBA-13493 新満足アンケート

    // [アンケート > パンくず]
    'admin_survey_breadcrumb_survey': '満足度アンケート',

    // [アンケート > 設定]
    'admin_survey_setting_type_title': 'タイプ選択',
    'admin_survey_setting_type_label': 'アンケートタイプ',
    'admin_survey_label_required': '必須',
    'admin_survey_setting_type_not_use': '満足度アンケートを利用しない',
    'admin_survey_setting_type_guest_feedback': '満足度評価のみ利用する',
    'admin_survey_setting_type_original': 'オリジナルアンケートを利用する',
    'admin_survey_setting_type_description': '・ 満足度評価のみ利用する場合、アンケート項目の追加・編集はできません。\n・ オリジナルアンケートを利用する場合、\n　Web小窓設定 > 問い合わせ終了時にルームを終了する 設定は自動的にONとなります。\n　(WEB小窓ウィジェットタイプ＝旧タイプ　の場合、当該設定項目は非表示)\n・ オリジナルアンケートを利用する場合、\n　Web小窓設定 ＞ バージョン選択 で1.43以上を選択してください。\n　1.42以前のバージョンを選択した場合はゲストにアンケートが表示されません。',

    // [アンケート > 設定 > オリジナルアンケート]
    'admin_survey_setting_original_title': 'オリジナルアンケート設定',
    'admin_survey_setting_original_period': '回答有効期限(時間)',
    'admin_survey_setting_original_period_description': 'ゲストがアンケートに回答可能な期限です。',

    // [アンケート > 設定 > 満足度評価文言設定]
    'admin_survey_setting_guest_feedback_title': '満足度評価文言設定',
    'admin_survey_setting_guest_feedback_description': '評価機能関連文言を指定してください。\n(WEB小窓ウィジェットタイプ＝旧タイプ　の場合、これらの設定はゲストの満足度評価に反映されません)',
    'admin_survey_setting_guest_feedback_link': 'レビュー評価リンク',
    'admin_survey_setting_guest_feedback_link_notice': 'Ver.22以降の小窓では表示されません',
    'admin_survey_setting_guest_feedback_overview': 'レビュー 概要説明',
    'admin_survey_setting_guest_feedback_comment': 'レビューコメント',
    'admin_survey_setting_guest_feedback_placeholder': '評価コメント入力プレースホルダー',
    'admin_survey_setting_guest_feedback_rating': '評価',
    'admin_survey_setting_guest_feedback_rating_1': '評価 1',
    'admin_survey_setting_guest_feedback_rating_2': '評価 2',
    'admin_survey_setting_guest_feedback_rating_3': '評価 3',
    'admin_survey_setting_guest_feedback_rating_4': '評価 4',
    'admin_survey_setting_guest_feedback_rating_5': '評価 5',
    'admin_survey_setting_guest_feedback_heading': 'タイトル',
    'admin_survey_setting_guest_feedback_choose': '評価選択',
    'admin_survey_setting_guest_feedback_submit': 'ゲストによる評価送信時',
    'admin_survey_setting_guest_feedback_submit_subtitle': 'オペレーター画面に自動で送信されるシステムメッセージを指定します。ゲスト画面には表示されません。',

    // [アンケート > 一覧 ]
    'admin_survey_list_item_priority': '優先',
    'admin_survey_list_item_title': 'アンケート名',
    'admin_survey_list_item_last_updater': '最終更新者',
    'admin_survey_list_item_registration_date': '登録日',
    'admin_survey_list_item_update_date': '更新日',

    // [アンケート > 追加/編集 アンカーリンク]
    'admin_survey_anchor_title': 'アンケート名設定',
    'admin_survey_anchor_greeting': '挨拶文設定',
    'admin_survey_anchor_items': '項目設定',
    'admin_survey_anchor_thanks': 'お礼文設定',

    // [アンケート > 追加]
    'admin_survey_create_title': 'アンケート追加',
    'admin_survey_section_title': 'アンケート名設定',
    'admin_survey_section_subtitle': 'アンケートを管理するための名称と説明文を設定',
    'admin_survey_section_greeting': 'アンケート挨拶文設定',
    'admin_survey_section_greeting_subtitle': 'アンケート項目の前に表示する挨拶と説明を設定',
    'admin_survey_section_items': 'アンケート項目設定',
    'admin_survey_section_items_subtitle': 'アンケート項目を設定',
    'admin_survey_section_thanks': 'アンケート終了時に表示するお礼メッセージを設定',

    // [アンケート > 追加 > アンケート名設定]
    'admin_survey_section_set_title': 'アンケート名',
    'admin_survey_section_set_description': '説明文',

    // [アンケート > 追加 > アンケート挨拶文設定]
    'admin_survey_section_set_greeting': 'ゲスト挨拶文',
    'admin_survey_section_set_greeting_description': 'ゲスト説明文',
    'admin_survey_section_set_greeting_checkbox': 'アンケートの前に挨拶ページを表示する',
    'admin_survey_section_set_greeting_notice': '※チェックをつけると、ゲストにアンケート項目を表示する前に回答の可否を選択させるための挨拶ページを挟みます。',
    'admin_survey_section_set_greeting_button':'ボタンラベルを設定',
    'admin_survey_section_set_greeting_reject':'拒否ボタン',
    'admin_survey_section_set_greeting_accept':'了承ボタン',

    // [アンケート > 追加 > アンケート項目設定]
    'admin_survey_section_set_items_patterns': 'アンケートパターン',
    'admin_survey_section_set_items_select_patterns': 'アンケートパターンを選択する',
    'admin_survey_section_set_items_multiple': '複数回答可能チェックボックス',
    'admin_survey_section_set_items_single': '単一回答ラジオボタン',
    'admin_survey_section_set_items_5steps': '段階評価（5段階）',
    'admin_survey_section_set_items_nps': '段階評価（NPS）',
    'admin_survey_section_set_items_free': 'フリーコメント',
    'admin_survey_section_set_items_question': '設問',
    'admin_survey_section_set_items_description': '説明文',
    'admin_survey_section_set_items_options': '選択肢',
    'admin_survey_section_set_items_options_button': '選択肢追加',
    'admin_survey_section_set_items_add_button': 'アンケート項目追加',　
    'admin_survey_section_set_items_required_checkbox': '回答を必須とする',
    'admin_survey_section_set_items_nps_subtext': 'NPSで利用できる段階評価のアンケート項目です。',
    'admin_survey_section_set_items_nps_tooltip': 'NPSとはNet Promoter Scoreの略で、顧客のロイヤルティを測る指標です。\n対象者に「この製品/サービスを友人や同僚にどの程度薦めたいと思いますか？」を質問し、0〜10点の11段階で回答を求めます。その点数で回答者を「批判者（0～6点）」「中立者（7、8点）」「推奨者（9、10点）」の3つに分類し、回答者全体の「推奨者」の割合から「批判者」の割合を引いた数値のことを指します。\n例えば、回答者の50%が推奨者で30%が批判者だった場合、NPSは20%になります。',
    'admin_survey_section_set_items_delete_patterns': 'アンケートパターンを削除しますか？',

    // [アンケート > 追加 > アンケート終了時に表示するお礼メッセージを設定]
    'admin_survey_section_set_thanks_complete': '回答完了時お礼文',
    'admin_survey_section_set_thanks_reject': '回答拒否時お礼文',

    // [アンケート > 編集]
    'admin_survey_edit_title': 'アンケート編集',

    // [アンケート > 共通　AlertBox メッセージ]
    'admin_survey_alert_message_saved': '保存しました。',
    'admin_survey_alert_message_deleted': '削除に成功しました。',
    'admin_survey_alert_message_priority': '優先アンケートを変更しました。',

    // [アンケート > 共通　DialogBox メッセージ]
    'admin_survey_dialog_message_priority': '#{title}へ優先設定を変更しますか？',
    'admin_survey_dialog_message_delete': '削除すると元に戻せません。\nアンケートを削除しますか？',
    'admin_survey_dialog_message_edit': 'アンケート内容を変更する場合、すでに送信済み・回答済みのアンケート情報と整合性が取れなくなる可能性があります。',

    // [アンケート > 入力エラー]
    'admin_survey_form_error_required': '#{label}を入力してください。',
    'admin_survey_form_error_minLength': '#{label}の最小文字数(#{min})未満の長さです。',
    'admin_survey_form_error_maxLength': '#{label}の最大文字数(#{max})を超えています。',
    'admin_survey_form_error_minItem': '#{label}は(#{min})個以上必要です。',
    'admin_survey_form_error_maxItem': '#{label}は最大(#{max})個までです。',
    'admin_survey_form_error_enum': '#{label}は不正な形式の文字列です。',
    'admin_survey_form_error_duplicateLabel': '同じ選択肢は登録できません。',
    'admin_survey_form_error_questionsRequired': 'アンケート項目を設定してください。',
    'admin_survey_form_error_optionsRequired': '選択肢は2つ以上設定してください。',
    'admin_survey_form_error_questionTypeRequired': 'アンケートパターンを選択してください。',

    // []
    'admin_callcenter_embed_error_survey': '',

    // [アンケート > データ出力]
    'admin_callcenter_metrics_export_label_surveyExport': '満足度アンケート情報出力',
    'common_label_duration_survey_export': 'アンケート送信期間',
  });
}(window.KonnectPlus));
