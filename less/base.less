html {
  height: 100%;
}

body {
  height: 100%;
  background-color: #fff;
  margin: 0;
}

// MBA-6029 [Admin]Safariでカレンダーを見切れしてしまう
button{
  margin: 0;
}

#konnect {
  width: 100%;
  height: 100%;
  left: 0;
  position:fixed;
  overflow: hidden;
  font-size: 13px;
  line-height: 20px;
  font-family: 'Roboto', sans-serif;
}

#konnect h1,
#konnect h2,
#konnect h3,
#konnect h4,
#konnect h5,
#konnect h6 {
  margin: 0;
}

#konnect a {
  color: #ff4081; //pinkA200
  text-decoration: none;
}

#konnect a:hover {
  text-decoration: underline;
}

.hashTag:hover {
  background-color: rgb(232, 232, 232) !important;
}

// http://stackoverflow.com/questions/2781549/removing-input-background-colour-for-chrome-autocomplete
input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px #FFFFFF inset;
}

.loginInput > input::-ms-clear, .loginInput > input::-ms-reveal {
    display: none;
}

.noselect {
  -webkit-touch-callout: none;
  /* iOS Safari */
  -webkit-user-select: none;
  /* Chrome/Safari/Opera */
  -khtml-user-select: none;
  /* Konqueror */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  /* Non-prefixed version, currently not supported by any browser */
}

.clientFilename {
  white-space: nowrap !important;
}

.flex {
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flex;
  display: -o-flex;
  display: flex;
}

.pre_wrap {
  white-space: pre-wrap;
}
// MBA-4036#comment-54992885
input+hr {
  border-bottom: solid 2px #e0e0e0 !important;
}

// https://mobilus.backlog.jp/view/MBA-5202
.location_lat input,
.location_lon > input {
  box-shadow : none !important;
}

// https://mobilus.backlog.jp/view/MBA-6122
// SelectFieldの選択肢非表示の状態でのスタイル
.StringSelectField > div:first-child > div:nth-child(3) {
  width: 90% !important;
  overflow-x: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// https://mobilus.backlog.jp/view/MBA-6413
// IEのフォームで表示される☓ボタンを隠す
input::-ms-clear,
textarea::-ms-clear,
input::-ms-reveal,
textarea::-ms-reveal {
  display: none;
}

.custom-select-field {
  overflow-y: auto !important;
  max-height: 300px;
}

.select-input-wrapper {
  font-size: 13px !important;
  width: 200px !important;
  height: 32px !important;
  border-radius: 4px;
  border: 1px solid #989A9D;
  top: 0 !important;
  margin: 5px auto 0;
  background-color: #fff;

  &.underline {
    border: none !important;
    border-bottom: 1px solid #989A9D !important;
    border-radius: 0 !important;

    &:hover {
      border-bottom: 1px solid #1FB891 !important;
    }
  }

  &:hover {
    border: 1px solid #1FB891;
  }

  div:nth-child(1) {
    svg {
      top: 0 !important;
      margin: auto;
      bottom: 0;
      fill: #414851 !important;
    }

    div:nth-child(3) {
      line-height: 32px !important;
      top: 0px !important;
      padding-left: 8px !important;
      box-sizing: border-box;
    }
  }

  div:nth-child(2) {
    max-height: 200px !important;
    overflow: auto !important;
    // プルダウンリスト
    & > div {
      padding-left: 16px !important;
      padding-right: 16px !important;
    }
  }
}

.custom-radio-button {
  div {
    div:nth-child(2) {
      margin-right: 5px !important;
    }
  }
}

// 固定幅のinput用クラス
.custom-input-field-fixed {
  .custom-input-field(200px);
}

// 全幅のinput用クラス
.custom-input-field-full {
  .custom-input-field(100%);
}

.custom-input-field(@width) {
  width: @width !important;
  height: 35px !important;
  border: 1px solid #989A9D;
  border-radius: 4px;
  padding: 6px 8px;
  box-sizing: border-box;
  font-size: 13px !important;
  line-height: 20px !important;

  &:focus {
    border: 1px solid #1FB891;
  }

  &:focus-visible {
    outline: none;
    border: 1px solid #1FB891;
  }

  &:disabled {
    background: #DDDDDE;
    border: 1px solid #989A9D;
  }

  &:required {
    border: 1px solid #F44336;
    background: #FEECEB;
  }
}

.custom-textarea-field {
  width: 100% !important;
  height: auto !important;
  border: 1px solid #989A9D;
  border-radius: 4px;
  padding: 6px 8px;
  box-sizing: border-box;
  font-size: 13px !important;
  line-height: 20px !important;

  div:nth-child(1) {
    textarea:nth-child(2) {
      resize: vertical !important;
      margin: 0 !important;
    }
  }

  &:focus {
    &:extend(.custom-input-field:focus);
  }

  &:focus-visible {
    &:extend(.custom-input-field:focus-visible);
  }

  &:disabled {
    background: #DDDDDE;
    border: 1px solid #989A9D;
  }

  &:required {
    &:extend(.custom-input-field:required);
  }
}

div.select-input-wrapper ~ hr,
.custom-input-field-fixed hr,
.custom-input-field-full hr,
.custom-textarea-field hr {
  visibility: hidden !important;
}

//https://mobilus-corp.atlassian.net/browse/MBAJ-263
.cssEditor.safari .ace_scrollbar-inner {
  background-color: white;
  position: initial !important;
}

input:-moz-ui-invalid {
  box-shadow: none;
}

.dynamic_search_textfield > ::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  font-size: 14px;
}
.dynamic_search_textfield > ::-moz-placeholder { /* Firefox 19+ */
  font-size: 14px;
}
.dynamic_search_textfield > :-ms-input-placeholder { /* IE 10+ */
  font-size: 14px;
  transform: scale(0.8) translateX(-54px);
}
.dynamic_search_textfield > ::-ms-input-placeholder { /* Edge */
  font-size: 14px;
  transform: scale(0.8) translateX(-54px);
}
.dynamic_search_textfield > :-moz-placeholder { /* Firefox 18- */
  font-size: 14px;
}
/* materia-uiのTextFieldがdisabledのときにplaceholderが濃く表示されたので、指定した場所では薄くなるようにした */
.placeholder_disabled_lighter :disabled::placeholder {
  color: rgba(0,0,0,0.1)
}
.sysadm_login_as_domain_adm:before {
  content: attr(data-label);
  position: absolute;
  color: #FFF;
  display: block;
  z-index: 99999;
  padding: 0 7.5px;
  background-color: #FC4349;
  white-space: nowrap;
  height: 56px;
  line-height: 56px;
  right:40%;
}
.operatorAssign_radioButton > input[disabled] {
  pointer-events: none !important;
}

.colorCircleIcon {
  border-radius: 15px;
  background: gray;
  height: 20px;
  width: 20px;
}
.required {
  display: flex;
  align-items: center;
  &::after {
    content: '*';
    font-size: 12px;
    margin-left: 3px;
    color: #f44336;
  }
}

//内部チャットコードハイライト
.codeHighlighter span {
  margin-right: 0;
}

.pln {
  color: #60ac39;
}

@media screen {

  /* string content */
  .str {
    color: #60ac39;
  }

  /* keyword */
  .kwd {
    color: #b854d4;
  }

  /* comment */
  .com {
    color: #999580;
  }

  /* type name */
  .typ {
    color: #6684e1;
  }

  /* literal value */
  .lit {
    color: #b65611;
  }

  /* punctuation */
  .pun {
    color: #20201d;
  }

  /* lisp open bracket */
  .opn {
    color: #20201d;
  }

  /* lisp close bracket */
  .clo {
    color: #20201d;
  }

  /* markup tag name */
  .tag {
    color: #d73737;
  }

  /* markup attribute name */
  .atn {
    color: #b65611;
  }

  /* markup attribute value */
  .atv {
    color: #1fad83;
  }

  /* declaration */
  .dec {
    color: #b65611;
  }

  /* variable name */
  .var {
    color: #d73737;
  }

  /* function name */
  .fun {
    color: #6684e1;
  }
}
