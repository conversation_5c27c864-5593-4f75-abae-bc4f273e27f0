@font-face {
  font-family: 'icomoon';
  src:  url('fonts/icomoon.eot?o4aryh');
  src:  url('fonts/icomoon.eot?o4aryh#iefix') format('embedded-opentype'),
    url('fonts/icomoon.ttf?o4aryh') format('truetype'),
    url('fonts/icomoon.woff?o4aryh') format('woff'),
    url('fonts/icomoon.svg?o4aryh#icomoon') format('svg');
    /* src:  url('fonts/icomoon.eot?greai');
    src:  url('fonts/icomoon.eot?greai#iefix') format('embedded-opentype'),
      url('fonts/icomoon.ttf?greai') format('truetype'),
      url('fonts/icomoon.woff?greai') format('woff'),
      url('fonts/icomoon.svg?greai#icomoon') format('svg'); */
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-list_alt:before {
  content: "\e92a";
}
.icon-cloud_download:before {
  content: "\e928";
}
.icon-headset_mic:before {
  content: "\e929";
}
.icon-data_usage:before {
  content: "\e927";
}
.icon-shop:before {
  content: "\e91e";
}
.icon-tag_faces:before {
  content: "\e91d";
}
.icon-arrow_drop_down:before {
  content: "\e923";
}
.icon-fiber_new:before {
  content: "\e924";
}
.icon-pie_chart:before {
  content: "\e925";
}
.icon-vpn_key:before {
  content: "\e926";
}
.icon-ic_assessment_black_24px:before {
  content: "\e900";
}
.icon-ic_block_black_24px:before {
  content: "\e901";
}
.icon-ic_build_black_24px:before {
  content: "\e919";
}
.icon-ic_chat_bubble_black_24px:before {
  content: "\e902";
}
.icon-ic_chrome_reader_mode_black_24px:before {
  content: "\e903";
}
.icon-ic_clear_black_24px:before {
  content: "\e91c";
}
.icon-ic_contact_mail_black_24px:before {
  content: "\e904";
}
.icon-ic_content_paste_black_24px:before {
  content: "\e905";
}
.icon-ic_credit_card_black_24px:before {
  content: "\e906";
}
.icon-ic_domain_black_24px:before {
  content: "\e907";
}
.icon-ic_email_black_24px:before {
  content: "\e908";
}
.icon-ic_error_outline_black_24px:before {
  content: "\e909";
}
.icon-ic_exit_to_app_black_24px:before {
  content: "\e90a";
}
.icon-ic_find_in_page_black_24px:before {
  content: "\e90b";
}
.icon-ic_folder_black_24px:before {
  content: "\e90c";
}
.icon-ic_folder_shared_black_24px:before {
  content: "\e90d";
}
.icon-ic_format_list_bulleted_black_24px:before {
  content: "\e90e";
}
.icon-ic_forum_black_24px:before {
  content: "\e90f";
}
.icon-ic_history_black_24px:before {
  content: "\e910";
}
.icon-ic_image_black_24px:before {
  content: "\e911";
}
.icon-ic_lightbulb_outline_black_24px:before {
  content: "\e912";
}
.icon-ic_mail_outline_black_24px:before {
  content: "\e913";
}
.icon-ic_menu_black_24px:before {
  content: "\e91b";
}
.icon-ic_notifications_black_24px:before {
  content: "\e91a";
}
.icon-ic_people_outline_black_24px:before {
  content: "\e914";
}
.icon-ic_perm_contact_calendar_black_24px:before {
  content: "\e915";
}
.icon-ic_person_outline_black_24px:before {
  content: "\e916";
}
.icon-ic_sentiment_satisfied_black_24px:before {
  content: "\e917";
}
.icon-ic_settings_black_24px:before {
  content: "\e918";
}
.icon-checkmark:before {
  content: "\e91f";
}
.icon-clipboard:before {
  content: "\e9b8";
}
.icon-ic_info_outline_black_24px:before {
  content: "\e922";
}
.icon-ic_mode_edit_black_24px:before {
  content: "\e920";
}
.icon-ic_file_download_black_24px:before {
  content: "\e921";
}
.icon-ic_file_download_white_24px:before {
  content: "\e921";
  color: white;
  font-size: 24px;
  position: absolute;
  left: 1px;
  top: 10px;
}
.icon-plus:before {
  content: "\ea0a";
}
.icon-minus:before {
  content: "\ea0b";
}
.icon-circle-right:before {
  content: "\ea42";
}
.icon-circle-left:before {
  content: "\ea44";
}
.icon-eye:before {
  content: "\e9ce";
}
.icon-eye-blocked:before {
  content: "\e9d1";
}
.icon-sentiment_positive:before {
  content: "\e9e0";
  color: #4D86EC;
}
.icon-sentiment_angry:before {
  content: "\e9e6";
  color: #EC544D;
}
.icon-sentiment_problem:before {
  content: "\e9f4";
  color: #ECB34D;
}
.icon-sentiment_neutral:before {
  content: "\e9f8";
  color: #9EB0C6;
}
