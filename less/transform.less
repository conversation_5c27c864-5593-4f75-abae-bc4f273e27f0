// .konnectFrameBottomUp-enter {
//   transform: translate3d(0, 40px, 0);
//   transition-duration: 1s;
// }
//
// .konnectFrameBottomUp-enter.konnectFrameBottomUp-enter-active {
//   transform: translate3d(0, 0px, 0);
// }
//
// .konnectFrameBottomUp-leave {
//   transition-duration: 1s;
// }
//
// .konnectFrameBottomUp-leave.konnectFrameBottomUp-leave-active {
//   transform: translate3d(0, 40px, 0);
// }
//
// .konnectFrameBodyBottomUp-enter {
//   transform: translate3d(0, 40px, 0);
//   transition-duration: 1s;
// }
//
// .konnectFrameBodyBottomUp-enter.konnectFrameBodyBottomUp-enter-active {
//   transform: translate3d(0, 0px, 0);
// }
//
// .konnectFrameBodyBottomUp-leave {
//   transition-duration: 1s;
// }
//
// .konnectFrameBodyBottomUp-leave.konnectFrameBodyBottomUp-leave-active {
//   transform: translate3d(0, 40px, 0);
// }