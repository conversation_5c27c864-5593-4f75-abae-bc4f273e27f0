@import url(https://fonts.googleapis.com/css?family=Roboto:400,300,500);

@font-face {
  font-family: 'Material Icons';
  font-style: normal;
  font-weight: 400;
  src: url('https://cdn.rawgit.com/google/material-design-icons/a6145e16/iconfont/MaterialIcons-Regular.eot');
  /* For IE6-8 */
  src: url('https://cdn.rawgit.com/google/material-design-icons/a6145e16/iconfont/MaterialIcons-Regular.woff2') format('woff2'),
       url('https://cdn.rawgit.com/google/material-design-icons/a6145e16/iconfont/MaterialIcons-Regular.woff') format('woff'),
       url('https://cdn.rawgit.com/google/material-design-icons/a6145e16/iconfont/MaterialIcons-Regular.ttf') format('truetype');
}

.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  /* Preferred icon size */
  display: inline-block;
  line-height: 1;
  text-transform: none;
  letter-spacing: normal;
  word-wrap: normal;
  white-space: nowrap;
  direction: ltr;
  /* Support for all WebKit browsers. */
  -webkit-font-smoothing: antialiased;
  /* Support for Safari and Chrome. */
  text-rendering: optimizeLegibility;
  /* Support for Firefox. */
  -moz-osx-font-smoothing: grayscale;
  /* Support for IE. */
  font-feature-settings: 'liga';
}


@import (inline) "./icomoon/style.css";
@import "base.less";
@import "transform.less";
@import "fixed_table.less"; // fork from fixed-data-table customized by mobilus
@import "paging.less"; // fork from react-paginate customized by mobilus
@import "./components/admin/manage/callcenter/chatWindow.less";
@import "./components/admin/manage/callcenter/optionsUi.less";
@import "./components/admin/manage/preferences/fieldRow.less";
@import "./components/admin/manage/preferences/periodSettingsEditor.less";
@import "./components/staticPages/LoginForm.less";
@import "./components/survey/survey.less";

@import (inline) "../node_modules/react-tooltip/dist/react-tooltip.min.css";
@import (inline) "../node_modules/nvd3/build/nv.d3.min.css";

@import (inline) "ace_ime_jp_font.css";
