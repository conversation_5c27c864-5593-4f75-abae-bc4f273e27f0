// tab
@chatwindow-tab-width: 145px; //一番長いタブの文字数に合わせて調整

.chatwindow-tabs  {
    // tabs
    &>div:nth-child(1) {
      .setting-tab {
        padding: 15px;
        color: #000 !important;
        width: @chatwindow-tab-width !important;
        height: auto !important;
        display: inline-flex !important;
        align-items: center;
        justify-content: center;
        &.deactivated {
          color: rgba(0, 0, 0, 0.4) !important;
        }
      }
    }
    //tabsの下線
    &>div:nth-child(2) {
      //tabの下線 5つタブ前提、それ以外の場合要調整
      width: calc(@chatwindow-tab-width * 5) !important;

      div {
        width: @chatwindow-tab-width !important;
      }
    }
}
