.survey-section {
  margin: 0 auto 32px;

  &__title {
    line-height: 1.8;
  }

  &__lead {
    font-size: 16px;
    line-height: 1.4;
    margin: 8px auto 20px;
  }
}

.survey-breadcrumbs {
  display: inline-block;
  background: #F5F5F5;
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 6px 12px;
  font-size: 12px;

  &__top {
    color: #1CAF9A !important;
    cursor: pointer;
    text-decoration: none !important;
  }

  &__slash {
    color: #878482;
  }

  &__sub {
    color: #333;
  }
}

.survey-top-container {
  &__subtitle {
    font-size: small;
    padding-left: 10px;
    white-space: nowrap;
  }
}

.survey-dialog {
  width: 40% !important;
  
  &>div {

    &>h3 {
      text-align: center;
    }

    // 本文
    &>div:nth-of-type(1) {
      border-bottom: 1px solid #B1B3BE;
      text-align: center;
    }

    // Button
    &>div:nth-of-type(2) {
      padding: 0 !important;
      text-align: center !important;
    }
  }

  button {
    border-radius: 4px !important;
  }

}

.survey-dialog_text{
  word-break: break-all;
  white-space: normal;
}

.survey-dialog_delete_message{
  word-break: break-all;
  white-space: pre-wrap;
}



.survey-form {
  margin: 16px auto;
  width: 100%;

  &__labelWrap {
    display: flex;
    align-items: center;
  }

  &__label {
    font-weight: bold;
  }

  &__caption {
    font-size: 11px;
    color: rgba(0, 0, 0, 0.6);
    font-weight: normal;
    padding: 0;
    margin: 0;
    white-space: pre-wrap;   // 文字列のwhite space, newline charactersを出力する
  }

  &__error {
    color: #F44336;
    font-size: 11px;
    margin: 5px 0 0;
    padding-left: 20px;
    background: url(/assets/images/admin/alert/error.svg) no-repeat top left / 16px,
  }

  &__required {
    font-size: 10px;
    color: #fff;
    padding: 0 5px;
    background: #E96060;
    border-radius: 2px;
    margin-left: 10px;
  }
}

// MUIのselectカスタマイズ
.selectField-wrap {
  height: 40px !important;

  .survey-selectField {
    font-size: 13px !important;
    width: 320px !important;
    height: 32px !important;
    border-radius: 4px;
    border: 1px solid #989A9D;
    top: 0 !important;
    margin: 5px auto 0;
    background-color: #fff;

    &:hover {
      border: 1px solid #1FB891;
    }

    div:nth-child(1) {
      svg {
        top: 0 !important;
        margin: auto;
        bottom: 0;
        fill: #414851 !important;
      }

      div:nth-child(3) {
        line-height: 32px !important;
        top: 0px !important;
        padding-left: 8px !important;
        box-sizing: border-box;
      }
    }

    div:nth-child(2) {
      max-height: 200px !important;
      overflow: auto !important;
      // プルダウンリスト
      & > div {
        padding-left: 16px !important;
        padding-right: 16px !important;
      }
    }
  }

  .survey-selectField.error {
    border: 1px solid #F44336;
    background: #FEECEB;
  }

  .survey-selectField~hr {
    display: none;
  }

}


// input[type="text"]
.survey-inputField {
  width: 100%;
  height: 32px;
  border: 1px solid #989A9D;
  border-radius: 4px;
  padding: 6px 8px;
  box-sizing: border-box;
  font-size: 13px;
  line-height: 20px;
  margin: 5px auto 0;
  font-family: 'Roboto', sans-serif;

  &:focus {
    border: 1px solid #1FB891;
  }

  &:focus-visible {
    outline: none;
    border: 1px solid #1FB891;
  }

  &:disabled {
    background: #DDDDDE;
    border: 1px solid #989A9D;
  }

  &:required {
    border: 1px solid #F44336;
    background: #FEECEB;
  }

}

// textarea
.survey-textareaField {
  resize: vertical;
  width: 100%;
  height: auto;
  padding: 8px;
  &:extend(.survey-inputField);

  &:focus {
    &:extend(.survey-inputField:focus);
  }

  &:focus-visible {
    &:extend(.survey-inputField:focus-visible);
  }

  &:disabled {
    background: #DDDDDE;
    border: 1px solid #989A9D;
  }

  &:required {
    &:extend(.survey-inputField:required);
  }
}

// tab
@tab-width: 80px;

.survey-tabs {

  // tabs
  &>div:nth-child(1) {
    display: flex !important;
    height: auto !important;
    min-height: 32px;

    // tab
    .survey-tab {
      padding: 8px;
      color: #000 !important;
      width: @tab-width !important;
      height: auto !important;
      min-height: 32px;
      display: flex !important;
      align-items: center;
      justify-content: center;

      &.deactivated {
        color: rgba(0, 0, 0, 0.4) !important;
      }
    }
  }

  //tabsの下線
  &>div:nth-child(2) {
    width: calc(@tab-width * 2) !important;

    //tabの下線 2つタブ前提、それ以外の場合要調整
    div {
      width: @tab-width !important;
    }
  }
}


.survey-table {

  // table
  .public_fixedDataTable_main {
    border: none !important;
    width: 100% !important;
  }

  .public_fixedDataTable_header {
    color: rgba(0, 0, 0, 0.54) !important;
    box-shadow: none !important;
  }

  .public_fixedDataTableRow_highlighted .public_fixedDataTableCell_main {
    background-color: #fff !important;
  }

  .public_fixedDataTableCell_main {
    a {
      text-decoration: none !important;
    }
  }

  .public_fixedDataTableRow_main {
    border-bottom: 1px solid #D3D3D3;
    border-radius: 0;
  }

  .survey-table-icon {
    height: 50px !important;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.54)!important;

    .material-icons {
      font-size: 18px !important;
      color: rgba(0, 0, 0, 0.4)!important;
      fill: rgba(0, 0, 0, 0.4)!important;
    }
  }

  // radio-button
  .survey-radio {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #BBBBBD;
    border-radius: 50%;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    padding: 0;
    margin: 0;
    cursor: pointer;

    &:checked {
      border: 2px solid #1FB891;
    }

    &:hover {
      &.survey-radio:checked {
        cursor: default;
      }
    }

    &:checked::after {
      content: '';
      position: absolute;
      left: 3px;
      top: 3px;
      display: block;
      border-radius: 50%;
      width: 10px;
      height: 10px;
      background-color: #1FB891;
    }
  }

  .survey-link {
    color: #1CAF9A !important;
    text-decoration: none !important;
  }
}

// 交互に背景色をつけるテーブル用クラス
.table-striped {
  // 偶数行
  .fixedDataTableRowLayout_rowWrapper .public_fixedDataTableRow_even {
    .public_fixedDataTableCell_main {
      background-color: #f9f9f9 !important;
    }
  }
}
.survey-button-wrap {
  text-align: center;
  margin: 16px auto 0;
}

.survey-buttonLabel {
  margin: 16px 0 0 20px;

  &__title {
    font-size: 14px;
    line-height: 1.6;
    margin: 0 0 8px;
  }

  &__inner {
    background: #F5F5F5;
    display: inline-flex;
    padding: 8px;
    gap: 20px;
    margin: 5px auto 0;

    .survey-form {
      margin: 0;
    }
  }

}

.survey-pageLink {
  display: flex;
  justify-content: flex-end;
  margin: -20px -20px 10px;

  &__list {
    list-style: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    margin: 0 0 0 30px;
  }

  &__icon {
    margin-right: 10px;
    display: inline-block;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 5px 0 5px;
    border-color: #1CAF9A transparent transparent transparent;
  }

  &__link {
    display: block;
    color: #1CAF9A !important;
    text-decoration: none !important;
    font-size: 16px;
    line-height: 19px;
  }
}

.survey-section-question {
  margin: 16px auto;
  position: relative;

  .survey-question-deleteButton {
    position: absolute;
    right: -30px;
    top: -20px;
  }
}

.survey-question-items {
  display: flex;

  &__num {
    font-weight: 700;
    font-size: 14px;
    color: #000;
    line-height: 16px;
    padding: 5px 10px;
  }

  &__wrap {
    background: #F5F5F5;
    padding: 8px 8px 16px;
    width: 100%;
  }

  &__subtext {
    display: flex;
    align-items: center;

     p {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.54);
      margin: 0;
    }
  }

  // NPSツールチップのカスタマイズ
  &__icon {
    & > div {
      & > div {
        text-align: left;
        width: 562px;
        line-height: 1.6 !important;
        padding: 8px !important;
        border-radius: 4px !important;
        background: rgba(0, 0, 0, 0.54) !important;
        top: -90px !important;
        & > span {
          white-space: pre-wrap !important;
          font-size: 11px;
        }
      }
    }
  }
}

.survey-question-options {

  &__wrap {
    display: flex;
  }

  &__deleteButton {
    position: relative;
    top: 33px;
  }
  &__bottom {
    text-align: center;
    & > div {
      width: 184px !important;
      margin-right: 28px; //  「アンケート項目追加」ボタンを揃えるため
      box-shadow: none !important;
    }
  }
  &__button {
    border-radius: 4px !important;
    border: 2px solid #1CAF9A !important;
    height: calc(100% + 4px) !important;
    &:hover {
      background-color: #E2F7F1 !important;
    }
  }
}

.survey-toTop {
  position: fixed;
  right: 24px;
  bottom: 76px;
  background: #fff;
  width: 48px;
  height: 48px;
	border: 2px solid #1CAF9A;
	outline: none;
  border-radius: 50%;
  box-shadow: rgba(0, 0, 0, 0.16) 0 3px 6px, rgba(0, 0, 0, 0.23) 0 3px 10px;
  z-index: 100;
  cursor: pointer;

  &:hover {
    border: 2px solid #82D7C1;
    opacity: 0.8;
  }

  &__icon {
    display: inline-block;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 8.54px 8.54px 8.54px;
    border-color: transparent transparent #1CAF9A transparent;
  }

  &__text {
    display: block;
    font-size: 10px;
    font-weight: 700;
    color: #1CAF9A;
  }
}


.survey-footer {
  position: fixed;
  bottom: 0;
  padding: 16px 24px;
  background: rgba(238, 238, 239, 0.6);
  width: calc(100% - 72px);
  left: 24px;
  display: flex;
  justify-content: flex-end;

  button {
    border-radius: 4px !important;
  }
}


.survey-reviewSetting {
  &__lead {
    white-space: pre-wrap;
  }
}
