import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  get() {
    request
      .get(`${APIRoot.get()}/api/sysconf`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        auth_domainId: 'anonymous',
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_SYSCONF,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_SYSCONF,
            data: res.body
          });
        }
      });
  },

  post(params, actionType) {
    const data = {
      auth_domainId: 'anonymous',
    };
    Object.keys(params).forEach((key) => {
      data[`param_${key}`] = params[key];
    });
    request
      .post(`${APIRoot.get()}/api/sysconf`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(data)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.POST_SYSCONF_NG,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.POST_SYSCONF_OK,
            data: res.body,
            actionType
          });
        }
      });
  },

  getSysadmPermits(params) {
    return new Promise((resolve) => {
      const data = params;
      request
        .get(`${APIRoot.get()}/api/sysconf/sysadmPermits`)
        .withCredentials()
        .type('form')
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .send(data)
        .query({
          auth_domainId: 'anonymous',
        })
        .end((err, res) => {
          if(err){
            Utils.logoutIfneccessary(err);
          }else{
            resolve(res.body);
          }
        });
    });
  }
};

export default Action;
