import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const DEFAULT_FILE_NAME = 'snippet.json';

const Action = {
  get(params={}) {

    const query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };

    let domainIdInUrl = encodeURIComponent(params.domainId);
    if (APIRoot.getAuthDomainId() === '') {
      domainIdInUrl = 'pub';
    } else if (APIRoot.getAuthDomainId() !== null) {
      domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
    }
    if (domainIdInUrl === ''){
      domainIdInUrl = 'pub';
    }

    const fileName = params.fileName || DEFAULT_FILE_NAME;

    request
      .get(`${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${fileName}`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_SNIPPETS,
            data: {
              error: res.status
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_SNIPPETS,
            error: 0,
            data: Utils.tryJSONParse(res.text)
          });
        }
      });
  },

  post(params={}) {

    let domainIdInUrl = encodeURIComponent(params.domainId);
    if (APIRoot.getAuthDomainId() === '') {
      domainIdInUrl = 'pub';
    } else if (APIRoot.getAuthDomainId() !== null) {
      domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
    }
    if (domainIdInUrl === ''){
      domainIdInUrl = 'pub';
    }

    const fileName = params.fileName || DEFAULT_FILE_NAME;

    return new Promise((resolve, reject) => {
      const form = new FormData();
      const reqJSON = Utils.attachLastUpdateToJSONString(params.json);
      const blob = new Blob([reqJSON], {type: 'application/json'});
      form.append('file', blob, fileName);
      form.append('contentType', 'application/json; charset=utf-8');
      form.append('exposeToAll', false);
      form.append('noNeedAuth', false);
      if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId', APIRoot.getAuthDomainId());
      const req = new XMLHttpRequest();
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${fileName}`);
      req.onload = function() {
        if (req.status === 200) {
          const result = req.responseText;
          const data = Utils.tryJSONParse(result);
          if (data.error === 0){
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject({error: -1});
        }
      };
      req.withCredentials = true;
      req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
      req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
      req.send(form);
    })
    .then((data) => {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.PUT_SNIPPETS_OK,
        data,
        json: params.json
      });
    }, (data) => {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.PUT_SNIPPETS_NG,
        data
      });
    });
  }
};
export default Action;
