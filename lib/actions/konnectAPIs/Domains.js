import assign from 'object-assign';
import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  getById(params) {
    const targetDomainId = APIRoot.getAuthDomainId() ? APIRoot.getAuthDomainId() : params.domainId;
    request
      .get(APIRoot.get() + '/api/domains/' + encodeURIComponent(targetDomainId))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_DOMAIN,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_DOMAIN,
            data: res.body
          });
        }
      });
  },

  put(params){
    const data = assign({
      _method: 'put',
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
    }, params);
    request
      .post(APIRoot.get() + '/api/domains/' + encodeURIComponent(params.domainId))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(data)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.PUT_DOMAIN_NG,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.PUT_DOMAIN_OK,
            data: res.body
          });
        }
      });
  }
};

export default Action;
