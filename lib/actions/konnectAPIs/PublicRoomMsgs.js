import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';
import _ from 'lodash-compat'

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  select(params) {
    const query = {
      limit: 100,
      sortAsc: params.sortAsc ? 'true' : 'false',
      [params.prevId ? 'prevId' : '_ignore'] : params.prevId,
      [params.internalPrevId ? 'internalPrevId' : '_ignore'] : params.internalPrevId,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
      internal: params.internal ? 'true' : 'false',
      format: 'raw',
    };
    request
      .get(`${APIRoot.get()}/userapi/msgsPeekPublicRoom/${encodeURIComponent(params.roomId)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.SELECT_PUBLICROOM_MSGS,
            data: {
              error: -1
            },
            query,
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.SELECT_PUBLICROOM_MSGS,
            data: res.body,
            query,
          });
        }
      });
  }
};
export default Action;
