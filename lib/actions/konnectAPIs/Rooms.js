import _ from 'lodash-compat';
import assign from 'object-assign';
import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();
const Action = {
  getById(roomId) {
    const params = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
    .get(`${APIRoot.get()}/api/rooms/` + encodeURIComponent(roomId))
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .query(params)
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_GET,
          data: {
            error: -1
          }
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_GET,
          data: {
            room: res.body.room
          }
        });
      }
    });
  },

  getIconById(roomId) {
    const params = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
    .get(`${APIRoot.get()}/api/roomIcon/` + encodeURIComponent(roomId))
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .query(params)
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_ICON_GET,
          data: {
            error: -1
          }
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_ICON_GET,
          data: res.body,
          error: 0
        });
      }
    });
  },

  getBgrById(roomId) {
    const params = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
    .get(`${APIRoot.get()}/api/roomBgImg/` + encodeURIComponent(roomId))
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .query(params)
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_BGR_GET,
          data: {
            error: -1
          }
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_BGR_GET,
          data: res.body,
          error: 0
        });
      }
    });
  },

  fulltextRoomSearch(params={}, callback) {
    // @TODO @FIXME
    // viewからの引数をそのまま投げずにサーバーが期待しているパラメータだけにしたほうが良さそう
    const query = assign({}, params)
    query[APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore'] = APIRoot.getAuthDomainId();
    const apiURL = `${APIRoot.get()}/api/roomSearch`;
    request
    .get(apiURL)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .query(query)
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_TEXT_SEARCH,
          data: {
            error: -1
          }
        });
      } else {
        if (callback && typeof callback === 'function') {
            callback(res);
            AppDispatcher.dispatch({
              type: BasicAPIEvents.ROOMS_TEXT_SEARCH,
              error: 0
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.ROOMS_TEXT_SEARCH,
              data: res.body,
              error: 0
            });
          }
      }
    });
  },

  fulltextRoomSearchES(params={}, callback) {
    // @TODO @FIXME
    // viewからの引数をそのまま投げずにサーバーが期待しているパラメータだけにしたほうが良さそう
    const query = assign({}, params);
    query[APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore'] = APIRoot.getAuthDomainId();
    const apiURL = `${APIRoot.get()}/api/roomSearchES`;
    request
    .get(apiURL)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .query(query)
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_TEXT_SEARCH,
          data: {
            error: -1
          }
        });
      } else {
        if (callback && typeof callback === 'function') {
            callback(res);
            AppDispatcher.dispatch({
              type: BasicAPIEvents.ROOMS_TEXT_SEARCH,
              error: 0
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.ROOMS_TEXT_SEARCH,
              data: res.body,
              error: 0
            });
          }
      }
    });
  },

  updateIconById(roomId, fileIcon) {
    new Promise((resolve, reject) => {
      const form = new FormData();
      form.append('data', fileIcon);
      if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId', APIRoot.getAuthDomainId());
      const req = new XMLHttpRequest();
      req.open('POST', `${APIRoot.get()}/api/roomIcon/` + encodeURIComponent(roomId));
      req.onload = function() {
        if (req.status === 200) {
          const result = req.responseText;
          const data = Utils.tryJSONParse(result);
          if (data.error === 0){
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject({error: -1});
        }
      };
      req.withCredentials = true;
      req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
      req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
      req.send(form);
    })
    .then(
      (data) => {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_ICON_UPDATE,
          data,
          roomId
        });
      },
      () => {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_ICON_UPDATE,
          roomId,
        });
      }
    );
  },

  updateBgrById(roomId, fileBgr) {
    new Promise((resolve, reject) => {
      const form = new FormData();
      form.append('data', fileBgr);
      if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId', APIRoot.getAuthDomainId());
      const req = new XMLHttpRequest();
      req.open('POST', `${APIRoot.get()}/api/roomBgImg/` + encodeURIComponent(roomId));
      req.onload = function() {
        if (req.status === 200) {
          const result = req.responseText;
          resolve(result);
        } else {
          reject();
        }
      };
      req.withCredentials = true;
      req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
      req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
      req.send(form);
    })
    .then(
      (value) => {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_BGR_UPDATE,
          data: {
            value
          },
          roomId
        });
      },
      () => {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOM_BGR_UPDATE,
          data: {
            error: -1
          },
          roomId,
        });
      }
    );
  },

  updateRoomPQPA(roomId, pqpa) {
    const queryParameters = {
      tag30: JSON.stringify(pqpa),
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    }
    request
      .post(APIRoot.get() + '/plusccapi/roomInfo/' + encodeURIComponent(roomId) + '/misc')
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(queryParameters)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.ROOM_PQPA_UPDATE_NG,
            data: err,
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.ROOM_PQPA_UPDATE_NG,
              data: res.body.error,
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.ROOM_PQPA_UPDATE_OK,
              data: pqpa,
            });
          }
        }
      });
  },

  post1To1(userId1, userId2, world, roomName, roomDesc) {
    request
    .post(`${APIRoot.get()}/api/oneToOneRoom`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send({
      'oneToOneUserId1': userId1,
      'oneToOneUserId2': userId2,
      world,
      'name': roomName,
      'desc': roomDesc,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    })
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_CREATE,
          data: {
            error: -1
          },
          roomName
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_CREATE,
          data: {
            error: 0,
            roomId: res.body.roomId
          }
        });
      }
    });
  },

  postNToN(members, privateRoom, world, roomName, roomDesc) {
    request
    .post(`${APIRoot.get()}/api/rooms`)
    .withCredentials()
    .type('form')
    .send({
      'csrf-token': antiCsrfToken,
      members,
      'private': privateRoom,
      world,
      'name': roomName,
      'desc': roomDesc,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    })
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_CREATE,
          data: {
            error: -1
          },
          roomName,
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_CREATE,
          data: {
            error: 0,
            roomId: res.body.room.id
          }
        });
      }
    });
  },

  deleteByRoomId(roomId) {
    request
    .delete(`${APIRoot.get()}/api/rooms/`)
    .withCredentials()
    .type('form')
    .send({
      'csrf-token': antiCsrfToken,
      roomId,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    })
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_DELETE,
          data: {
            error: -1
          }
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_DELETE,
          data: res.body
        });
      }
    });
  },

  updateById(roomId, roomName, roomDesc) {
    request
    .put(`${APIRoot.get()}/api/rooms/` + encodeURIComponent(roomId))
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send({
      'name': roomName,
      'desc': roomDesc,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    })
    .end((err) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_UPDATE,
          data: {
            error: -1
          },
          roomId,
          roomDesc,
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_UPDATE,
          data: {
            error: 0
          }
        });
      }
    });
  },

  changeUserGroup(roomType) {
    AppDispatcher.dispatch({
      type: BasicAPIEvents.GROUP_CHANGE,
      data: {
        roomType,
        error: 0,
      }
    });
  },


  // roomType  必須  "11": 1:1ルーム、"1N": 1:Nルーム、"Public": パブリックルーム
  // myDomainOnly  任意  true: 自分のドメインの所有するルームのみを返す。false: 自分から見えるルームを全て返す。(roomType="Public" の場合のみ有効。デフォルト: true)
  // fromTime  任意  指定した日時以降に作成されたルームを検索する。(Unix time)
  // toTime  任意  指定した日時より前に作成されたルームを検索する。(Unix time)
  // skipNum  任意  検索結果リストの先頭を返さないようにする (デフォルト: 0)
  // limit  任意  検索結果の最大数 (デフォルト: 100。最大:100)
  // sortAsc  任意  検索結果のソート順。(true: ルームIDの昇順、false: 降順)(デフォルトは昇順)
  // world  任意  ワールド
  // name  任意  名称
  // desc  任意  説明
  // icon  任意  アイコン
  // bgImg  任意  背景画像
  // extra  任意  アプリケーション固有拡張属性
  // tag1  任意  アプリケーション固有拡張属性
  // tag2  任意  アプリケーション固有拡張属性
  search(roomType='11', myDomainOnly=true,
         fromTime='', toTime='',
         skipNum=0, limit=100, sortAsc=true,
         roomExtraInfo={}){

    const query = {
      roomType,
      myDomainOnly,
      [fromTime ? 'fromTime' : '_ignore']: fromTime,
      [toTime ? 'toTime' : '_ignore']: toTime,
      skipNum,
      limit,
      sortAsc,
      [roomExtraInfo.world ? 'world' : '_ignore']: roomExtraInfo.world,
      [roomExtraInfo.name ? 'name' : '_ignore']: roomExtraInfo.name,
      [roomExtraInfo.desc ? 'desc' : '_ignore']: roomExtraInfo.desc,
      [roomExtraInfo.icon ? 'icon' : '_ignore']: roomExtraInfo.icon,
      [roomExtraInfo.bgImg ? 'bgImg' : '_ignore']: roomExtraInfo.bgImg,
      [roomExtraInfo.extra ? 'extra' : '_ignore']: roomExtraInfo.extra,
      [roomExtraInfo.tag1 ? 'tag1' : '_ignore']: roomExtraInfo.tag1,
      [roomExtraInfo.tag2 ? 'tag2' : '_ignore']: roomExtraInfo.tag2,
      [roomExtraInfo.tag3 ? 'tag3' : '_ignore']: roomExtraInfo.tag3,
      [roomExtraInfo.tag4 ? 'tag4' : '_ignore']: roomExtraInfo.tag4,
      [roomExtraInfo.tag5 ? 'tag5' : '_ignore']: roomExtraInfo.tag5,
      [roomExtraInfo.tag6 ? 'tag6' : '_ignore']: roomExtraInfo.tag6,
      [roomExtraInfo.tag7 ? 'tag7' : '_ignore']: roomExtraInfo.tag7,
      [roomExtraInfo.tag8 ? 'tag8' : '_ignore']: roomExtraInfo.tag8,
      [roomExtraInfo.tag9 ? 'tag9' : '_ignore']: roomExtraInfo.tag9,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };

    request
    .get(`${APIRoot.get()}/api/rooms`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .query(query)
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_SEARCH,
          data: {
            error: -1
          }
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_SEARCH,
          data: res.body
        });
      }
    });
  },

  searchRecursive(roomType='11', myDomainOnly=true, fromTime='', toTime='', skipNum=0, limit=100, sortAsc=true, roomExtraInfo={}){
    const query = {
      roomType,
      myDomainOnly,
      [fromTime ? 'fromTime' : '_ignore']: fromTime,
      [toTime ? 'toTime' : '_ignore']: toTime,
      skipNum,
      limit,
      sortAsc,
      [roomExtraInfo.world ? 'world' : '_ignore']: roomExtraInfo.world,
      [roomExtraInfo.name ? 'name' : '_ignore']: roomExtraInfo.world,
      [roomExtraInfo.desc ? 'desc' : '_ignore']: roomExtraInfo.desc,
      [roomExtraInfo.icon ? 'icon' : '_ignore']: roomExtraInfo.icon,
      [roomExtraInfo.bgImg ? 'bgImg' : '_ignore']: roomExtraInfo.bgImg,
      [roomExtraInfo.extra ? 'extra' : '_ignore']: roomExtraInfo.extra,
      [roomExtraInfo.tag1 ? 'tag1' : '_ignore']: roomExtraInfo.tag1,
      [roomExtraInfo.tag2 ? 'tag2' : '_ignore']: roomExtraInfo.tag2,
      [roomExtraInfo.tag3 ? 'tag3' : '_ignore']: roomExtraInfo.tag3,
      [roomExtraInfo.tag4 ? 'tag4' : '_ignore']: roomExtraInfo.tag4,
      [roomExtraInfo.tag5 ? 'tag5' : '_ignore']: roomExtraInfo.tag5,
      [roomExtraInfo.tag6 ? 'tag6' : '_ignore']: roomExtraInfo.tag6,
      [roomExtraInfo.tag7 ? 'tag7' : '_ignore']: roomExtraInfo.tag7,
      [roomExtraInfo.tag8 ? 'tag8' : '_ignore']: roomExtraInfo.tag8,
      [roomExtraInfo.tag9 ? 'tag9' : '_ignore']: roomExtraInfo.tag9,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };

    const doSearch = (acc) => {
      const q = _.clone(query);
      q.skipNum = acc.length;
      return new Promise((resolve, reject) => {
        request
        .get(`${APIRoot.get()}/api/rooms`)
        .withCredentials()
        .type('form')
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .query(q)
        .end((err, res) => {
          if (err) {
            Utils.logoutIfneccessary(err);
            reject();
          } else {
            const values = acc.concat(res.body.rooms)
            resolve([values, res.body.rooms]);
          }
        });
      });
    };

    const recursiveHandler = (values) => {
      const acc = values[0];
      const result = values[1];
      if (result.length < 100){
        return Promise.resolve(acc);
      } else {
        return doSearch(acc).then(recursiveHandler);
      }
    }

    return doSearch([]).then(recursiveHandler)
                       .then((result) => {
                         AppDispatcher.dispatch({
                           type: BasicAPIEvents.ROOMS_SEARCH_RECURSIVE,
                           data: {
                             rooms: result
                           }
                         });
                       })
                       .catch(() => {
                         AppDispatcher.dispatch({
                           type: BasicAPIEvents.ROOMS_SEARCH_RECURSIVE,
                           data: {
                             rooms: []
                           }
                         });
                       })
  },

  addUser(params={}){
    request
    .post(`${APIRoot.get()}/api/rooms/${encodeURIComponent(params.roomId)}/${encodeURIComponent(params.userId)}`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send({
      _method: 'put',
      adderUserId: params.adderUserId
    })
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_ADD_MEMBER_NG,
          data: {
            error: -1
          }
        });
      } else {
        AppDispatcher.dispatch({
          type: BasicAPIEvents.ROOMS_ADD_MEMBER_OK,
          data: res.body
        });
      }
    });
  }
};

export default Action;
