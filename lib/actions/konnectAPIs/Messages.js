import assign from 'object-assign';
import request from 'superagent';
import jsonpipe from 'jsonpipe';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import APIRoot from '../APIRoot';
import Utils from '../../utils/Utils';
import _ from 'lodash-compat';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();
let exportReq;

const Action = {


  // val extUserIdOpt   = paramo("userId")
  // val roomIdOpt      = paramo("roomId")
  // val fromOpt        = paramo[Int]("from")
  // val toOpt          = paramo[Int]("to")
  // val keywordOpt     = paramo("keyword")
  // val mtypeOpt       = paramo("type")
  // val mextraOpt      = paramo("extra")
  // val limit          = paramo[Int]("limit").getOrElse(100).toInt.min(100)
  // val excludeSystems = List(Msg.SYSTEM_MSG_READ, Msg.SYSTEM_MSG_DELETE)
  // val responseFormat = paramo("format").getOrElse(APIMsg.FORMAT_OLD)

  search(userId, roomId, fromDateTime, toDateTime, keyword, msgType, extra, limit=100, paramExts={}) {
    let params = {
      [userId ? 'userId' : '_ignore'] : userId,
      [roomId ? 'roomId' : '_ignore'] : roomId,
      [fromDateTime ? 'from' : '_ignore']: fromDateTime,
      [toDateTime ? 'to' : '_ignore']: toDateTime,
      [msgType ? 'type' : '_ignore']: msgType,
      [extra ? 'extra' : '_ignore']: extra,
      limit,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
      format: 'raw',

      //https://mobilus-corp.atlassian.net/browse/MBAJ-162?focusedCommentId=10361&page=com.atlassian.jira.plugin.system.issuetabpanels%3Acomment-tabpanel#comment-10361
      keyword: keyword || ''
    };

    if(paramExts) {
      params = _.merge(params, paramExts);
    }

    request
      .get(APIRoot.get() + '/api/msgs')
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.MSGS_SEARCH,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.MSGS_SEARCH,
            data: res.body
          });
        }
      });
  },

  deleteMsgById(msgId, execUserId='') {
    const params = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    if (execUserId) {
      params.execUserId = execUserId;
    }
    return new Promise((resolve, reject) => {
      request
        .del(APIRoot.get() + '/api/msgs/' + encodeURIComponent(msgId))
        .withCredentials()
        .type('form')
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .query(params)
        .end((err, res) => {
          if (err) {
            Utils.logoutIfneccessary(err);
            AppDispatcher.dispatch({
              type: BasicAPIEvents.MSG_DELETE,
              data: {
                error: -1
              }
            });
            reject({
              error: -1
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.MSG_DELETE,
              data: res.body,
              msgId: msgId
            });
            resolve(res.body);
          }
        });
    });
  },

  put(params) {

    const authDomainId = APIRoot.getAuthDomainId();
    const admOverwrite = {};
    if (authDomainId !== null) {
      admOverwrite['auth_domainId'] = authDomainId;
      admOverwrite.execUserId = `dummy-sysNotify--${authDomainId}`;
    }
    const reqBody = assign(
      {_method: 'PUT'},
      params,
      admOverwrite,
    );
    request
      .post(`${APIRoot.get()}/api/msgs/${encodeURIComponent(params.id)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(reqBody)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.PUT_MESSAGE_NG,
            data: {
              error: -1
            },
            request: params,
          });
        } else {
          if (res.body.error === 0) {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.PUT_MESSAGE_OK,
              data: res.body,
              request: params,
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.PUT_MESSAGE_NG,
              data: res.body,
              request: params,
            });
          }
        }
      });
  },

  abortExportRequest(){
    if (exportReq){
      try {
        exportReq.abort();
      } catch(e) {
        Utils.log(`Failed to abort export request`);
      }
    }
  },

  // @TODO unused
  // See components.admin.maname.messages.Form.js , konnectPLusCCAPIs.MetricsExport.js
  chunkExport(params){
    Action.abortExportRequest();
    let queryString = '?outputType=json';
    if (params.from) {
      queryString = `${queryString}&from=${params.from}`;
    }
    if (params.to) {
      queryString = `${queryString}&to=${params.to}`;
    }
    if (APIRoot.getAuthDomainId() !== null){
      queryString = `${queryString}&auth_domainId=${APIRoot.getAuthDomainId()}`;
    }

    let i = 0;
    let messages = [];
    const stTime = Date.now();
    AppDispatcher.dispatch({
      type: BasicAPIEvents.EXPORT_MESSAGES_START,
      data: {
      }
    });
    exportReq = jsonpipe.flow(`${APIRoot.get()}/api/msgsExport${queryString}`, {
      method: 'GET',
      headers: {
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      },
      timeout: 15 * 60 * 1000,
      delimiter: '\n',
      success: (data) =>{
        i++;
        messages.push(data);
        if (i % 5000 === 0){
          Utils.log(`Exporting messages... ${i} [${Date.now() - stTime} ms]`);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.EXPORT_MESSAGES_PROGRESS,
            data: {
              messages,
              count: i
            }
          });
        }
      },
      error: (errorMsg) => {
        if (errorMsg){
          Utils.warn(`Failed to export messages ${errorMsg} [${Date.now() - stTime} ms]` );
          AppDispatcher.dispatch({
            type: BasicAPIEvents.EXPORT_MESSAGES_NG,
            data: {
              error: -1,
              messages: []
            }
          });
        }
      },
      complete: (statusText/*OK*/) => {
        Utils.log(`Finished Export messages... total: ${i}, statusText: ${statusText}  [${Date.now() - stTime} ms]`);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.EXPORT_MESSAGES_OK,
          data: {
            error: 0,
            messages,
            count: i
          }
        });
      }
    });
  },

  getJSONDownloadUrl(params={}){
    let queryString = '?_ignore=dummy';
    if (params.from) {
      queryString = `${queryString}&from=${params.from}`;
    }
    if (params.to) {
      queryString = `${queryString}&to=${params.to}`;
    }
    if (APIRoot.getAuthDomainId() !== null){
      queryString = `${queryString}&auth_domainId=${APIRoot.getAuthDomainId()}`;
    }
    let url = `${APIRoot.get()}/api/msgsExport${queryString}`;
    return Utils.urlWithAntiCsrfToken(url);
  }
};

export default Action;
