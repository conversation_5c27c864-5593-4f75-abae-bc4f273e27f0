'use strict';

import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';
import {LOG_LEVELS} from '../../stores/apiResults/Logs';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const ADMIN_PAGE_LOGEVENT_PREFIX = 'Admin page event:';


const Action = {
  get(opt = {}) {
    const query = {
      logDomainId: opt.logDomainId || '',
      excludeLogDomainIds: opt.excludeLogDomainIds || '',
      [opt.auth_domainId ? 'auth_domainId': '_ignore']: opt.auth_domainId,
      [opt.logKeys ? 'logKeys': '_ignore']: opt.logKeys.join(','),
      [opt.minLevel ? 'minLevel': '_ignore']: opt.minLevel,
      [opt.from ? 'from': '_ignore']: opt.from,
      [opt.to ? 'to': '_ignore']: opt.to,
      [opt.skip ? 'skip': '_ignore']: opt.skip,
      [opt.limit ? 'limit': '_ignore']: opt.limit
    };
    // for sysadm user
    if (query.logDomainId === 'adm' || query.logDomainId === 'sys' || !query.logDomainId) {
      query.auth_domainId = 'anonymous';
    }
    request
      .get(APIRoot.get() + '/api/logs')
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_LOGS,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_LOGS,
            data: res.body
          });
        }
      });
  },

  post(logLevel, text){
    let level = 0;

    Object.keys(LOG_LEVELS).forEach((key) => {
      if (logLevel === key){
        level = LOG_LEVELS[key].val;
      } else if (logLevel === LOG_LEVELS[key].val){
        level = LOG_LEVELS[key].val;
      } else if (logLevel === LOG_LEVELS[key].label){
        level = LOG_LEVELS[key].val;
      }
    })
    request
      .post(APIRoot.get() + '/api/logs')
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send({
        level,
        text: [ADMIN_PAGE_LOGEVENT_PREFIX, text, `(${window.document.location.hash})`].join('\n'),
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.POST_LOGS,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.POST_LOGS,
            data: res.body
          });
        }
      });
  }
};
export default Action;
