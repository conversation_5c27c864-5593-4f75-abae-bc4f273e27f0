import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();
const FILE_NAME = 'systemLinks.json';

function _doPost(domainId, file, fileName, contentType, exposeToAll, noNeedAuth, isAnonymous){
  let domainIdInUrl = encodeURIComponent(domainId);
  if (APIRoot.getAuthDomainId() === 'anonymous') {
    domainIdInUrl = 'pub';
  } else if (APIRoot.getAuthDomainId() !== null) {
    domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
  }
  if (domainIdInUrl === ''){
    domainIdInUrl = 'pub';
  }

  return new Promise((resolve, reject) => {
    const form = new FormData();
    form.append('file', file, fileName);
    form.append('contentType', contentType);
    form.append('exposeToAll', exposeToAll);
    form.append('noNeedAuth', noNeedAuth);
    if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId', APIRoot.getAuthDomainId());
    const req = new XMLHttpRequest();
    if (!isAnonymous){
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${encodeURIComponent(fileName)}`);
    } else {
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${encodeURIComponent(domainId)}/${encodeURIComponent(fileName)}`);
    }

    req.onload = function() {
      if (req.status === 200) {
        const result = req.responseText;
        const data = Utils.tryJSONParse(result);
        if (data.error === 0){
          resolve(data);
        } else {
          reject(data);
        }
      } else {
        reject({error: -1});
      }
    };
    req.withCredentials = true;
    req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
    req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
    req.send(form);
  });
}

const Action = {

  post(file) {
    _doPost('anonymous', file, FILE_NAME, 'application/json; charset=utf-8', true, true, false)
    .then((res) => {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.POST_SYSTEM_LINKS_OK,
        data: res
      });
    }, (data) => {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.POST_SYSTEM_LINKS_NG,
        data,
      });
    });
  },

  get(){
    const query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/api/publicFiles/pub/system.json`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_SYSTEM_LINKS,
            data: null,
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_SYSTEM_LINKS,
            error: 0,
            data: Utils.tryJSONParse(res.text)
          });
        }
      });
  },

  getFromGlobal(){
    request
      .get(`${APIRoot.get()}/api/globalPublicFiles/mst/pub/${FILE_NAME}`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_SYSTEM_LINKS,
            data: null,
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_SYSTEM_LINKS,
            error: 0,
            data: Utils.tryJSONParse(res.text)
          });
        }
      });
  }
};
export default Action;
