'use strict';

import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';
const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const ADMIN_PAGE_LOGEVENT_PREFIX = 'Admin page event:';

const Action = {
  get(opt = {}) {
    const query = {
      logDomainId: opt.logDomainId || '',
      excludeLogDomainIds: opt.excludeLogDomainIds || '',
      [opt.auth_domainId ? 'auth_domainId': '_ignore']: opt.auth_domainId,
      [opt.logKeys ? 'logKeys': '_ignore']: opt.logKeys.join(','),
      [opt.key1 ? 'key1': '_ignore']: opt.key1,
      [opt.from ? 'fromTime': '_ignore']: opt.from,
      [opt.to ? 'toTime': '_ignore']: opt.to,
    };
    // for sysadm user
    if (query.logDomainId === 'adm' || query.logDomainId === 'sys' || !query.logDomainId) {
      query.auth_domainId = 'anonymous';
    }
    request
    
      .get(APIRoot.get() + '/api/actionlogs')
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_ACTIONLOGS,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_ACTIONLOGS,
            data: {result: Utils.tryJSONParse(`[${res.text.trim().replace(/}\r\n{|}\n{/g, '},{')}]`)} 
          });
        }
      });
  },

  
};
export default Action;
