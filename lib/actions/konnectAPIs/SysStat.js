'use strict';

import moment from 'moment';
import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();


let _defaultFrom = () => {
  return moment().subtract(90, 'days').hour(0).minute(0).second(0).unix();
};

let _defaultTo = () => {
  return moment().subtract(1, 'days').hour(23).minute(59).second(59).unix();
};

let _get = (_from, to, span='1d') => {
  if (!_from) {
    _from = _defaultFrom();
  }
  if (!to) {
    to = _defaultTo();
  }

  let query = {
    auth_domainId: 'anonymous',
    'from': _from,
    to,
    span
  };

  request
  .get(APIRoot.get() + '/api/sysstat')
  .withCredentials()
  .type('form')
  .set({
    'X-CSRF-Token': antiCsrfToken,
    'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
  })
  .query(query)
  .end((err, res) => {
    if (err) {
      Utils.logoutIfneccessary(err);
      AppDispatcher.dispatch({
        type: BasicAPIEvents.GET_SYSSTAT,
        data: {
          error: -1
        }
      });
    } else {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.GET_SYSSTAT,
        data: res.body
      });
    }
  });
};

const Action = {
  getDailyStat(_from, to) {
    _get(_from, to, '1d');
  },

  getHourlyStat(_from, to) {
    _get(_from, to, '1h');
  }
};
export default Action;
