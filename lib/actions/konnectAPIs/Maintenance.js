'use strict';

import request from 'superagent';

import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const antiCsrfToken = Utils.getAntiCSRFToken();


const Action = {
  clearDbCache: (cb) => {
    const query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
    };

    request
      .post(`${APIRoot.get()}/api/clearDbCache`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(query)
      .end((err, res) => {
        if (cb) cb(err, res);
      });
  },
  updateUserControlPermissionMaster: (cb) => {
    const query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
    };

    request
      .post(`${APIRoot.get()}/plusapi/userBasePermissions`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(query)
      .end((err, res) => {
        if (cb) cb(err, res);
      });
  }
}

export default Action;
