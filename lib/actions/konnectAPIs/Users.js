import assign from 'object-assign';
import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  getByUId(uid, opt) {
    const query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
      withRooms: false,
      withLastAccessInfo: false,
    };
    request
      .get(APIRoot.get() + '/api/users/' + encodeURIComponent(uid))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(assign({}, query, opt))
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.ADMIN_USERS_GET,
            data: {
              error: err.status
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.ADMIN_USERS_GET,
            data: res.body
          });
        }
      });
  },

  suspendByAdmin(userId, suspendFlg, authDomainId) {
    const data = {
      'csrf-token': antiCsrfToken,
      'suspendedByAdmin' : suspendFlg,
      [authDomainId ? 'auth_domainId' : '_ignore']: authDomainId
    };

    request
      .post(`${APIRoot.get()}/api/users/${encodeURIComponent(userId)}`)
      .withCredentials()
      .type('form')
      .send(data)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.ADMIN_USERS_SUSPEND,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.ADMIN_USERS_SUSPEND,
            suspendFlg,
            data: res.body
          });
        }
      });
  },

  getRoomsByUId(userId) {
    const query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
      withRooms: true,
      withLastAccessInfo: false,
    };
    request
      .get(`${APIRoot.get()}/api/users/${encodeURIComponent(userId)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.ROOMS_SEARCH_BY_UID,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.ROOMS_SEARCH_BY_UID,
            data: res.body
          });
        }
      });
  },

  search(permitLevel=null, prevId=null, limit=100) {

    let params = {
      [permitLevel !== null ? 'permitLevel' : '_ignore']: permitLevel,
      [prevId !== null ? 'prevId' : '_ignore']: prevId,
      limit,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };

    request
      .get(`${APIRoot.get()}/api/users`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.USERS_SEARCH,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.USERS_SEARCH,
            data: res.body
          });
        }
      });
  },
};
export default Action;
