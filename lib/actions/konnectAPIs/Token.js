'use strict';

import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const Token = {

  get(params={}) {
    request
      .post(APIRoot.get() + '/api/token_checkDomainId')
      .withCredentials()
      .type('form')
      // do not auth by cookie
      // .set({
      //   'X-CSRF-Token':
      // })
      .send(params)
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.TOKEN_GET,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.TOKEN_GET,
            data: {
              error: 0,
              result: res.text,
            }
          });
        }
      });
  },
};

export default Token;
