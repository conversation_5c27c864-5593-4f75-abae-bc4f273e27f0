import moment from 'moment';
import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

let _defaultFrom = () => {
  return moment().subtract(90, 'days').hour(0).minute(0).second(0).unix();
};

let _defaultTo = () => {
  return moment().subtract(1, 'days').hour(23).minute(59).second(59).unix();
};

let _get = (domainId, userId, _from, to, span='1d', all=false) => {
  if (!_from) {
    _from = _defaultFrom();
  }
  if (!to) {
    to = _defaultTo();
  }

  let query = {
    [domainId ? 'domainId' : '_ignore']: domainId,
    [userId ? 'userId' : '_ignore']: userId,
    'from': _from,
    to,
    span,
    [APIRoot.getAuthDomainId() ? 'auth_domainId' : '_ignore']: domainId
  };
  if (!query.auth_domainId && Utils.isSysAdmAccess()) {
    if (query.domainId) {
      query.auth_domainId = query.domainId;
    } else {
      query.auth_domainId = 'anonymous';
    }
  }
  const url = all ? '/api/userstatAll' : '/api/userstat';
  request
  .get(APIRoot.get() + url)
  .withCredentials()
  .type('form')
  .set({
    'X-CSRF-Token': antiCsrfToken,
    'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
  })
  .query(query)
  .end((err, res) => {
    if (err) {
      Utils.logoutIfneccessary(err);
      AppDispatcher.dispatch({
        type: BasicAPIEvents.USER_STAT,
        data: {
          error: -1
        }
      });
    } else {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.USER_STAT,
        data: all ? {result: Utils.tryJSONParse(`[${res.text.trim().replace(/}\r\n{|}\n{/g, '},{')}]`)} : res.body,
        domainId: query.domainId,
        userId,
        condition: {
          span,
          from: _from,
          to,
        }
      });
    }
  });
};

let _getSum = (domainId, userId, _from, to, span='1h', sum=24) =>{
  if (!_from) {
    _from = _defaultFrom();
  }
  if (!to) {
    to = _defaultTo();
  }

  let query = {
    [domainId ? 'domainId' : '_ignore']: domainId,
    [userId ? 'userId' : '_ignore']: userId,
    'from': _from,
    to,
    span,
    sum,
    [APIRoot.getAuthDomainId() ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
  };
  if (!query.auth_domainId && Utils.isSysAdmAccess()) {
    if (query.domainId) {
      query.auth_domainId = query.domainId;
    } else {
      query.auth_domainId = 'anonymous';
    }
  }
  request
  .get(APIRoot.get() + '/api/userstatSum')
  .withCredentials()
  .type('form')
  .set({
    'X-CSRF-Token': antiCsrfToken,
    'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
  })
  .query(query)
  .end((err, res) => {
    if (err) {
      Utils.logoutIfneccessary(err);
      AppDispatcher.dispatch({
        type: BasicAPIEvents.USER_STAT_SUM,
        data: {
          error: -1
        }
      });
    } else {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.USER_STAT_SUM,
        data: res.body,
        userId,
        condition: {
          span,
          from: _from,
          to,
        },
      });
    }
  });
};

let uncompleteRequest;
const _abortUncompleteRequest = () => {
  if (uncompleteRequest){
    try {
      uncompleteRequest.abort();
    } catch(e) {
      Utils.log('Failed to abort uncomplete request');
    }
  }
};

const Action = {
  getStat: _get,
  getSum: _getSum,

  getDailyStat(domainId, userId, _from, to) {
    _get(domainId, userId, _from, to, '1d');
  },
  getDailyStatByHours(domainId, userId, _from, to, all=false) {
    _get(domainId, userId, _from, to, '1h', all);
  },
  getDailyStatAll(domainId, _from, to) {
    _get(domainId, '', _from, to, '1d', true);
  },

  getHourlyStat(domainId, userId, _from, to) {
    _getSum(domainId, userId, _from, to, '1h', 24);
  },

  getMonthlyStat(domainId, userId, _from, to) {
    _getSum(domainId, userId, _from, to, '1d', 30);
  },

  abortUncompleteRequest: _abortUncompleteRequest,
  async getUserStatAll(domainId, _from, to, span='1h', byDomainId=false) {
    _abortUncompleteRequest();
    if (!_from) {
      _from = _defaultFrom();
    }
    if (!to) {
      to = _defaultTo();
    }

    const lastHour = moment().startOf('hour');
    const start = moment.unix(_from || _defaultFrom())
    const end = moment.unix(to || _defaultTo())
    const now = moment().unix()
    const isCurrentDay = end.isSame(moment(), 'day');
    const startHour = span === '1d' && isCurrentDay ? moment({hour: 0}) : start;
    const endDaily = span === '1d' && isCurrentDay ? moment.unix(to || _defaultTo()).subtract(1, 'd') : end;

    let dailyItems = [];
    let oneHourItems = [];
    let fiveMinuteItems = [];

    AppDispatcher.dispatch({
      type: BasicAPIEvents.EXPORT_ALLUSER_STAT_START,
      data: {
        error: 0,
        count: 0,
        span,
        stats: [],
        to,
        _from
      }
    });

    let authDomainId = '';
    if (APIRoot.getAuthDomainId()){
      authDomainId = APIRoot.getAuthDomainId();
    } else if (Utils.isSysAdmAccess()) {
      if (domainId) {
        authDomainId = domainId;
      } else {
        authDomainId = 'anonymous';
      }
    }

    if (span === '1d') {
      try {
        const dailyQuery = {
          [byDomainId && !domainId ? '_ignore' : 'domainId']: domainId,
          from: _from,
          to: endDaily.unix(),
          span,
          [authDomainId ? 'auth_domainId' : '_ignore']: authDomainId
        };
        const dailyData = await request
        .get(
          `${APIRoot.get()}/api/userstatAll`,
        )
        .withCredentials()
        .type('form')
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .query(dailyQuery)

        dailyItems = dailyData.text.split('\n').map((item) => {
          try {
            return JSON.parse(item)
          } catch (err) {
            return undefined // eslint-disable-line no-undefined
          }
        })
        dailyItems = dailyItems.filter(Boolean)
      } catch (error) {
        Utils.warn(`Failed to export all user stats ${error} [${Date.now() - now} ms]`);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.EXPORT_ALLUSER_STAT_NG,
          data: {
            error: -1,
            count: 0,
            span,
            stats: [],
          }
        });
      }
    }

    if (span === '1h' || isCurrentDay) {
      try {
        const oneHourQuery = {
          [byDomainId && !domainId ? '_ignore' : 'domainId']: domainId,
          from: startHour.unix(),
          to: isCurrentDay ? lastHour.unix() : to,
          span: '1h',
          [authDomainId ? 'auth_domainId' : '_ignore']: authDomainId
        };
        const oneHourData = await request
          .get(
          `${APIRoot.get()}/api/userstatAll`,
          )
          .withCredentials()
          .type('form')
          .set({
            'X-CSRF-Token': antiCsrfToken,
            'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
          })
          .query(oneHourQuery)

        oneHourItems = oneHourData.text.split('\n').map((item) => {
          try {
            return JSON.parse(item)
          } catch (err) {
            return undefined // eslint-disable-line no-undefined
          }
        })
        oneHourItems = oneHourItems.filter(Boolean)
      } catch (error) {
        Utils.warn(`Failed to export all user stats ${error} [${Date.now() - now} ms]`);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.EXPORT_ALLUSER_STAT_NG,
          data: {
            error: -1,
            count: 0,
            span,
            stats: [],
          }
        });
      }
    }

    if (lastHour.isBetween(start, end)) {
      try {
        const fiveMinuteQuery = {
          [byDomainId && !domainId ? '_ignore' : 'domainId']: domainId,
          from: lastHour.unix(),
          to: now,
          span: '5m',
          [authDomainId ? 'auth_domainId' : '_ignore']: authDomainId
        }

        const fiveMinuteData = await request
          .get(
          `${APIRoot.get()}/api/userstatAll`,
        )
          .withCredentials()
          .type('form')
          .set({
            'X-CSRF-Token': antiCsrfToken,
            'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
          })
          .query(fiveMinuteQuery)

        fiveMinuteItems = fiveMinuteData.text.split('\n').map((item) => {
          try {
            return JSON.parse(item)
          } catch (err) {
            return undefined // eslint-disable-line no-undefined
          }
        })

        fiveMinuteItems = fiveMinuteItems.filter(Boolean)
      } catch (error) {
        Utils.warn(`Failed to export all user stats ${error} [${Date.now() - now} ms]`);
        AppDispatcher.dispatch({
          type: BasicAPIEvents.EXPORT_ALLUSER_STAT_NG,
          data: {
            error: -1,
            count: 0,
            span,
            stats: [],
          }
        });
      }
    }

    const allItems = dailyItems.concat(oneHourItems.concat(fiveMinuteItems));

    AppDispatcher.dispatch({
      type: BasicAPIEvents.EXPORT_ALLUSER_STAT_OK,
      data: {
        error: 0,
        to,
        _from,
        span,
        stats: allItems,
        count: allItems.length,
      }
    });
  },
};
export default Action;
