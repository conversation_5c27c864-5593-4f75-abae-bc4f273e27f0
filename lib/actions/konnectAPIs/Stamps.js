'use strict';

import _ from 'lodash-compat';
import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const FILE_NAME = 'index.json';
const STAMP_DIR = 'stamps';
const TMP_DIR = '_tmp_';
const LINE_STAMP_DIR = 'line/stamps';

const Action = {
  getIndex(params={}) {

    let query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };

    const targetDir = params.isLINE ? LINE_STAMP_DIR : STAMP_DIR;
    const dir = params.isTmp ? `${TMP_DIR}/${targetDir}` : targetDir;

    let domainIdInUrl = encodeURIComponent(params.domainId);
    if (APIRoot.getAuthDomainId() === '') {
      domainIdInUrl = 'pub';
    } else if (APIRoot.getAuthDomainId() !== null) {
      domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
    }
    if (domainIdInUrl === ''){
      domainIdInUrl = 'pub';
    }

    request
      .get(`${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${dir}/${FILE_NAME}`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_STAMPS_JSON,
            data: {
              error: res.status
            },
            isTmp: params.isTmp
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_STAMPS_JSON,
            error: 0,
            data: Utils.tryJSONParse(res.text),
            isTmp: params.isTmp
          });
        }
      });
  },

  postIndex(params={}) {

    let domainIdInUrl = encodeURIComponent(params.domainId);
    if (APIRoot.getAuthDomainId() === '') {
      domainIdInUrl = 'pub';
    } else if (APIRoot.getAuthDomainId() !== null) {
      domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
    }
    if (domainIdInUrl === ''){
      domainIdInUrl = 'pub';
    }

    const targetDir = params.isLINE ? LINE_STAMP_DIR : STAMP_DIR;

    return new Promise(function(resolve, reject){
      let form = new FormData();
      const reqJSON = Utils.attachLastUpdateToJSONString(params.json);
      let blob = new Blob([reqJSON], {type: 'application/json'});
      form.append('file', blob, FILE_NAME);
      form.append('contentType', 'application/json; charset=utf-8');
      form.append('exposeToAll', true);
      form.append('noNeedAuth', true);
      if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId',  APIRoot.getAuthDomainId());
      let req = new XMLHttpRequest();
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${targetDir}/${FILE_NAME}`);
      req.onload = function() {
        if (req.status === 200) {
          let result = req.responseText;
          let data = Utils.tryJSONParse(result);
          if (data.error === 0){
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject({error: -1});
        }
      };
      req.withCredentials = true;
      req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
      req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
      req.send(form);
    })
    .then(function(data){
      AppDispatcher.dispatch({
        type: BasicAPIEvents.POST_STAMPS_JSON_OK,
        data: data,
        json: params.json,
        isTmp: params.isTmp
      });
    }, function(data){
      AppDispatcher.dispatch({
        type: BasicAPIEvents.POST_STAMPS_JSON_NG,
        data: data,
        isTmp: params.isTmp
      });
    });
  },

  upload(params={}) {

    let domainIdInUrl = encodeURIComponent(params.domainId);
    if (APIRoot.getAuthDomainId() === '') {
      domainIdInUrl = 'pub';
    } else if (APIRoot.getAuthDomainId() !== null) {
      domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
    }
    if (domainIdInUrl === ''){
      domainIdInUrl = 'pub';
    }

    const targetDir = params.isLINE ? LINE_STAMP_DIR : STAMP_DIR;

    return new Promise(function(resolve, reject){
      let form = new FormData();
      form.append('file', params.file, params.filename);
      form.append('contentType', 'image/png');
      form.append('exposeToAll', true);
      form.append('noNeedAuth', true);
      if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId',  APIRoot.getAuthDomainId());
      let req = new XMLHttpRequest();
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${targetDir}/${params.pkgId}/${params.filename}`);
      req.onload = function() {
        if (req.status === 200) {
          let result = req.responseText;
          let data = Utils.tryJSONParse(result);
          if (data.error === 0){
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject({error: -1});
        }
      };
      req.withCredentials = true;
      req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
      req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
      req.send(form);
    })
    .then(function(data){
      AppDispatcher.dispatch({
        type: BasicAPIEvents.UPLOAD_STAMP_OK,
        data: data,
        isTmp: params.isTmp
      });
    }, function(data){
      AppDispatcher.dispatch({
        type: BasicAPIEvents.UPLOAD_STAMP_NG,
        data: data,
        isTmp: params.isTmp
      });
    });
  },

  import(params={}) {
    const contentTypes = 'png\timage/png\t\tjson\tapplication/json; charset=utf-8';
    let rootDir = params.isTmp ? TMP_DIR : '';
    if (params.isLINE) {
      rootDir = `${rootDir}/line`
    }

    let domainIdInUrl = encodeURIComponent(params.domainId);
    if (APIRoot.getAuthDomainId() === '') {
      domainIdInUrl = 'pub';
    } else if (APIRoot.getAuthDomainId() !== null) {
      domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
    }
    if (domainIdInUrl === ''){
      domainIdInUrl = 'pub';
    }
    return new Promise(function(resolve, reject){
      let form = new FormData();
      form.append('zip', params.zip);
      form.append('rootDir', rootDir);
      form.append('contentTypes', contentTypes);
      form.append('exposeToAll', true);
      form.append('noNeedAuth', true);
      if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId',  APIRoot.getAuthDomainId());
      let req = new XMLHttpRequest();
      req.open('POST', `${APIRoot.get()}/api/publicFilesImport/${domainIdInUrl}`);
      req.onload = function() {
        if (req.status === 200) {
          let result = req.responseText;
          let data = Utils.tryJSONParse(result);
          if (data.error === 0){
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject({error: -1});
        }
      };
      req.withCredentials = true;
      req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
      req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
      req.send(form);
    })
    .then(function(data){
      AppDispatcher.dispatch({
        type: BasicAPIEvents.IMPORT_STAMPS_OK,
        data: data,
        isTmp: params.isTmp
      });
    }, function(data){
      AppDispatcher.dispatch({
        type: BasicAPIEvents.IMPORT_STAMPS_NG,
        data: data,
        isTmp: params.isTmp
      });
    });
  },

  delete(params={}){
    const targetDir = params.isLINE ? LINE_STAMP_DIR : STAMP_DIR;
    const dir = params.isTmp ? `${TMP_DIR}/${targetDir}` : targetDir;
    let query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };

    let domainIdInUrl = encodeURIComponent(params.domainId);
    if (APIRoot.getAuthDomainId() === '') {
      domainIdInUrl = 'pub';
    } else if (APIRoot.getAuthDomainId() !== null) {
      domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
    }

    request
      .del(`${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${dir}/${params.pkgId}/${params.filename}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.DELETE_STAMP_NG,
            data: {
              error: -1
            },
            isTmp: params.isTmp
          });
        } else {
          if (res.body.error === 0){
            AppDispatcher.dispatch({
              type: BasicAPIEvents.DELETE_STAMP_OK,
              data: res.body,
              isTmp: params.isTmp
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.DELETE_STAMP_NG,
              data: res.body,
              isTmp: params.isTmp
            });
          }
        }
      });
  },

  deleteTempStamps(params={}){

    let domainIdInUrl = encodeURIComponent(params.domainId);
    if (APIRoot.getAuthDomainId() === '') {
      domainIdInUrl = 'pub';
    } else if (APIRoot.getAuthDomainId() !== null) {
      domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
    }
    const targetDir = params.isLINE ? LINE_STAMP_DIR : STAMP_DIR;

    return new Promise((resolve, reject) => {
      let query = {
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
      };
      request
        .get(`${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${TMP_DIR}/${targetDir}/${FILE_NAME}`)
        .withCredentials()
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .query(query)
        .end((err, res) => {
          if (err) {
            reject();
          } else {
            resolve(Utils.tryJSONParse(res.text));
          }
      });
    })
    .then((values) => {
      let deleteList = [
        `${APIRoot.get()}/api/publicFiles/${encodeURIComponent(params.domainId)}/${TMP_DIR}/${targetDir}/${FILE_NAME}`
      ];
      _.each(values.list, (group) => {
        let id = group.id;
        _.each(group.stamps, (stamp) => {
          let url = `${APIRoot.get()}/api/publicFiles/${encodeURIComponent(params.domainId)}/${TMP_DIR}/${targetDir}/${id}/${stamp.code}.png`;
          deleteList.push(url);
        });
      });

      return new Promise((resolve, reject) => {
        let next = (i) => {
          if (i < deleteList.length){
            let query = {
              [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
            };
            request
              .del(deleteList[i])
              .withCredentials()
              .type('form')
              .set({
                'X-CSRF-Token': antiCsrfToken,
                'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
              })
              .query(query)
              .end((err, res) => {
                return next(i + 1);
              });
          } else {
            resolve();
          }
        };
        next(0);
      });
    }).catch((err) => { if(err) console.log('ERROR: ' + err) });
  },

  deleteTempStampsThenImport(params={}){
    Action.deleteTempStamps(params)
          .then(() => {
            Action.import(params);
          })
  }
};
export default Action;
