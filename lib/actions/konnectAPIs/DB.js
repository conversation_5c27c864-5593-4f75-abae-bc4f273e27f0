'use strict';

import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  execFind(collection, queryJsonObj, sortJsonObj, limit) {
    let query = {
      method: 'find',
      coll: collection,
      query: JSON.stringify(queryJsonObj),
      [sortJsonObj ? 'sort' : '_ignore']: JSON.stringify(sortJsonObj),
      [limit ? 'limit' : '_ignore']: limit,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
   };
    request
      .get(APIRoot.get() + '/api/db/execQuery')
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.DB_FIND,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.DB_FIND,
            data: res.body
          });
        }
      });
  },

  execCount(collection, queryJsonObj) {
    let query = {
      method: 'count',
      coll: collection,
      query: JSON.stringify(queryJsonObj),
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
   };
    request
      .get(APIRoot.get() + '/api/db/execQuery')
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.DB_FIND,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.DB_FIND,
            data: res.body
          });
        }
      });
  }
};
export default Action;
