'use strict';

import _ from 'lodash-compat';
import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {

  search(params={}) {
     let query = {
       [params.userId ? 'userId' : '_ignore'] : params.userId,
       [params.roomId ? 'roomId' : '_ignore'] : params.roomId,
       [params.prevId ? 'prevId' : '_ignore']: params.prevId,
       [params.from ? 'from' : '_ignore']: params.from,
       [params.to ? 'to' : '_ignore']: params.to,
       [params.fname ? 'fname': '_ignore']: params.fname,
       [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
     };
    request
      .get(`${APIRoot.get()}/api/files`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.SEARCH_FILES,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.SEARCH_FILES,
            data: res.body,
            isReadMore: query.prevId
          });
        }
      });
  },

  get(fileId) {
    let query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(APIRoot.get() + '/api/files/' + encodeURIComponent(fileId))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_FILE,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_FILE,
            data: res.body
          });
        }
      });
  },

  delete(fileId) {
    let query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .del(APIRoot.get() + '/api/files/' + encodeURIComponent(fileId))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.DELETE_FILE_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error === 0){
            AppDispatcher.dispatch({
              type: BasicAPIEvents.DELETE_FILE_OK,
              data: res.body,
              deletedId: fileId
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.DELETE_FILE_NG,
              data: res.body
            });
          }
        }
      });
  },

  changeOwner(fromUid, toUid){
    let query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .put(`${APIRoot.get()}/api/filesOwner/${encodeURIComponent(fromUid)}/${encodeURIComponent(toUid)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.CHANGE_FILE_OWNER_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error === 0){
            AppDispatcher.dispatch({
              type: BasicAPIEvents.CHANGE_FILE_OWNER_OK,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.CHANGE_FILE_OWNER_NG,
              data: res.body
            });
          }
        }
      });
  }
};
export default Action;
