'use strict';

import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  // backup api respond 'application/zip'
  //
  // get(outputType='json', charsetName='UTF8') {
  //   let query = {
  //     outputType: outputType,
  //     charsetName: charsetName
  //  };
  //   request
  //     .get(APIRoot.get() + '/api/backup')
  //     .withCredentials()
  //     .type('form')
  //     .set({
  //       'X-CSRF-Token': antiCsrfToken, 'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
  //     })
  //     .query(query)
  //     .end((err, res) => {
  //       if (err) {
  //         Utils.logoutIfneccessary(err);
  //         AppDispatcher.dispatch({
  //           type: BasicAPIEvents.GET_BACKUP_NG,
  //           data: {
  //             error: -1
  //           }
  //         });
  //       } else {
  //         AppDispatcher.dispatch({
  //           type: BasicAPIEvents.GET_BACKUP_OK
  //         });
  //       }
  //     });
  // },
  getUrl(){
    let url = APIRoot.get() + '/api/backup';
    return Utils.urlWithAntiCsrfToken(url);
  }
};
export default Action;
