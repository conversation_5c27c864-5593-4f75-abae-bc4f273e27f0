import _ from 'lodash-compat';
import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const BasicAPIEvents = AppConstants.BasicAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

export const getDomainIdInUrl = (domainId = '') => {
  let domainIdInUrl = domainId ? encodeURIComponent(domainId) : '';
  if (APIRoot.getAuthDomainId() === 'anonymous') {
    domainIdInUrl = 'pub';
  } else if (APIRoot.getAuthDomainId() !== null) {
    domainIdInUrl = encodeURIComponent(APIRoot.getAuthDomainId());
  }
  if (domainIdInUrl === ''){
    domainIdInUrl = 'pub';
  }
  return domainIdInUrl;
}

function _doPost(domainId, file, fileName, contentType, exposeToAll, noNeedAuth, isAnonymous){
  return new Promise((resolve, reject) => {
    const form = new FormData();
    form.append('file', file, fileName);
    form.append('contentType', contentType);
    form.append('exposeToAll', exposeToAll);
    form.append('noNeedAuth', noNeedAuth);
    if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId',  APIRoot.getAuthDomainId());
    const req = new XMLHttpRequest();
    if (!isAnonymous){
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${getDomainIdInUrl(domainId)}/${encodeURIComponent(fileName)}`);
    } else {
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${encodeURIComponent(domainId)}/${encodeURIComponent(fileName)}`);
    }

    req.onload = function() {
      if (req.status === 200) {
        let result = req.responseText;
        let data = Utils.tryJSONParse(result);
        if (data.error === 0){
          resolve(data);
        } else {
          reject(data);
        }
      } else {
        reject({error: -1});
      }
    };
    req.withCredentials = true;
    req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
    req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
    req.send(form);
  });
}

const Action = {

  search(domainId, prefix='') {

    const params = {
      prefix,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/api/publicFiles/${getDomainIdInUrl(domainId)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (!err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.SEARCH_PUBLIC_FILES,
            data: res.body
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.SEARCH_PUBLIC_FILES,
            data: {
              error: -1
            }
          });
        }
      });
  },

  post(domainId, file, fileName, contentType='application/octet-stream', exposeToAll=true, noNeedAuth=false, isAnonymous=false, callback = null) {
    _doPost(domainId, file, fileName, contentType, exposeToAll, noNeedAuth, isAnonymous)
    .then((res) => {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.POST_PUBLIC_FILE_OK,
        data: res,
        fileName,
      });
      if (_.isFunction(callback)) {
        callback(true);
      }
    }, (data) => {
      AppDispatcher.dispatch({
        type: BasicAPIEvents.POST_PUBLIC_FILE_NG,
        data,
        fileName,
      });
      if (_.isFunction(callback)) {
        callback(false);
      }
    });
  },

  get(domainId, fileName) {
    const query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/api/publicFiles/${getDomainIdInUrl(domainId)}/${encodeURIComponent(fileName)}`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_PUBLIC_FILE,
            error: -1,
            data: {
              error: -1,
            },
            fileName
          });
        } else {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.GET_PUBLIC_FILE,
            error: 0,
            data: res.text,
            fileName
          });
        }
      });
  },


  del(domainId, fileName) {
    const params = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .del(`${APIRoot.get()}/api/publicFiles/${getDomainIdInUrl(domainId)}/${encodeURIComponent(fileName)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: BasicAPIEvents.DELETE_PUBLIC_FILE_NG,
            data: {
              error: -1
            },
            fileName
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.DELETE_PUBLIC_FILE_NG,
              data: res.body,
              fileName
            });
          } else {
            AppDispatcher.dispatch({
              type: BasicAPIEvents.DELETE_PUBLIC_FILE_OK,
              data: res.body,
              fileName
            });
          }
        }
      });
  }
};
export default Action;
