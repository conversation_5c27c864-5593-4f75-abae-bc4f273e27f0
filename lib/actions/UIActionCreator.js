import AppConstants from '../constants/AppConstants';
import AppDispatcher from '../dispatcher/AppDispatcher';
import Utils from '../utils/Utils';

const UserEvents = AppConstants.UserEvents;

/*
 This ActionCreator make actions from user action(click, scroll, etc)
 All result will be dispatched by `AppDispatcher`
*/

const Action = {
  adminMenuChange(menuItem) {
    Utils.nextTick(() => AppDispatcher.dispatch({
      type: UserEvents.ADMIN_MENU_CHANGE,
      data: menuItem
    }));
  },
  readNotification() {
    Utils.nextTick(() => AppDispatcher.dispatch({
      type: UserEvents.READ_NOTIFICATION,
      data: {
        lastReadTime: Date.now(),
      },
    }));
  },
  pathChange(path) {
    Utils.nextTick(() => AppDispatcher.dispatch({
      type: UserEvents.HASH_CHANGE,
      data: path
    }))
  },
  deleteWidget(id) {
    Utils.nextTick(() => AppDispatcher.dispatch({
      type: 'DELETE_WIDGET',
      data: { id }
    }));
  },
  moveWidgetUp(id) {
    Utils.nextTick(() => AppDispatcher.dispatch({
      type: 'MOVE_WIDGET_UP',
      data: { id }
    }));
  }
};

export default Action;
