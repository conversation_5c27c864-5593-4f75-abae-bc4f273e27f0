import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();


const Action = {

  resetMyPassword(params) {
    request
      .put(APIRoot.get() + '/plusapi/users/' + encodeURIComponent(params.uid) + '/password')
      .withCredentials()
      .type('form')
      .send({
        oldPassword: params.oldPassword,
        password: params.password,
        'csrf-token': antiCsrfToken,
        [params.auth_domainId ? 'auth_domainId': '_ignore_']: params.auth_domainId,
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.RESET_PASSWORD,
            data: {
              uid: params.uid,
              error: -1
            }
          });
        } else {
          let data = res.body;
          data.uid = params.uid;
          AppDispatcher.dispatch({
            type: PlusAPIEvents.RESET_PASSWORD,
            data: data
          });
        }
      });

  },

  resetPassword1(plusUserId) {
    request
      .post(`${APIRoot.get()}/plusapi/users/password/resetByUser`)
      .withCredentials()
      .type('form')
      .send({
        plusUserId,
        'csrf-token': antiCsrfToken
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.RESET_PASSWORD1,
            data: {
              error: -1,
              plusUserId,
            }
          });
        } else {
          let data = res.body;
          data.plusUserId = plusUserId;
          AppDispatcher.dispatch({
            type: PlusAPIEvents.RESET_PASSWORD1,
            data,
          });
        }
      });
  },

  resetPassword2(plusUserId, passwdTmp, passwdNew) {
    request
    .post(`${APIRoot.get()}/plusapi/users/password/resetByUser2`)
      .withCredentials()
      .type('form')
      .send({
        plusUserId,
        passwdTmp,
        passwdNew,
        'csrf-token': antiCsrfToken
      })
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.RESET_PASSWORD2,
            data: {
              error: -1,
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.RESET_PASSWORD2,
            data: res.body
          });
        }
      });
  },
};

export default Action;
