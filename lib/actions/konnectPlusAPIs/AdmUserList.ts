import {AdmUserListApi, Mode} from "../../openapi/plus"
import AdmUserList from "../../stores/apiResults/AdmUserList"
import AppConstants from '../../constants/AppConstants'
import {cfg, credential} from "../../utils/AxiosUtils"
import {handleErr} from "../Utils"

const PlusAPIEvents = AppConstants.PlusAPIEvents

const hdl = handleErr(PlusAPIEvents.ADMUSER_LIST_NG)

function api() {
  return new AdmUserListApi(cfg())
}

const Action = {
  async loadUsers(){
    const res = await hdl(api().getAdmUsers(credential()))
    const users = res.data.users
    AdmUserList.loadUsersOK(users)
    return users
  },

  async deleteUsers(userIds) {
    const res = await hdl(api().deleteAdmUsers(userIds, credential()))
    this.noticeChanged()
    return res.data
  },

  async addUser(sysadminUserId: string, mode: Mode) {
    const res = await hdl(api().createAdmUsers(sysadminUserId, mode, credential()))
    this.noticeChanged()
    return res.data
  },

  async setPassword(sysadminUserId: string, password: string) {
    const res = await hdl(api().setPassword(sysadminUserId, password, credential()))
    this.noticeChanged()
    return res.data
  },

  async setMode(sysadminUserId: string, mode: Mode) {
    const res = await hdl(api().putAdmUser(sysadminUserId, mode, credential()))
    this.noticeChanged()
    return res.data
  },

  noticeChanged() {
    // 変更があったらロードし直す責任をViewではなくActionに持たせる
    // Storeでやってもいいけど、そうするとAction<->Storeと互いに知り合うのでちょっと嫌だった
    this.loadUsers()
  }
}
export default Action
