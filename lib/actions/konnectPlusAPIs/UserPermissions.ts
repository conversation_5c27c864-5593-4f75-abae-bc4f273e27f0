import AppConstants from "../../constants/AppConstants";
import AppDispatcher from "../../dispatcher/AppDispatcher";
import APIRoot from "../APIRoot";
import Utils from "../../utils/Utils";

import request from "superagent";
import { MyPermissionsResponse, GetUserPermissionsResponse, GetUserPermissionByIdResponse, GetBaseUserPermissionsResponse } from '../../components/admin/manage/userPermissions/types';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  // mba-dashboard, mba-statでも使用
  getMyPermissions() {
    request
      .get(`${APIRoot.get()}/plusapi/userPermissions/user/myPermissions`)
      .set({
        "X-CSRF-Token": antiCsrfToken,
        "If-Modified-Since": "Thu, 01 Jun 1970 00:00:00 GMT",
      })
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_MY_PERMISSIONS,
            data: {
              error: err,
            },
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_MY_PERMISSIONS,
            data: res.body as MyPermissionsResponse
          });
        }
      });
  },

  getUserPermissions() {
    const optionQuery: { auth_domainId?: string } = {};
    if (APIRoot.getAuthDomainId() !== null) {
      optionQuery.auth_domainId = APIRoot.getAuthDomainId();
    }
    request
      .get(`${APIRoot.get()}/plusapi/userPermissions`)
      .set({
        "X-CSRF-Token": antiCsrfToken,
        "If-Modified-Since": "Thu, 01 Jun 1970 00:00:00 GMT",
      })
      .query(optionQuery)
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_USER_PERMISSIONS,
            data: {
              error: err,
            },
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_USER_PERMISSIONS,
            data: res.body as GetUserPermissionsResponse
          });
        }
      });
  },
  getUserPermissionById(permissionControlId) {
    const optionQuery: { auth_domainId?: string } = {};
    if (APIRoot.getAuthDomainId() !== null) {
      optionQuery.auth_domainId = APIRoot.getAuthDomainId();
    }
    request
      .get(`${APIRoot.get()}/plusapi/userPermissions/${permissionControlId}`)
      .set({
        "X-CSRF-Token": antiCsrfToken,
        "If-Modified-Since": "Thu, 01 Jun 1970 00:00:00 GMT",
      })
      .query(optionQuery)
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_USER_PERMISSION_BY_ID,
            data: {
              error: err,
            },
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_USER_PERMISSION_BY_ID,
            data: res.body as GetUserPermissionByIdResponse
          });
        }
      });
  },
  getBaseUserPermissions() {
    const optionQuery: { auth_domainId?: string } = {};
    if (APIRoot.getAuthDomainId() !== null) {
      optionQuery.auth_domainId = APIRoot.getAuthDomainId();
    }
    request
      .get(`${APIRoot.get()}/plusapi/userBasePermissions`)
      .set({
        "X-CSRF-Token": antiCsrfToken,
        "If-Modified-Since": "Thu, 01 Jun 1970 00:00:00 GMT",
      })
      .query(optionQuery)
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_USER_BASE_PERMISSIONS,
            data: {
              error: err,
            },
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_USER_BASE_PERMISSIONS,
            data: res.body as GetBaseUserPermissionsResponse
          });
        }
      });
  },
  postUserPermission(data) {
    const optionQuery: { auth_domainId?: string } = {};
    if (APIRoot.getAuthDomainId() !== null) {
      optionQuery.auth_domainId = APIRoot.getAuthDomainId();
    }
    request
      .post(`${APIRoot.get()}/plusapi/userPermissions`)
      .set({
        "X-CSRF-Token": antiCsrfToken,
        "Content-Type": "application/json",
      })
      .query(optionQuery)
      .send(data)
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.POST_USER_PERMISSION,
            data: {
              error: err,
            },
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.POST_USER_PERMISSION,
            data: res.body
          });
        }
      });
  },
  putUserPermissionById(permissionControlId, data) {
    const optionQuery: { auth_domainId?: string } = {};
    if (APIRoot.getAuthDomainId() !== null) {
      optionQuery.auth_domainId = APIRoot.getAuthDomainId();
    }
    request
      .put(`${APIRoot.get()}/plusapi/userPermissions/${permissionControlId}`)
      .set({
        "X-CSRF-Token": antiCsrfToken,
        "Content-Type": "application/json",
      })
      .query(optionQuery)
      .send(data)
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.PUT_USER_PERMISSION,
            data: {
              error: err,
            },
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.PUT_USER_PERMISSION,
            data: res.body
          });
        }
      });
  },
  deleteUserPermissionById(permissionControlId, permitLevel, successCallback) {
    const optionQuery: { auth_domainId?: string } = {};
    if (APIRoot.getAuthDomainId() !== null) {
      optionQuery.auth_domainId = APIRoot.getAuthDomainId();
    }
    request
      .delete(`${APIRoot.get()}/plusapi/userPermissions/${permissionControlId}?permitLevel=${permitLevel}`)
      .set({
        "X-CSRF-Token": antiCsrfToken,
      })
      .query(optionQuery)
      .end((err, res) => {
        if (err) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.DELETE_USER_PERMISSION,
            data: {
              error: err,
            },
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.DELETE_USER_PERMISSION,
            data: {
              error: 0,
              id: permissionControlId
            }
          });
          successCallback();
        }
      });
  },
};

export default Action;
