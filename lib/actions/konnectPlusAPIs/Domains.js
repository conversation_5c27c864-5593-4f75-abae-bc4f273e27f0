import _ from 'lodash-compat';
import assign from 'object-assign';
import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';
import {DOMAIN_ATTRIBUTE_TYPES} from '../../stores/apiResults/Domains';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

let isExists = (val) => {
  return !_.isUndefined(val) && !_.isNull(val);
};

const Action = {

  get() {
    request
      .get(`${APIRoot.get()}/plusapi/domains`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_PLUS_DOMAINS,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_PLUS_DOMAINS,
            data: res.body
          });
        }
      });
  },

  readByOperator(domainId) {
    request
      .get(`${APIRoot.get()}/plusccapi/operatorAllowedSettings/${domainId}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_PLUS_DOMAIN,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_PLUS_DOMAIN,
            data: {
              error:res.body.error,
              domain: assign({id: domainId}, res.body.plusccDomain),
            }
          });
        }
      });
  },

  getById(params) {
    if (Utils.isOperatorAccess() || Utils.isSupervisorAccess()){
      return Action.readByOperator(params.domainId);
    }
    let auth_domainId;
    
    if (APIRoot.getAuthDomainId() === 'anonymous') {
      auth_domainId = params.domainId
    } else {
      auth_domainId = APIRoot.getAuthDomainId()
    }
    let targetDomainId = params.domainId || APIRoot.getAuthDomainId();

    request
      .get(APIRoot.get() + '/plusapi/domains/' + encodeURIComponent(targetDomainId))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: auth_domainId
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_PLUS_DOMAIN,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_PLUS_DOMAIN,
            data: res.body
          });
        }
      });
  },

  post(domainId, name) {
    let params = {
      domainId,
      name,
      auth_domainId: ''
    };
    request
      .post(APIRoot.get() + '/plusapi/domains/' + encodeURIComponent(domainId))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.POST_PLUS_DOMAIN_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.POST_PLUS_DOMAIN_NG,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.POST_PLUS_DOMAIN_OK,
              data: res.body
            });
          }
        }
      });
  },

  put(domainId, opt){
    let params = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: domainId,
      _method : 'put'
    };

    Object.keys(DOMAIN_ATTRIBUTE_TYPES).forEach((key) => {
      const tpe = DOMAIN_ATTRIBUTE_TYPES[key];
      switch (tpe){
        case 'array':
          if (isExists(opt[key])) {
            if (opt[key].join) {
              params[key] = opt[key].join(',');
            } else {
              params[key] = opt[key];
            }
          }
          break;
        case 'number':
          if (!_.isNaN(Number(opt[key]))) {
            params[key] = Number(opt[key]);
          }
          break;
        case 'boolean':
          if (_.isBoolean(opt[key])) {
             params[key] = opt[key] ? 'true': 'false';
          }
          break;
        default:
          if (isExists(opt[key])){
            params[key] = opt[key];
          }
      }
    });

    const targetDomainId = APIRoot.getAuthDomainId() && APIRoot.getAuthDomainId() !== 'anonymous' ? APIRoot.getAuthDomainId() : domainId;
    request
      .post(APIRoot.get() + '/plusapi/domains/' + encodeURIComponent(targetDomainId))
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .type('form')
      .send(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.PUT_PLUS_DOMAIN_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.PUT_PLUS_DOMAIN_NG,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.PUT_PLUS_DOMAIN_OK,
              data: res.body
            });
          }
        }
      });
  },

  del(domainId) {
    request
      .post(APIRoot.get() + '/plusapi/domains/' + encodeURIComponent(domainId))
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .type('form')
      .send({
        'auth_domainId': '',
        '_method': 'delete'
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.DELETE_PLUS_DOMAIN_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.DELETE_PLUS_DOMAIN_NG,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.DELETE_PLUS_DOMAIN_OK,
              data: res.body
            });
          }
        }
      });
  },

};

export default Action;
