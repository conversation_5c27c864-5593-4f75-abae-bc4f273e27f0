import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import { CCUtils } from '../../konnect/cc/Engine';
import APIRoot from '../APIRoot';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const PARAM_DEBUG_USER = 'param_debugUser';
const PARAM_DISPLAY_OPTION_ON_DAILY_REPORT = 'param_displayOptionOnDailyReport';

let _post = (uid, params={}) => {

  if (APIRoot.getAuthDomainId() !== null) {
    params.auth_domainId = APIRoot.getAuthDomainId();
  }

  return new Promise((resolve, reject) => {
    request
    .post(`${APIRoot.get()}/plusapi/userAttributes/${encodeURIComponent(uid)}`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send(params)
    .end((err, res) => {
      if (err){
        reject(err);
      } else {
        resolve(res);
      }
    });
  });
};

const Action = {
  PARAM_DEBUG_USER,
  PARAM_DISPLAY_OPTION_ON_DAILY_REPORT,

  search(query={}) {
    request
    .get(`${APIRoot.get()}/plusapi/userAttributes`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .query({
      query: JSON.stringify(query),
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    })
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.SEARCH_USER_ATTRIBUTES,
          data: {
            error: -1
          }
        });
      } else {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.SEARCH_USER_ATTRIBUTES,
          data: res.body
        });
      }
    });
  },

  get(uid, optionQuery={}) {

    if (APIRoot.getAuthDomainId() !== null) {
      optionQuery.auth_domainId = optionQuery.auth_domainId || APIRoot.getAuthDomainId();
    }
    
    const endPoint = CCUtils.isGuest(uid) ? 'plusccapi/guestUserAttributes' : 'plusapi/userAttributes';

    request
    .get(`${APIRoot.get()}/${endPoint}/${encodeURIComponent(uid)}`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .query(optionQuery)
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.GET_USER_ATTRIBUTES,
          data: {
            error: -1
          }
        });
      } else {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.GET_USER_ATTRIBUTES,
          data: res.body,
          uid,
        });
      }
    });
  },

  post(uid, params={}) {
    _post(uid, params)
      .then((res) => {
              if (res.body.error !== 0) {
                AppDispatcher.dispatch({
                  type: PlusAPIEvents.POST_USER_ATTRIBUTES_NG,
                  data: res.body
                });
              } else {
                AppDispatcher.dispatch({
                  type: PlusAPIEvents.POST_USER_ATTRIBUTES_OK,
                  data: res.body
                });
              }
            },
            (err) => {
              Utils.logoutIfneccessary(err);
              AppDispatcher.dispatch({
                type: PlusAPIEvents.POST_USER_ATTRIBUTES_NG,
                data: {
                  error: -1
                }
              });
            }
      );
  },

  del(uid, params={}) {
    params['_method'] = 'delete';
    if (APIRoot.getAuthDomainId() !== null) {
      params.auth_domainId = APIRoot.getAuthDomainId();
    }
    request
    .post(`${APIRoot.get()}/plusapi/userAttributes/${encodeURIComponent(uid)}`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send(params)
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.DELETE_USER_ATTRIBUTES_NG,
          data: {
            error: -1
          }
        });
      } else {
        if (res.body.error !== 0) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.DELETE_USER_ATTRIBUTES_NG,
            data: res.body
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.DELETE_USER_ATTRIBUTES_OK,
            data: res.body
          });
        }
      }
    });
  },

  setDebug(uid) {
    Action.post(uid, {PARAM_DEBUG_USER: 1});
  },

  unsetDebug(uid) {
    Action.del(uid, {PARAM_DEBUG_USER: 0});
  }
};
export default Action;
export {
  PARAM_DISPLAY_OPTION_ON_DAILY_REPORT
}
