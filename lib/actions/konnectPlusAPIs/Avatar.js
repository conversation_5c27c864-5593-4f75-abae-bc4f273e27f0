'use strict';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

let _uploadAvatarByAjax = (uid, file, isPublic) => {
  return new Promise(function(resolve, reject){
    let form = new FormData();
    form.append('data', file);
    let endPoint = isPublic ? '/avatarPublic' : '/avatar';
    let req = new XMLHttpRequest();
    req.open('POST', APIRoot.get() + '/plusapi/users/' + encodeURIComponent(uid) + endPoint);
    req.onload = function() {
      if (req.status === 200) {
        let result = req.responseText;
        let data = Utils.tryJSONParse(result);
        if (data.error === 0){
          resolve(data);
        } else {
          reject(data);
        }
      } else {
        reject({error: -1});
      }
    };
    req.withCredentials = true;
    req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
    req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
    req.send(form);
  });
};

const Action = {

  uploadAvatar(uid, file, isPublic, seq) {
    _uploadAvatarByAjax(uid, file, isPublic)
    .then(
      (data) => {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.UPLOAD_AVATAR_OK,
          data: data,
          uid: uid,
          isPublic: isPublic,
          seq: seq
        });
      },
      (data) => {
        Utils.log('fail to upload', arguments);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.UPLOAD_AVATAR_NG,
          data: data,
          uid: uid,
          seq: seq
        });
      }
    );
  }

};

export default Action;
