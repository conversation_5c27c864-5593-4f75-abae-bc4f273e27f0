'use strict';

import request from 'superagent';

import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  signup(mail, name) {
    request
      .post(APIRoot.get() + '/plusapi/anonymousUser')
      .withCredentials()
      .type('form')
      .send({
        mail: mail,
        name: name,
        'csrf-token': antiCsrfToken
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.SIGNUP,
            data: {
              error: -1,
              mail: mail
            }
          });
        } else {
          let data = res.body;
          // email will be used as plusId
          data.plusId = mail;
          AppDispatcher.dispatch({
            type: PlusAPIEvents.SIGNUP,
            data: res.body
          });
        }
      });
  }

};

export default Action;
