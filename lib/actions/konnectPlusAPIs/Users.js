import _ from 'lodash-compat';
import assign from 'object-assign';
import jsonpipe from 'jsonpipe';
import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

let cachedUserIdsRoomIds = [];
let exportReq;

const Action = {

  search(opt={}) {

    // namePrefixes==${STRING}                 # 検索文字列 (全角半角スペース、タブ文字で区切って複数の文字列を指定可能) (前方一致のAND条件で検索されます) (オプション)
    // +      permitLevels==${CSV_INT}                # 検索対象にするユーザーの権限レベルをCSVの数値で指定する (未指定時は全権限) (オプション)
    // +      permissionControlIds==${CSV_STRING}     # 検索対象にするユーザーのユーザー制御権限設定IDをカンマ区切りの文字列で指定する
    // +      foreignUserId==${STRING}                # 外部連携ID (オプション。このパラメーターを指定する場合は、findOtherDomain, findInAnonymous を指定してはいけない)
    // +      foreignUserIdHistory==${STRING}         # 過去に設定されたことのある外部連携ID (オプション。このパラメーターを指定する場合は、findOtherDomain, findInAnonymous を指定してはいけない)
    // +      prevId==${STRING}                       # 前回の検索結果の続きを取得する場合に指定する (オプション)
    // +      limit==${INT}                           # 返される最大件数 (未指定時は100、最大100) (オプション)
    // +      findOtherDomain==${BOOL}                # 他ドメインも検索するかどうか (デフォルトは false) (オプション)
    // +      findInAnonymous==${BOOL}

    const permitLevels = opt.permitLevels || [];
    let params = {
      [opt.namePrefixes ? 'namePrefixes' : '_ignore'] : opt.namePrefixes,
      [permitLevels.length > 0 ? 'permitLevels' : '_ignore'] : permitLevels.join(','),
      [opt.permissionControlIds ? 'permissionControlIds' : '_ignore'] : opt.permissionControlIds,
      [opt.foreignUserId ? 'foreignUserId' : '_ignore'] : opt.foreignUserId,
      [opt.foreignUserIdHistory ? 'foreignUserIdHistory' : '_ignore'] : opt.foreignUserIdHistory,
      [opt.foreignUserIdHistory ? '_ignore' : 'findInAnonymous'] : opt.findInAnonymous,
      [opt.foreignUserIdHistory ? '_ignore' : 'findOtherDomain'] : opt.findOtherDomain,
      prevId: opt.prevId || null,
      limit: opt.limit || 100,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };

    if (params.limit === 0){
      params.limit = 100;
    }

    if (opt.auth_domainId){
      params.auth_domainId = opt.auth_domainId;
    }

    request
      .get(`${APIRoot.get()}/plusapi/usersSearch`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USERS_SEARCH,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USERS_SEARCH,
            data: res.body
          });
        }
      });
  },

  readByOperator(uid, optionQuery={}) {
    request
    .post(`${APIRoot.get()}/plusapi/usersInRoom`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send(assign({_method: 'GET'}, optionQuery, {userIdsRoomIds: `${uid},dummy`}))
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.USER_GET,
          requestUid: uid,
          data: {
            error: err.status,
          }
        });
      } else {
        if (res.body.users && res.body.users[0]) {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_GET,
            requestUid: uid,
            data: {
              error: res.body.error,
              user: res.body.users[0]
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_GET,
            requestUid: uid,
            data: {
              error: -1
            }
          });
        }
      }
    });
  },

  getByUId(uid, optionQuery={}) {

    if (APIRoot.getAuthDomainId() !== null) {
      optionQuery.auth_domainId = optionQuery.auth_domainId || APIRoot.getAuthDomainId();
    }

    if (Utils.isOperatorAccess() && !optionQuery.isMe){
      return Action.readByOperator(uid, optionQuery);
    }

    request
      .get(APIRoot.get() + '/plusapi/users/' + encodeURIComponent(uid))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(optionQuery)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_GET,
            requestUid: uid,
            data: {
              error: -1,
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_GET,
            requestUid: uid,
            data: res.body
          });
        }
      });
  },

  exists(plusUserId, optionQuery={}) {
    const params = assign({
      plusUserId,
    }, optionQuery);
    if (APIRoot.getAuthDomainId() !== null) {
      params.auth_domainId = APIRoot.getAuthDomainId();
    }
    request
      .post(`${APIRoot.get()}/plusapi/userExists`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_EXISTS,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_EXISTS,
            data: res.body
          });
        }
      });
  },

  getKonnectUserByUId(uid, opt={}) {

    const query = assign({}, { withRooms: false, withLastAccessInfo: false}, opt);
    if(APIRoot.getAuthDomainId() !== null) {
      query.auth_domainId = opt.auth_domainId || APIRoot.getAuthDomainId();
    }

    request
      .get(APIRoot.get() + '/api/users/' + encodeURIComponent(uid))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(assign(query))
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_GET_KONNECT_USER,
            data: {
              error: err.status
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_GET_KONNECT_USER,
            data: res.body
          });
        }
      });
  },


  findByRoomId(uids, roomId, useCache=false) {
    if (!uids || !roomId) {
      return false;
    }

    let targetUids = uids;

    if (useCache) {
      targetUids = _.filter(uids, (uid) => {
        return !_.isUndefined(uid) && cachedUserIdsRoomIds.indexOf(uid) < 0;
      });
    }
    if (targetUids.length < 1) {
      return false;
    }
    cachedUserIdsRoomIds = cachedUserIdsRoomIds.concat(targetUids);

    let userIdsRoomIds = _.map(targetUids, (u) => {
      return u + ',' + roomId;
    }).join('\n');

    request
      .get(APIRoot.get() + '/plusapi/usersInRoom')
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        userIdsRoomIds: userIdsRoomIds,
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USERS_GET,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USERS_GET,
            data: res.body,
            uids: uids,
            roomId: roomId
          });
        }
      });
  },

  _post(plusUserId, postData={}, seq) {
    return new Promise((resolve, reject) => {
      request
        .post(`${APIRoot.get()}/plusapi/users/create`)
        .withCredentials()
        .type('form')
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .send(assign({}, {plusUserId}, postData))
        .end((err, res) => {
          if (res.error) {
            reject(res.error);
          } else {
            resolve(res.body);
          }
        });
    });
  },

  post(plusUserId, postData={}, seq) {

    return Action._post(plusUserId, postData, seq)
          .then(
            (result) => {
              if (result.error !== 0) {
                AppDispatcher.dispatch({
                  type: PlusAPIEvents.USER_CREATE_NG,
                  error: result.error,
                  data: result,
                  postData:postData,
                  plusUserId: plusUserId,
                  seq: seq
                });
              } else {
                AppDispatcher.dispatch({
                  type: PlusAPIEvents.USER_CREATE_OK,
                  data: result,
                  postData:postData,
                  plusUserId: plusUserId,
                  seq: seq
                });
              }
            },
            (err) => {
              Utils.logoutIfneccessary(err);
              AppDispatcher.dispatch({
                type: PlusAPIEvents.USER_CREATE_NG,
                error: -1,
                data: {
                  error: -1,
                  msg: err
                },
                postData:postData,
                plusUserId: plusUserId,
                seq: seq
              });
            }
          );
  },

  deleteUser(uid, optionQuery={}, domainId) {
    if (APIRoot.getAuthDomainId()) {
      optionQuery.auth_domainId = domainId || APIRoot.getAuthDomainId();
    }

    request
      .del(APIRoot.get() + '/plusapi/users/' + encodeURIComponent(uid))
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(optionQuery)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_DELETE,
            error: -1,
            data: {
              error: -1,
              msg: err
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USER_DELETE,
            data: res.body,
            uid: uid
          });
        }
      });
  },

  _put(uid, opt, seq) {

    if(APIRoot.getAuthDomainId() && !opt.auth_domainId) {
      opt.auth_domainId = APIRoot.getAuthDomainId()
    }

    return new Promise((resolve, reject) => {
      request
        .put(APIRoot.get() + '/plusapi/users/' + encodeURIComponent(uid))
        .withCredentials()
        .type('form')
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .send(opt)
        .end((err, res) => {
          if (res.error) {
            reject(res.error);
          } else {
            resolve(res.body);
          }
        });
    });
  },

  // ユーザー情報設定
  //   name:                  ユーザー名
  //   namePublic:            ユーザー名 (他のドメイン用)
  //   nameSearchWords:       ユーザー名検索文字列 (カンマ区切り)
  //   nameSearchWordsPublic: ユーザー名検索文字列 (他のドメイン用) (カンマ区切り)
  //   mail:                  メールアドレス
  //   password:              パスワード
  //   permitLevel:           権限レベル (ドメイン管理者のみ呼べる)
  //   exposeToDomainIds:     後悔先のドメインID (カンマ区切り) (ドメイン管理者のみ呼べる)
  //   accountLock:           アカウントロック (ドメイン管理者のみ呼べる)
  //   accountLockMemo:       アカウントロック時のメモ (ドメイン管理者のみ呼べる)
  put(uid, opt, seq) {
    return Action._put(uid, opt, seq)
                 .then(
                   (result) => {
                     if (result.error !== 0) {
                       AppDispatcher.dispatch({
                         type: PlusAPIEvents.USER_UPDATE_NG,
                         data: result,
                         uid: uid,
                         seq: seq
                       });
                     } else {
                       AppDispatcher.dispatch({
                         type: PlusAPIEvents.USER_UPDATE_OK,
                         data: result,
                         uid: uid,
                         opt: opt,
                         seq: seq
                       });
                     }
                   },
                   (err) => {
                     Utils.logoutIfneccessary(err);
                     AppDispatcher.dispatch({
                       type: PlusAPIEvents.USER_UPDATE_NG,
                       error: -1,
                       data: {
                         error: -1,
                         msg: err
                       },
                       uid: uid,
                       seq: seq
                     });
                   }
                 );
  },

  unlockAccount(uid, optionQuery={}){
    optionQuery.accountLock = 'false';
    Action.put(uid, optionQuery);
  },

  postThenPut(plusUserId, opt){
    let seq = 'createUser:'+Date.now();
    let dispatchToken = AppDispatcher.register(action => {
      if (!action.seq || seq !== action.seq) {return false; }
      AppDispatcher.unregister(dispatchToken);
      if (action.type === PlusAPIEvents.USER_CREATE_OK) {
        Action.put(action.data.user.id, opt, seq);
      }
    });
    Action.post(plusUserId, opt, seq);
  },

  resetPasswordByAdmin(plusUserId, mailTitle, mailBody, mailCc, mailBcc, limitSec, domainId) {
    const postParams = {
      'csrf-token': antiCsrfToken,
      plusUserId,
      mailTitle,
      mailBody,
      mailCc,
      mailBcc,
      limitSec: limitSec || 60 * 60,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: domainId || APIRoot.getAuthDomainId()
    };
    request
      .post(`${APIRoot.get()}/plusapi/users/password/resetByAdmin`)
      .withCredentials()
      .type('form')
      .send(postParams)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USERS_RESET_BY_ADMIN_NG,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USERS_RESET_BY_ADMIN_OK,
            data: res.body
          });
        }
      });
  },


  multiInsert(users){
    // simultaneousOperationsをmaxInteractionUserに置き換える
    const mappedUsers = users.map(user => {
      const replaceUser = { ...user };
      if (replaceUser.hasOwnProperty('simultaneousOperations')) {
        replaceUser.maxInteractionUser = replaceUser.simultaneousOperations;
        delete replaceUser.simultaneousOperations; // APIリクエストに不要なので削除
      }
      return replaceUser;
    });

    const len = mappedUsers.length;
    const multiInsertResult = [];
    AppDispatcher.dispatch({
      type: PlusAPIEvents.USERS_MULTI_INSERT_START,
      data: {
        multiInsertResult
      }
    });
    const next = (i) => {
      const user = mappedUsers[i];
      if (i < len && user) {
        if(APIRoot.getAuthDomainId()) user.auth_domainId = APIRoot.getAuthDomainId();
        Action._post(user.plusId, user)
              .then(
                (postResult) => {
                  if (postResult.error !== 0) {
                    multiInsertResult.push({
                                              plusId: user.plusId,
                                              index: i,
                                              error: postResult.error
                                            });
                    AppDispatcher.dispatch({
                      type: PlusAPIEvents.USERS_MULTI_INSERT_FAIL,
                      data: {
                        multiInsertResult: multiInsertResult,
                        error: postResult.error
                      }
                    });
                    if(postResult.error === 150) { // ユーザー登録数超過
                      AppDispatcher.dispatch({
                        type: PlusAPIEvents.USERS_MULTI_INSERT_NG,
                        error: 152,
                        data: {
                          multiInsertResult: multiInsertResult,
                          error: postResult.error
                        }
                      });
                    } else {
                      AppDispatcher.dispatch({
                        type: PlusAPIEvents.USERS_MULTI_INSERT_NG,
                        error: postResult.error,
                        isSilent: true,
                        data: {
                          multiInsertResult: multiInsertResult,
                          error: postResult.error
                        }
                      });
                      next(i + 1);
                    }
                  } else { // create success
                    return Action._put(postResult.user.id, user)
                                .then(
                                  (putResult) => {
                                    if (putResult.error !== 0) { // error
                                      // rollback
                                      Action.deleteUser(postResult.user.id);
                                      multiInsertResult.push({
                                                                plusId: user.plusId,
                                                                index: i,
                                                                error: putResult.error
                                                              });
                                      AppDispatcher.dispatch({
                                        type: PlusAPIEvents.USERS_MULTI_INSERT_FAIL,
                                        error: putResult.error,
                                        isSilent: true,
                                        data: {
                                          multiInsertResult: multiInsertResult,
                                          error: putResult.error
                                        }
                                      });
                                      AppDispatcher.dispatch({
                                        type: PlusAPIEvents.USERS_MULTI_INSERT_NG,
                                        error: putResult.error,
                                        isSilent: true,
                                        data: {
                                          multiInsertResult: multiInsertResult,
                                          error: putResult.error
                                        }
                                      });
                                      next(i + 1);
                                    } else { // put success
                                      multiInsertResult.push({
                                                                plusId: user.plusId,
                                                                index: i,
                                                                error: 0
                                                              });
                                      AppDispatcher.dispatch({
                                        type: PlusAPIEvents.USERS_MULTI_INSERT_PROGGRESS,
                                        error: 0,
                                        data: {
                                          multiInsertResult: multiInsertResult
                                        }
                                      });
                                      next(i + 1);
                                    }
                                  },
                                  (err) => {
                                    // rollback
                                    Action.deleteUser(postResult.user.id);
                                    multiInsertResult.push({
                                                              plusId: user.plusId,
                                                              index: i,
                                                              error: -1
                                                            });
                                    AppDispatcher.dispatch({
                                      type: PlusAPIEvents.USERS_MULTI_INSERT_FAIL,
                                      error: -1,
                                      data: {
                                        multiInsertResult: multiInsertResult,
                                        error: -1,
                                        msg: err
                                      }
                                    });
                                    AppDispatcher.dispatch({
                                      type: PlusAPIEvents.USERS_MULTI_INSERT_NG,
                                      error: -1,
                                      data: {
                                        error: -1,
                                        msg: err
                                      }
                                    });
                                  }
                                )

                    }
                },
                (err) => {
                  multiInsertResult.push({
                                          plusId: user.plusId,
                                          index: i,
                                          error: -1
                                        });
                  AppDispatcher.dispatch({
                    type: PlusAPIEvents.USERS_MULTI_INSERT_FAIL,
                    error: -1,
                    data: {
                      multiInsertResult: multiInsertResult,
                      error: -1,
                      msg: err
                    }
                  });
                  AppDispatcher.dispatch({
                    type: PlusAPIEvents.USERS_MULTI_INSERT_NG,
                    error: -1,
                    data: {
                      error: -1,
                      msg: err
                    }
                  });
                }
              )
      } else {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.USERS_MULTI_INSERT_SUCCESS,
          data: {
            multiInsertResult: multiInsertResult
          }
        });
      }
    };
    next(0);
  },

  abortExportRequest(){
    if (exportReq){
      try {
        exportReq.abort();
      } catch(e) {
        Utils.log('Failed to abort export request');
      }
    }
  },

  chunkExport(){
    Action.abortExportRequest();
    let i = 0;
    let users = [];
    let stTime = Date.now();
    AppDispatcher.dispatch({
      type: PlusAPIEvents.EXPORT_USERS_START,
      data: {
      }
    });

    let url = `${APIRoot.get()}/plusapi/usersAll`;
    if (APIRoot.getAuthDomainId() !== null) {
      url = `${url}?auth_domainId=${APIRoot.getAuthDomainId()}`;
    }

    exportReq = jsonpipe.flow(url, {
      method: 'GET',
      headers: {
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      },
      timeout: 15 * 60 * 1000,
      delimiter: '\n',
      success: (data) =>{
        i++;
        users.push(data);
        if (i % 5000 === 0){
          Utils.log(`Exporting Users... ${i} [${Date.now() - stTime} ms]`);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.EXPORT_USERS_PROGRESS,
            data: {
              users: users,
              count: i
            }
          });
        }
      },
      error: (errorMsg) => {
        if (errorMsg){
          Utils.warn(`Failed to export users ${errorMsg} [${Date.now() - stTime} ms]` );
          AppDispatcher.dispatch({
            type: PlusAPIEvents.EXPORT_USERS_NG,
            data: {
              error: -1,
              users: []
            }
          });
        }
      },
      complete: (statusText/*OK*/) => {
        Utils.log(`Finished Export Users... total: ${i}, statusText: ${statusText}  [${Date.now() - stTime} ms]`);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.EXPORT_USERS_OK,
          data: {
            error: 0,
            users: users,
            count: i
          }
        });
      }
    });
  },

  // https://mobilus-corp.atlassian.net/browse/MBAJ-1191
  // 管理画面>ユーザー検索から実施する場合の権限指定は'0,1,2,5,9'とし、ゲストは含めない
  getUserJSONDownloadUrl(permitLevels = '0,1,2,5,9,99') {
    let url = `${APIRoot.get()}/plusapi/usersAll?permitLevels=${permitLevels}`;
    if (APIRoot.getAuthDomainId() !== null) {
      url = `${url}&auth_domainId=${APIRoot.getAuthDomainId()}`;
    }
    return Utils.urlWithAntiCsrfToken(url);
  },

  // permissionControlIds==${CSV_STRING} # 検索対象にするユーザーのユーザー制御権限設定IDをカンマ区切りの文字列で指定する
  searchByPasswordUpdateDate(opt={}){
    const permitLevels = opt.permitLevels || [];
    let params = {
      [permitLevels.length > 0 ? 'permitLevels' : '_ignore'] : permitLevels.join(','),
      [opt.permissionControlIds ? 'permissionControlIds' : '_ignore'] : opt.permissionControlIds,
      limit: opt.limit || 100,
      passwordUpdateDate: opt.passwordUpdateDate,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/plusapi/usersSearchByPasswordUpdateDate`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USERS_SEARCH_BY_PASSWORD_UPDATE_DATE,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.USERS_SEARCH_BY_PASSWORD_UPDATE_DATE,
            data: res.body
          });
        }
      });
  },
};
export default Action;
