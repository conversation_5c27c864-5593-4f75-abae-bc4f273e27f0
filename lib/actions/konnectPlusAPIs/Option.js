import request from 'superagent';
import PlusAPIEvents from '../../constants/PlusAPIEvents';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const antiCsrfToken = Utils.getAntiCSRFToken();

const FILE_NAME = 'plusOption.json';

const Action = {
  get(params={}) {

    const query = {};
    if (APIRoot.getAuthDomainId() !== null) {
      query.auth_domainId = APIRoot.getAuthDomainId();
      params.domainId = APIRoot.getAuthDomainId();
    }

    const apiType = Utils.isOperatorAccess() ? 'userapi' : 'api';

    request
      .get(`${APIRoot.get()}/${apiType}/publicFiles/${encodeURIComponent(params.domainId)}/${FILE_NAME}`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_OPTION,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_OPTION,
            error: 0,
            data: res.body
          });
        }
      });
  },

  post(params={}){
    return new Promise((resolve, reject) => {
      const form = new FormData();
      const reqJSON = Utils.attachLastUpdateToJSONString(params.json);
      const blob = new Blob([reqJSON], {type: 'application/json'});
      form.append('file', blob, FILE_NAME);
      form.append('contentType', 'application/json; charset=utf-8');
      form.append('exposeToAll', false);
      form.append('noNeedAuth', true);
      if (APIRoot.getAuthDomainId() !== null) {
        form.append('auth_domainId', APIRoot.getAuthDomainId());
        params.domainId = APIRoot.getAuthDomainId();
      }
      const req = new XMLHttpRequest();
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${encodeURIComponent(params.domainId)}/${FILE_NAME}`)
      req.onload = function() {
        if (req.status === 200) {
          const result = req.responseText;
          const data = Utils.tryJSONParse(result);
          if (data.error === 0){
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject({error: -1});
        }
      };
      req.withCredentials = true;
      req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
      req.setRequestHeader('If-Modified-Since', 'Thu, 01 Jun 1970 00:00:00 GMT');
      req.send(form);
    })
    .then(
      (data) => {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.POST_OPTION_OK,
          data,
        });
      },
      (data) => {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.POST_OPTION_NG,
          data
        });
      }
    );
  }
};
export default Action;
