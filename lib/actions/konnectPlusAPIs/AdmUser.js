import assign from 'object-assign';
import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {

  get(params){
    request
    .post(`${APIRoot.get()}/plusapi/admUsersAll`)
    .withCredentials()
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send({
      _method: 'GET',
      auth_domainId: 'anonymous',
    })
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.GET_ADMUSER,
          data: {
            error: -1
          },
          params
        });
      } else {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.GET_ADMUSER,
          data: res.text,
          params
        });
      }
    });
  },

  post(params) {
    request
    .post(`${APIRoot.get()}/plusapi/admUsers/${encodeURIComponent(params.sysadminUserId)}`)
    .withCredentials()
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send({
      auth_domainId: 'anonymous',
    })
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.POST_ADMUSER_NG,
          data: {
            error: -1
          },
          params
        });
      } else {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.POST_ADMUSER_OK,
          data: res.body,
          params
        });
      }
    });
  },

  put(params) {
    request
    .post(`${APIRoot.get()}/plusapi/users/${encodeURIComponent(params.sysadminUserId)}`)
    .withCredentials()
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send(assign({
      _method: 'put',
      auth_domainId: 'anonymous',
    }, params))
    .end((err, res) => {
      if (err) {
        Utils.logoutIfneccessary(err);
        AppDispatcher.dispatch({
          type: PlusAPIEvents.PUT_ADMUSER_NG,
          data: {
            error: -1
          },
          params
        });
      } else {
        AppDispatcher.dispatch({
          type: PlusAPIEvents.PUT_ADMUSER_OK,
          data: res.body,
          params
        });
      }
    });
  },

  delete(params) {
    request
      .del(`${APIRoot.get()}/plusapi/users/${encodeURIComponent(params.sysadminUserId)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        auth_domainId: 'anonymous',
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.DELETE_ADMUSER_NG,
            data: {
              error: -1
            },
            params
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.DELETE_ADMUSER_OK,
            data: res.body,
            params
          });
        }
      });
  },
};
export default Action;
