import _ from 'lodash-compat';
import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';
import {SIZE_LIMITS} from '../../utils/Validator';

const PlusAPIEvents = AppConstants.PlusAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();
const Action = {
  get(uid, opt={}) {
    
    if (APIRoot.getAuthDomainId() !== null) {
      opt.auth_domainId = opt.auth_domainId || APIRoot.getAuthDomainId();
    }

    request
      .get(`${APIRoot.get()}/plusapi/directories/${encodeURIComponent(uid)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(opt)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_USER_DIRECTORIES,
            data: {
              error: err.status,
            },
            uid
          });
        } else {
          AppDispatcher.dispatch({
            type: PlusAPIEvents.GET_USER_DIRECTORIES,
            data: res.body,
            uid
          });
        }
      });
  },
  post(uid, opt={}) {

    if (APIRoot.getAuthDomainId() !== null) {
      opt.auth_domainId = APIRoot.getAuthDomainId();
    }

    request
      .post(`${APIRoot.get()}/plusapi/directories/${encodeURIComponent(uid)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(opt)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.POST_USER_DIRECTORIES_NG,
            data: {
              error: -1
            },
            uid
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.POST_USER_DIRECTORIES_NG,
              data: res.body,
              uid,
            });
          } else {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.POST_USER_DIRECTORIES_OK,
              data: res.body,
              uid,
            });
          }
        }
      });
  },

  postThenUpdateSearchWords(uid, opt={}, searchWords){

    if (APIRoot.getAuthDomainId() !== null) {
      opt.auth_domainId = APIRoot.getAuthDomainId();
    }

    request
      .post(`${APIRoot.get()}/plusapi/directories/${encodeURIComponent(uid)}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(opt)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: PlusAPIEvents.POST_USER_DIRECTORIES_NG,
            data: {
              error: -1
            },
            uid: uid
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: PlusAPIEvents.POST_USER_DIRECTORIES_NG,
              data: res.body
            });
          } else {
            let directoryWords = _.map(opt, (val, key) => {
              return val;
            });
            let newSearchWords = _.uniq(searchWords.concat(directoryWords)).join(',').substring(0, SIZE_LIMITS.PLUS_USER_SEARCH_WORDS);
            request
              .put(`${APIRoot.get()}/plusapi/users/${encodeURIComponent(uid)}`)
              .withCredentials()
              .type('form')
              .set({
                'X-CSRF-Token': antiCsrfToken,
                'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
              })
              .send({
                nameSearchWords: newSearchWords,
                [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
              })
              .end((err, res) => {
                // ignore search words update error
                AppDispatcher.dispatch({
                  type: PlusAPIEvents.POST_USER_DIRECTORIES_OK,
                  data: res.body
                });
              });
          }
        }
      });
  }
};
export default Action;
