import request from 'superagent';
import AppConstants from '../../constants/AppConstants';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const KnowledgeAPIEvents = AppConstants.KnowledgeAPIEvents;
const antiCsrfToken = Utils.getAntiCSRFToken();

const Action ={
  getByKeyword(keyword){
    const limit = 100;
    const params = {
      keyword,
      limit,
      [APIRoot.getAuthDomainId()!== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/knowledgeapi/knowledge`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGES_SEARCH,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGES_SEARCH,
            data: res.body.result
          });
        }
      });
  },

  getDetailById(id){
    const params = {
      [APIRoot.getAuthDomainId()!== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/knowledgeapi/knowledge/${encodeURIComponent(id)}`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err){
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGE_DETAIL,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGE_DETAIL,
            data: res.body
          });
        }
      });
  },

  removeById(id){
    const params = {
      [APIRoot.getAuthDomainId()!== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .del(`${APIRoot.get()}/knowledgeapi/knowledge/${encodeURIComponent(id)}`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err){
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGE_DELETE,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGE_DELETE,
            data: res.body,
            kId: id
          });
        }
      });
  },

  getKnowledgeEnableOrNot(){
    const params = {
      [APIRoot.getAuthDomainId()!== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/knowledgeapi/clientflags`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err){
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGE_ENABLE_OR_NOT,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGE_ENABLE_OR_NOT,
            data: res.body.result.knowledge_enabled,
          });
        }
      });
  },

  getHashTag(keyword) {
    const params = {
    keyword,
      [APIRoot.getAuthDomainId()!== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/knowledgeapi/hashtags`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err){
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGE_GET_HASH_TAG,
            data: []
          });
        } else {
          AppDispatcher.dispatch({
            type: KnowledgeAPIEvents.KNOWLEDGE_GET_HASH_TAG,
            data: res.body.result
          });
        }
      });
    },

    approveKnowledge(id) {
      const params = {
        [APIRoot.getAuthDomainId()!== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
      };
      request
        .put(`${APIRoot.get()}/knowledgeapi/knowledge/${encodeURIComponent(id)}/approve`)
        .withCredentials()
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .query(params)
        .end((err, res) => {
          if (err){
            Utils.logoutIfneccessary(err);
            AppDispatcher.dispatch({
              type: KnowledgeAPIEvents.KNOWLEDGE_APPROVE,
              data: {
                error: -1
              }
            });
          } else {
            AppDispatcher.dispatch({
              type: KnowledgeAPIEvents.KNOWLEDGE_APPROVE,
              data: res.body,
            });
          }
        });
    },

    unapproveKnowledge(id) {
      const params = {
        [APIRoot.getAuthDomainId()!== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
      };
      request
        .del(`${APIRoot.get()}/knowledgeapi/knowledge/${encodeURIComponent(id)}/approve`)
        .withCredentials()
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .query(params)
        .end((err, res) => {
          if (err){
            Utils.logoutIfneccessary(err);
            AppDispatcher.dispatch({
              type: KnowledgeAPIEvents.KNOWLEDGE_UNAPPROVE,
              data: {
                error: -1
              }
            });
          } else {
            AppDispatcher.dispatch({
              type: KnowledgeAPIEvents.KNOWLEDGE_UNAPPROVE,
              data: res.body,
            });
          }
        });
    },
};
export default Action
