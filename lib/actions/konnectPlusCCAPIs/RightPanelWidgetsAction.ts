// import request from 'superagent';

// import CCAPIEvents from '../../constants/CC_APIEvents';
// import AppDispatcher from '../../dispatcher/AppDispatcher';

// import Utils from '../../utils/Utils';
// import APIRoot from '../APIRoot';

// export interface RightPanelWidgetRequest {
//   id: string,
//   operation: string,
//   order: number,
//   title: string,
//   url: string,
//   iconType: string,
//   iconFileId: string
// }

// export interface putRightPanelWidgetsApiParams {
//   widgets: RightPanelWidgetRequest[]
// }

// const url = '/plusccapi/rightPanelWidgets';
// const antiCsrfToken = Utils.getAntiCSRFToken();
// const getSettingOk = CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK;
// const getSettingNg = CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG;
// const putSettingOk = CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_OK;
// const putSettingNg = CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_NG;

// const RightPanelWidgetsAction = {
//   getRightPanelWidgetsSettingsTemplate() {
//     request
//       .get(APIRoot.get() + url)
//       .withCredentials()
//       .type('form')
//       .set({
//         'X-CSRF-Token': antiCsrfToken,
//         'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
//       })
//       .end((err, res) => {
//         if (err || res.error) {
//           Utils.logoutIfneccessary(err);
//           AppDispatcher.dispatch({
//             type: getSettingNg,
//             error: res?.error?.status || -1,
//             data: {
//               error: -1
//             }
//           });
//         } else {
//           const error = res.body.error;
//           if (error === 0) {
//             AppDispatcher.dispatch({
//               type: getSettingOk,
//               data: res.body
//             });
//           } else {
//             AppDispatcher.dispatch({
//               type: getSettingNg,
//               data: {
//                 error,
//               }
//             });
//           }
//         }
//       });
//   },

//   putRightPanelWidgetsSettingsTemplate(params: putRightPanelWidgetsApiParams) {
//     request
//       .put(APIRoot.get() + url)
//       .withCredentials()
//       .type('json')
//       .send(params)
//       .set({
//         'X-CSRF-Token': antiCsrfToken,
//         'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
//       })
//       .end((err, res) => {
//         if (err || res.error) {
//           Utils.logoutIfneccessary(err);
//           AppDispatcher.dispatch({
//             type: putSettingNg,
//             error: res?.error?.status || -1,
//             data: {
//               error: -1
//             }
//           });
//         } else {
//           const error = res.body?.error || 0;
//           if (error === 0) {
//             AppDispatcher.dispatch({
//               type: putSettingOk,
//               data: res.body
//             });
//           } else {
//             AppDispatcher.dispatch({
//               type: putSettingNg,
//               data: {
//                 error,
//               }
//             });
//           }
//         }
//       });
//   },

//   deleteRightPanelWidgetsSettingsTemplate(id: string) {
//     request
//       .del(APIRoot.get() + `${url}/${id}`)
//       .withCredentials()
//       .type('form')
//       .set({
//         'X-CSRF-Token': antiCsrfToken,
//         'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
//         })
//       .end((err, res) => {
//         if (err || res.error) {
//           Utils.logoutIfneccessary(err);
//           AppDispatcher.dispatch({
//             type: getSettingNg,
//             error: res?.error?.status || -1,
//             data: {
//               error: -1
//             }
//           });
//         } else {
//           const error = res.body?.error || 0;
//           if (error === 0) {
//             AppDispatcher.dispatch({
//               type: getSettingOk,
//               data: res.body
//             });
//           } else {
//             AppDispatcher.dispatch({
//               type: getSettingNg,
//               data: {
//                 error,
//               }
//             });
//           }
//         }
//       });
//   }
    
// }

// export default RightPanelWidgetsAction;

import request from 'superagent';

import CCAPIEvents from '../../constants/CC_APIEvents';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

export interface RightPanelWidgetRequest {
  id: string;
  operation: string;
  order: number;
  title: string;
  url: string;
  iconType: string;
  iconFileId: string;
}

export interface putRightPanelWidgetsApiParams {
  widgets: RightPanelWidgetRequest[];
}

const url = '/plusccapi/rightPanelWidgets';
const antiCsrfToken = Utils.getAntiCSRFToken();

const RightPanelWidgetsAction = {
  getRightPanelWidgetsSettingsTemplate() {
    console.log('[API][GET] rightPanelWidgets: sending request...');
    request
      .get(APIRoot.get() + url)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        if (err || res.error) {
          console.error('[API][GET] Error:', err || res.error);
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
            error: res?.error?.status || -1,
            data: { error: -1 },
          });
        } else {
          console.log('[API][GET] Success:', res.body);
          const error = res.body.error;
          if (error === 0) {
            AppDispatcher.dispatch({
              type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK,
              data: res.body,
            });
          } else {
            AppDispatcher.dispatch({
              type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
              data: { error },
            });
          }
        }
      });
  },

  putRightPanelWidgetsSettingsTemplate(params: putRightPanelWidgetsApiParams) {
    console.log('[API][PUT] rightPanelWidgets: sending request...', params);
    request
      .put(APIRoot.get() + url)
      .withCredentials()
      .type('json')
      .send(params)
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        if (err || res.error) {
          console.error('[API][PUT] Error:', err || res.error);
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
            error: res?.error?.status || -1,
            data: { error: -1 },
          });
        } else {
          console.log('[API][PUT] Success:', res.body);
          const error = res.body?.error || 0;
          if (error === 0) {
            AppDispatcher.dispatch({
              type: CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_OK,
              data: res.body,
            });
          } else {
            AppDispatcher.dispatch({
              type: CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
              data: { error },
            });
          }
        }
      });
  },

  deleteRightPanelWidgetsSettingsTemplate(id: string) {
    console.log('[API][DELETE] rightPanelWidgets: sending request...', id);
    request
      .del(APIRoot.get() + `${url}/${id}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        if (err || res.error) {
          console.error('[API][DELETE] Error:', err || res.error);
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
            error: res?.error?.status || -1,
            data: { error: -1 },
          });
        } else {
          console.log('[API][DELETE] Success:', res.body);
          const error = res.body?.error || 0;
          if (error === 0) {
            AppDispatcher.dispatch({
              type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK,
              data: res.body,
            });
          } else {
            AppDispatcher.dispatch({
              type: CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG,
              data: { error },
            });
          }
        }
      });
  },
};

export default RightPanelWidgetsAction;
