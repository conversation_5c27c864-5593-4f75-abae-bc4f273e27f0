import request from 'superagent';

import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';

import APIRoot from '../APIRoot';
import CCAPIEvents from '../../constants/CC_APIEvents';

const antiCsrfToken = Utils.getAntiCSRFToken();

export const KEYS = [
  'wordsMatchWord', // MBA未使用 =${STRING}    # 禁止単語リスト文字列（改行区切り。オプション。未指定時はクリアされる）
  'checkIgnoreCase', // MBA未使用 =${BOOL}     # 大文字小文字の無視 (オプション。未指定時は konnect.conf の設定が使用される)
  'skipChars', // MBA未使用 =${STRING}        # 比較時に無視する文字 (オプション。未指定時は konnect.conf の設定が使用される)
  'wordDelimiter', // MBA未使用 =${STRING}     # 単語チェック時、文章を単語に区切る文字 (オプション。未指定時は konnect.conf の設定が使用される)

  'doFuzzySearch',  // MBAはtrue固定 比較時に正規化する
  '0,1,9_words', // MBA未使用 =${STRING}       # 権限レベル 0, 1, 9 のユーザーに適用される禁止語リスト
  '99_words', // =${STRING}          # 権限レベル 99 のユーザーに適用される禁止語リスト
  'words', // =${STRING}             # それ以外の権限レベルのユーザーに適用される禁止語リスト
];

const Action = {
  get() {
    const params = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    request
      .get(`${APIRoot.get()}/plusccapi/warnWord`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_WARNWORD,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_WARNWORD,
            data: res.body
          });
        }
      });
  },

  post(params={}) {
    const requestBody = {};
    KEYS.forEach((key) => {
      if (typeof params[key] !== 'undefined'){
        requestBody[key] = params[key];
      }
    })
    if (APIRoot.getAuthDomainId() !== null) {
      requestBody['auth_domainId'] = APIRoot.getAuthDomainId();
    }
    request
      .post(`${APIRoot.get()}/plusccapi/warnWord`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(requestBody)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.POST_WARNWORD_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: CCAPIEvents.POST_WARNWORD_NG,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: CCAPIEvents.POST_WARNWORD_OK,
              data: res.body
            });
          }
        }
      });
  },
};
export default Action;
