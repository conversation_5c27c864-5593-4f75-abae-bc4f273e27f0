import request from 'superagent';

import APIRoot from '../APIRoot';
import Utils from '../../utils/Utils';

const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  // このAPIは
  // メッセージ削除予約機能でテキスト更新を行う際に、msg.info4を最新化するために呼び出す必要があります。
  // Msg.putの直前、およびMarkedMessages.bulkDeleteMsgsの中で呼び出します。
  // UIに影響させないのでAppDispatcher.dispatchは行わない。
  // 将来的にユーザーのアクションが必要になる場合はこの関数をラップした関数でdispatchしてください。
  post(params = {}) {
    const req = request
      .post(`${APIRoot.get()}/plusccapi/privacyMasking`)
      .withCredentials()
    const requestBody = {
      text: params.text,
    };
    return new Promise((resolve, reject) => {
      req
        .type('form')
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .send(requestBody)
        .end((err, res) => {
          if (err) { // HTTPエラー
            Utils.warn(err);
            reject(err);
          }
          // HTTP呼び出し失敗以外はそのままinfo4として用いる
          resolve(res.body);
        });
    });
  },
};

export default Action;
