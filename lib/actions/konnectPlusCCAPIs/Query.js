'use strict';

import _ from 'lodash-compat';
import request from 'superagent';

import CCAPIEvents from '../../constants/CC_APIEvents';
import CCConstants from '../../constants/CC_Constants';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const antiCsrfToken = Utils.getAntiCSRFToken();
const LIMIT = 10000;

const Action = {

  exportGuests(domainId) {
    let steps = 0;
    let actionResult = {
      count:0,
      data: []
    };

    let countRequest,
        findRequest,
        getNextOrFinish;

    AppDispatcher.dispatch({
      type: CCAPIEvents.EXPORT_GUEST_START,
      data: {
      }
    });

    countRequest = () => {
      Utils.log('Start exporting GuestUsers');
      return new Promise((resolve, reject) => {
        let query = {
          method: 'count',
          coll: 'plus_users',
          query: JSON.stringify({domainId: domainId, permitLevel: CCConstants.PERMITLEVEL_GUEST, removed: false}),
          auth_domainId: domainId
       };
        request
          // @TODO @FIXME use api for non-admin user
          .get(APIRoot.get() + '/api/db/execQuery')
          .withCredentials()
          .type('form')
          .set({
            'X-CSRF-Token': antiCsrfToken,
            'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
          })
          .query(query)
          .end((err, res) => {
            if (err) {
              Utils.logoutIfneccessary(err);
              reject(-1);
            } else {
              let error = res.body.error;
              if (error !== 0) {
                reject(error);
              } else {
                actionResult.count = res.body.result;
                Utils.log(`Exporting GuestUsers... total: ${actionResult.count}`);
                resolve(res.body.result);
              }
            }
          });
      });
    };

    findRequest = (minCreatedDate, step) => {
      let query = {
        method: 'find',
        coll: 'plus_users',
        query: JSON.stringify({domainId: domainId, permitLevel: CCConstants.PERMITLEVEL_GUEST, removed: false, createdDate: {$gte: minCreatedDate}}),
        sort: JSON.stringify({createdDate: 1}),
        limit: LIMIT,
        auth_domainId: domainId
      };

      return new Promise((resolve, reject) => {
        request
          // @TODO @FIXME use api for non-admin user
          .get(APIRoot.get() + '/api/db/execQuery')
          .withCredentials()
          .type('form')
          .set({
            'X-CSRF-Token': antiCsrfToken,
            'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
          })
          .query(query)
          .end((err, res) => {
            if (err) {
              Utils.logoutIfneccessary(err);
              reject(-1);
            } else {
              let error = res.body.error;
              if (error !== 0) {
                reject(error);
              } else {
                Utils.log(`Exporting GuestUsers... step: ${step}/${steps}`);
                AppDispatcher.dispatch({
                  type: CCAPIEvents.EXPORT_GUEST_PROGRESS,
                  data: {
                    steps: steps,
                    step: step
                  }
                });
                resolve([res.body.result, step]);
              }
            }
          });
      }).then(getNextOrFinish);
    };

    getNextOrFinish = (values) => {
      let apiResult = values[0];
      let step = values[1];
      if (step < steps) {
        actionResult.data = actionResult.data.concat(apiResult);
        let lastRecord = _.last(apiResult);
        let minCreatedDate = lastRecord.createdDate;
        return findRequest(minCreatedDate, step + 1);
      } else {
        actionResult.data = actionResult.data.concat(apiResult);
        return Promise.resolve();
      }
    };

    countRequest()
    .then((totalCount)=>{
      steps = Math.ceil((totalCount / LIMIT)) + 1;
      AppDispatcher.dispatch({
        type: CCAPIEvents.EXPORT_GUEST_COUNT,
        data: {
          totalCount: totalCount,
          steps: steps
        }
      });
      return findRequest(0, 1);
    })
    .then(() => {
      actionResult.data = _.uniq(actionResult.data, (d)=>{return d._id; });
      Utils.log('Finished to export GuestUsers');
      AppDispatcher.dispatch({
        type: CCAPIEvents.EXPORT_GUEST_OK,
        data: {
          users: actionResult.data
        }
      });
    }, (error) => {
      Utils.warn('Failed to export GuestUsers', error);
      AppDispatcher.dispatch({
        type: CCAPIEvents.EXPORT_GUEST_NG,
        data: {
          error: error
        }
      });
    });
  }
};


export default Action;
window.exportGuests = Action.exportGuests;
