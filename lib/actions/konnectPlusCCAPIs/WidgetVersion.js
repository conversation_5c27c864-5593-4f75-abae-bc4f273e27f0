import request from 'superagent';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';
import CCAPIEvents from '../../constants/CC_APIEvents';

const Action = {
  get() {
    request
      .get(`${APIRoot.getAssetsCdnURL()}/assets/script/embed/widgetVersions.json`)
      .set({
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_WIDGET_VERSIONS,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_WIDGET_VERSIONS,
            data: res.body
          });
        }
      });
    },
};
export default Action;
