import request from 'superagent';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';
import CCAPIEvents from '../../constants/CC_APIEvents';

const Action = {
  get() {
    request
      .get(`${APIRoot.getAssetsCdnURL()}/assets/script/chatwindow/widgetVersions.json`)
      .set({
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        const data = err ? { error: -1 } : res.body;
        if (err) {
          Utils.logoutIfneccessary(err);
        }
        AppDispatcher.dispatch({
          type: CCAPIEvents.GET_WIDGET_VERSIONS_2,
          data: data
        });
      });
  },
};

export default Action;
