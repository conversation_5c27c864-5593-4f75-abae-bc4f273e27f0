import {Survey, SurveyApi, SurveyConfig} from "../../openapi/pluscc"
import {cfg, credential} from "../../utils/AxiosUtils"
import {AxiosError} from "axios"
import {AlertMessageContext} from "../../components/AlertBox"
import {setErrorAlertMessage} from "../../components/admin/manage/survey/validation"
import {toOperationResponse} from "../Utils"

function api() {
  return new SurveyApi(cfg())
}

export interface UploadingState {
  isUploading: boolean
}

/**
 * APIエラーが出たときにAlertMessageに表示する
 * API呼び出し前後にisUploadingをセットする
 *
 * todo setErrorAlertMessageがsurvey以下にあるので共通部分に移動できない
 *      survey/validationをを共通部分に移動したらこちらも移動する
 */
export class ApiWrapper {
  constructor(private alertMessage: AlertMessageContext,
              private i18n,
              private setState: (state: UploadingState) => void) {
  }

  wrap = async <T>(fn: () => Promise<T>) => {
    this.setState({isUploading: true})
    try {
      const res = await fn()
      this.setState({isUploading: false})
      return res
    } catch (err) {
      const error = (err as AxiosError<{ error: number }>).response.data.error
      setErrorAlertMessage(this.i18n)(this.alertMessage, error)
      this.setState({isUploading: false})
      throw err
    }
  }

  wrap0 = <T>(fn: () => Promise<T>) => () => this.wrap(() => fn())
  wrap1 = <A, T>(fn: (a: A) => Promise<T>) => (a: A) => this.wrap(() => fn(a))
  wrap2 = <A, B, T>(fn: (a: A, b: B) => Promise<T>) => (a: A, b: B) => this.wrap(() => fn(a, b))
}

export const SurveyAction = {
  getSurveyConfig: async () => {
    const res = await api().getSurveyConfig(credential())
    return res.data.config
  },

  putSurveyConfig: async (cfg: SurveyConfig) => {
    return toOperationResponse(api().putSurveyConfig(cfg, credential()))
  },

  createSurvey: (survey: Survey) => {
    return toOperationResponse(api().createSurvey(survey, credential()))
  },

  putSurvey: (id: string, survey: Survey) => {
    return toOperationResponse(api().replaceSurvey(id, survey, credential()))
  },

  listSurveys: () => {
    return api().listSurvey(credential()).then(a => a.data)
  },

  setDefaultSurvey: (id: string) => {
    return api().replaceDefaultSurvey(id, credential())
  },

  getSurvey: (id: string) => {
    return api().getSurvey(id, credential()).then(a => a.data)
  },

  deleteSurvey: (id: string) => {
    return api().deleteSurvey(id, credential()).then(a => a.data)
  },
}
