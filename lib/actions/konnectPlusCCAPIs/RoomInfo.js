import _ from 'lodash-compat';
import request from 'superagent';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';
import CCAPIEvents from '../../constants/CC_APIEvents';

const antiCsrfToken = Utils.getAntiCSRFToken();
const Action = {
  updateRoomExtraTags(roomId, params) {
    const authDomainId = APIRoot.getAuthDomainId();
    if (authDomainId !== null) {
      params['auth_domainId'] = authDomainId;
    }

    request
      .post(`${APIRoot.get()}/plusccapi/roomInfo/${encodeURIComponent(roomId)}/misc`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(params)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.ROOMS_EXTRA_TAGS_UPDATE_NG,
            data: {
              error: -1,
            }
          });
        } else {
          if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: CCAPIEvents.ROOMS_EXTRA_TAGS_UPDATE_NG,
              data: res.body,
            });
          } else {
            AppDispatcher.dispatch({
              type: CCAPIEvents.ROOMS_EXTRA_TAGS_UPDATE_OK,
              data: res.body,
            });
          }
        }
      });
  },

  updateRoomOperator(userId, roomId, comment, groupId, prevOperator) {
    const queryParams = Utils.withoutEmptyObj({
      operator: userId || '',
      groupId,
      comment,
      prevOperator: prevOperator || '',
    });

    request
      .post(`${APIRoot.get()}/plusccapi/roomInfo/${encodeURIComponent(roomId)}/operator`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(queryParams)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.CHANGE_PERSON_IN_CHARGE_NG,
            data: {
              error: -1
            }
          });
        } else if (res.body.error !== 0) {
            AppDispatcher.dispatch({
              type: CCAPIEvents.CHANGE_PERSON_IN_CHARGE_NG,
              data: {
                error: res.body.error
              }
            });
          } else {
          AppDispatcher.dispatch({
            type: CCAPIEvents.CHANGE_PERSON_IN_CHARGE_OK,
            data: res.body
          });
        }
      });
  },
};
export default Action;
