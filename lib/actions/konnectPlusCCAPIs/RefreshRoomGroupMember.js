import request from 'superagent';
import CCAPIEvents from '../../constants/CC_APIEvents';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {
  post(params={}) {
    const requestBody = {
      groupId: params.groupId
    };
    request
      .post(`${APIRoot.get()}/plusccapi/refreshRoomGroupMember`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(requestBody)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.REFRESH_ROOM_GROUP_MEMBER_NG,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: res.body.error === 0 ? CCAPIEvents.REFRESH_ROOM_GROUP_MEMBER_OK : CCAPIEvents.REFRESH_ROOM_GROUP_MEMBER_NG,
            data: res.body,
          });
        }
      });
  },
};
export default Action;
