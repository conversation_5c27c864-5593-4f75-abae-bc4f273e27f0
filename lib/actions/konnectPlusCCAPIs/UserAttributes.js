import request from 'superagent';
import APIRoot from '../APIRoot';
import PlusUserAttributeAPI from '../konnectPlusAPIs/UserAttributes';
import CCAPIEvents from '../../constants/CC_APIEvents';
import CCConstants from '../../constants/CC_Constants';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
const antiCsrfToken = Utils.getAntiCSRFToken();

const PARAM_EMBED_FIELD = 'param_embedField';

let _post = (uid, questionKey, params={}) => {
  params.questionKey = questionKey;
  if (APIRoot.getAuthDomainId() !== null) {
    params.auth_domainId = APIRoot.getAuthDomainId();
  }
  return new Promise((resolve, reject) => {
    request
    .post(`${APIRoot.get()}/plusapi/userAttributes/${encodeURIComponent(uid)}`)
    .withCredentials()
    .type('form')
    .set({
      'X-CSRF-Token': antiCsrfToken,
      'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
    })
    .send(params)
    .end((err, res) => {
      if (err){
        reject(err);
      } else {
        resolve(res);
      }
    });
  });
};

const Action = {
  search: PlusUserAttributeAPI.search,

  get: PlusUserAttributeAPI.get,

  del: PlusUserAttributeAPI.del,

  post(uid, questionKey, params={}) {
    PlusUserAttributeAPI.post(uid, params);
    // _post(uid, questionKey, params)
    //   .then(
    //     (res) => {
    //       if (res.body.error !== 0) {
    //         AppDispatcher.dispatch({
    //           type: CCAPIEvents.POST_USER_ATTRIBUTES_NG,
    //           data: res.body
    //         });
    //       } else {
    //         AppDispatcher.dispatch({
    //           type: CCAPIEvents.POST_USER_ATTRIBUTES_OK,
    //           data: res.body
    //         });
    //       }
    //     },
    //     (err) => {
    //       Utils.logoutIfneccessary(err);
    //       AppDispatcher.dispatch({
    //         type: CCAPIEvents.POST_USER_ATTRIBUTES_NG,
    //         data: {
    //           error: -1
    //         }
    //       });
    //     }
    //   );
  },

  setDebug(uid) {
    Action.post(uid, CCConstants.QUESTION_KEY_UNUSED, {[PlusUserAttributeAPI.PARAM_DEBUG_USER]: 1});
  },

  unsetDebug(uid) {
    Action.post(uid, CCConstants.QUESTION_KEY_UNUSED, {[PlusUserAttributeAPI.PARAM_DEBUG_USER]: 0});
  },

  setEmbedfieldMulti(fieldKey, userAndValues){

    let len = userAndValues.length;
    let successUids = [];

    let next = (i) => {
      let userAndValue = userAndValues[i];
      if (i < len && userAndValue){
        let param = {};
        param[`param_${fieldKey}`] = userAndValue.val;
        param[PARAM_EMBED_FIELD] = fieldKey;
        _post(userAndValue.uid, CCConstants.QUESTION_KEY_UNUSED, param)
        .then(
          (res) => {
            if (res.body.error !== 0) {
              AppDispatcher.dispatch({
                type: CCAPIEvents.SET_EMBED_USERS_ATTRIBUTES_FAIL,
                data: {
                  error: res.body.error,
                  successUids: successUids
                }
              });
            } else {
              successUids.push(userAndValue.uid);
              AppDispatcher.dispatch({
                type: CCAPIEvents.SET_EMBED_USERS_ATTRIBUTES_PROGRESS,
                data: {
                  error: 0,
                  successUids: successUids
                }
              });
              next(i + 1);
            }
        },
        (err) => {
          AppDispatcher.dispatch({
            type: CCAPIEvents.SET_EMBED_USERS_ATTRIBUTES_FAIL,
            data: {
              error: err,
              successUids: successUids
            }
          });
        });
      } else {
        AppDispatcher.dispatch({
          type: CCAPIEvents.SET_EMBED_USERS_ATTRIBUTES_SUCCESS,
          data: {
            error: 0,
            successUids: successUids
          }
        });
      }
    };
    next(0);
  }
};
export default Action;
