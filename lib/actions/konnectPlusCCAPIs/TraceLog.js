import request from 'superagent';
import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';
import AppDispatcher from '../../dispatcher/AppDispatcher';
import CCAPIEvents from '../../constants/CC_APIEvents';

// TODO 他のAction系と共通化する
const antiCsrfToken = Utils.getAntiCSRFToken();
const Action = {
  getTraceInfo(roomId) {
    return new Promise((resolve, reject) => {
      const query = {
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
        roomId
      };

      const roomIdInUrl = encodeURIComponent(roomId);

      request
        .get(`${APIRoot.get()}/plusapi/tracelog/${roomIdInUrl}`)
        .withCredentials()
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .query(query)
        .end((err, res) => {
          if (err) {
            Utils.logoutIfneccessary(err);
            reject({
              error: -1,
            });
            AppDispatcher.dispatch({
              type: CCAPIEvents.TRACE_LOG_GET_NG,
              data: {
                error: -1,
              }
            });
          } else {
            if (res.body.error !== 0) {
              reject(res.body);
              AppDispatcher.dispatch({
                type: CCAPIEvents.TRACE_LOG_GET_NG,
                ...res.body
              });
            } else {
              resolve({
                roomId: roomId,
                ...res.body.trace
              });
              AppDispatcher.dispatch({
                type: CCAPIEvents.TRACE_LOG_GET_OK,
                data: res.body,
              });
            }
          }
        });
    });
  },
};
export default Action;
