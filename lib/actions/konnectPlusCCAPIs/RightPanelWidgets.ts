import request from 'superagent';

import CCAPIEvents from '../../constants/CC_APIEvents';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

export interface RightPanelWidgetRequest {
  id: string,
  operation: string,
  order: number,
  title: string,
  url: string,
  iconType: string,
  iconFileId: string
}

export interface putRightPanelWidgetsApiParams {
  widgets: RightPanelWidgetRequest[]
}

const url = '/plusccapi/rightPanelWidgets';
const antiCsrfToken = Utils.getAntiCSRFToken();
const getSettingOk = CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK;
const getSettingNg = CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG;
const putSettingOk = CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_OK;
const putSettingNg = CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_NG;

const RightPanelWidgetsAction = {
  getRightPanelWidgetsSettingsTemplate() {
    request
      .get(APIRoot.get() + url)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        if (err || res.error) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: getSettingNg,
            error: res?.error?.status || -1,
            data: {
              error: -1
            }
          });
        } else {
          const error = res.body.error;
          if (error === 0) {
            AppDispatcher.dispatch({
              type: getSettingOk,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: getSettingNg,
              data: {
                error,
              }
            });
          }
        }
      });
  },

  putRightPanelWidgetsSettingsTemplate(params: putRightPanelWidgetsApiParams) {
    request
      .put(APIRoot.get() + url)
      .withCredentials()
      .type('json')
      .send(params)
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .end((err, res) => {
        if (err || res.error) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: putSettingNg,
            error: res?.error?.status || -1,
            data: {
              error: -1
            }
          });
        } else {
          const error = res.body?.error || 0;
          if (error === 0) {
            AppDispatcher.dispatch({
              type: putSettingOk,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: putSettingNg,
              data: {
                error,
              }
            });
          }
        }
      });
  },

  deleteRightPanelWidgetsSettingsTemplate(id: string) {
    request
      .del(APIRoot.get() + `${url}/${id}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
      .end((err, res) => {
        if (err || res.error) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: getSettingNg,
            error: res?.error?.status || -1,
            data: {
              error: -1
            }
          });
        } else {
          const error = res.body?.error || 0;
          if (error === 0) {
            AppDispatcher.dispatch({
              type: getSettingOk,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: getSettingNg,
              data: {
                error,
              }
            });
          }
        }
      });
  }
    
}

export default RightPanelWidgetsAction;
