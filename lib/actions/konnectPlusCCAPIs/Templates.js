import request from 'superagent';
import CCAPIEvents from '../../constants/CC_APIEvents';
import AppDispatcher from '../../dispatcher/AppDispatcher';

import Utils from '../../utils/Utils';
import APIRoot from '../APIRoot';

const antiCsrfToken = Utils.getAntiCSRFToken();

const DEFAULT_FILE_NAME = 'templateMessages.json';

const Action = {
  get(params={}) {

    const query = {
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };

    const domainIdInUrl = encodeURIComponent(params.domainId);
    request
      .get(`${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${DEFAULT_FILE_NAME}`)
      .withCredentials()
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query(query)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_TEMPLATES,
            data: {
              error: res.status
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_TEMPLATES,
            error: 0,
            data: Utils.tryJSONParse(res.text)
          });
        }
      });
  },

  post(params={}) {

    const domainIdInUrl = encodeURIComponent(params.domainId);
    return new Promise((resolve, reject) => {
      const form = new FormData();
      const reqJSON = Utils.attachLastUpdateToJSONString(params.json);
      const blob = new Blob([reqJSON], {type: 'application/json'});
      form.append('file', blob, DEFAULT_FILE_NAME);
      form.append('contentType', 'application/json; charset=utf-8');
      form.append('exposeToAll', false);
      form.append('noNeedAuth', false);
      if (APIRoot.getAuthDomainId() !== null) form.append('auth_domainId', APIRoot.getAuthDomainId());
      const req = new XMLHttpRequest();
      req.open('POST', `${APIRoot.get()}/api/publicFiles/${domainIdInUrl}/${DEFAULT_FILE_NAME}`);
      req.onload = function() {
        if (req.status === 200) {
          const result = req.responseText;
          const data = Utils.tryJSONParse(result);
          if (data.error === 0){
            resolve(data);
          } else {
            reject(data);
          }
        } else {
          reject({error: -1});
        }
      };
      req.withCredentials = true;
      req.setRequestHeader('X-CSRF-Token', antiCsrfToken);
      req.send(form);
    })
    .then((data) => {
      AppDispatcher.dispatch({
        type: CCAPIEvents.PUT_TEMPLATES_OK,
        data,
        json: params.json
      });
    }, (data) => {
      AppDispatcher.dispatch({
        type: CCAPIEvents.PUT_TEMPLATES_NG,
        data
      });
    });
  }
};
export default Action;
