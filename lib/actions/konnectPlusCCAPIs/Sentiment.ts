import {Sentiment, SentimentApi, Status} from "../../openapi/pluscc"
import SentimentStore from "../../stores/apiResults/Sentiment"
import {cfg, credential} from "../../utils/AxiosUtils"
import {handleErr} from "../Utils"

// GlobalError意外でこのイベントを聞く予定がないので適当。必要になったらConstantに移動する
const hdl = handleErr('SENTIMENT_API_NG')

function api() {
  return new SentimentApi(cfg())
}

const Action = {
  async loadMessages(query: { roomId?: string, sentiment?: Sentiment, suggestedSentiment?: Sentiment, suggestedBy?: string, status?: Status[]}){
    const {roomId, sentiment, suggestedSentiment, suggestedBy, status} = query
    const res = await hdl(api().getMessages(roomId, sentiment, suggestedSentiment, suggestedBy, status?.length == 0 ? undefined : status, credential()))
    const messages = res.data.messages
    SentimentStore.loadMessagesOK(messages)
    return messages
  },

  // todo 一部失敗したらどうする？今は成功扱いになるけど
  async modifySentiments(modifies: [string, Sentiment][]) {
    const arg = modifies.map(([a, b]) => ({id: a, sentiment: b}))
    await hdl(api().modifySentiments(arg, credential()))
  },

  // todo ↑に同じ
  async approveSentiments(messageIds: string[]) {
    await hdl(api().approveSentiments(messageIds, credential()))
  },

  // todo ↑に同じ
  async rejectSentiments(messageIds: string[]) {
    await hdl(api().rejectSentiments(messageIds, credential()))
  },
}
export default Action
