'use strict';

import _ from 'lodash-compat';
import request from 'superagent';

import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import CCAPIEvents from '../../constants/CC_APIEvents';
import CCConstants from '../../constants/CC_Constants';
import APIRoot from '../APIRoot';

const antiCsrfToken = Utils.getAntiCSRFToken();

const Action = {

    get(param = {}) {
    const auth_domainId = param.domainId ? param.domainId : ''
    request
      .get(`${APIRoot.get()}/plusccapi/userAttributeMemos`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: auth_domainId || APIRoot.getAuthDomainId()
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_USER_ATTRIBUTE_MEMOS,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_USER_ATTRIBUTE_MEMOS,
            data: res.body
          });
        }
      });
  },

  _post(fldName, refField, questionKey=CCConstants.QUESTION_KEY_UNUSED, name=null, tpe=null, values={}) {

    let params = {
      questionKey,
      [refField ? 'refField' : '_ignore'] : refField,
      [name ? 'name' : '_ignore'] : name,
      [tpe ? 'type' : '_ignore'] : tpe,
      [!_.isEmpty(values) ? 'values' : '_ignore'] : JSON.stringify(values),
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
    };
    return new Promise((resolve, reject) => {
      request
        .post(`${APIRoot.get()}/plusccapi/userAttributeMemos/${fldName}`)
        .withCredentials()
        .type('form')
        .set({
          'X-CSRF-Token': antiCsrfToken,
          'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
        })
        .send(params)
        .end((err, res) => {
          if (err) {
            reject(err);
          } else {
            resolve(res);
          }
        });
    });
  },

  post(fldName, refField, questionKey=CCConstants.QUESTION_KEY_UNUSED, name=null, tpe=null, values={}) {
    Action._post(fldName, refField, questionKey, name, tpe, values)
          .then(
            (res) => {
              if (res.body.error !== 0){
                AppDispatcher.dispatch({
                  type: CCAPIEvents.POST_USER_ATTRIBUTE_MEMO_NG,
                  data: res.body
                });
              } else {
                AppDispatcher.dispatch({
                  type: CCAPIEvents.POST_USER_ATTRIBUTE_MEMO_OK,
                  data: res.body
                });
              }
            },
            (err) => {
              Utils.logoutIfneccessary(err);
              AppDispatcher.dispatch({
                type: CCAPIEvents.POST_USER_ATTRIBUTE_MEMO_NG,
                data: {
                  error: -1
                }
              });
            }
          );
  },

  del(fldName) {
    request
      .del(`${APIRoot.get()}/plusccapi/userAttributeMemos/${fldName}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .query({
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId()
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.DELETE_USER_ATTRIBUTE_MEMO_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error !== 0){
            AppDispatcher.dispatch({
              type: CCAPIEvents.DELETE_USER_ATTRIBUTE_MEMO_NG,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: CCAPIEvents.DELETE_USER_ATTRIBUTE_MEMO_OK,
              data: res.body
            });
          }
        }
      });
  }
};
export default Action;
