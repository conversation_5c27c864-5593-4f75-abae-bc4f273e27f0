import _ from 'lodash-compat';
import request from 'superagent';

import AppDispatcher from '../../dispatcher/AppDispatcher';
import Utils from '../../utils/Utils';
import CCAPIEvents from '../../constants/CC_APIEvents';
import APIRoot from '../APIRoot';
import PreferencesStore, {PREFERENCE_TYPES} from '../../stores/apiResults/Preferences';

const antiCsrfToken = Utils.getAntiCSRFToken();
const DEFAULT_DATE = 'Thu, 01 Jun 1970 00:00:00 GMT';

const isExists = (val) => {
  return !_.isUndefined(val) && !_.isNull(val);
};

const Action = {

  readByOperator(domainId){
    request
      .get(`${APIRoot.get()}/plusccapi/operatorAllowedSettings/${domainId}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': DEFAULT_DATE,
      })
      .query({
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_PREFERENCES,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_PREFERENCES,
            data: {
              error:res.body.error,
              preference: res.body.plusccPreference
            }
          });
        }
      });
  },

  get(domainId){
    if (Utils.isOperatorAccess()){
      return Action.readByOperator(domainId);
    }
    request
      .get(`${APIRoot.get()}/plusccapi/preferences/${domainId}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': DEFAULT_DATE,
      })
      .query({
        [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
      })
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_PREFERENCES,
            data: {
              error: -1
            }
          });
        } else {
          AppDispatcher.dispatch({
            type: CCAPIEvents.GET_PREFERENCES,
            data: res.body
          });
        }
      });
  },

  put(domainId, preferences = {}, callback) {
    let data = {
      _method: 'put',
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
    };

    const currentData = PreferencesStore.data().preferences;
    const isChanged = (key, type) => {

      if (type === 'array') {
        return (currentData[key] || []).joins(',') !== preferences[key].join(',');
      }
      if (type === 'number') {
        return Number(currentData[key]) !== Number(preferences[key]);
      }
      if (type === 'bool') {
        return Boolean(currentData[key]) !== Boolean(preferences[key]);
      }
      return currentData[key] !== preferences[key];
    }

    _.each(_.keys(preferences), (key) => {
      // LineWorksのアクセストークンの更新は、値が変更されているかのチェックを行う必要なく常にサーバーに送るでOK MBA-14475
      const lineworksAccessTokenParams = [
        'lineworksAccessTokenForApi2',
        'lineworksAccessTokenExpireForApi2',
        'lineworksAccessTokenUpdateDateForApi2',
      ];

      if (lineworksAccessTokenParams.includes(key)) {
        // 変換なし
        data[key] = preferences[key];
      } else if (key.startsWith('param_') || PREFERENCE_TYPES[key] === 'string') {
        if (isExists(preferences[key]) && isChanged(key, 'string')) {
          data[key] = preferences[key];
        }
      } else if (PREFERENCE_TYPES[key] === 'array') {
        if (isExists(preferences[key])) {
          if (preferences[key].join && (currentData[key] || []).join(',') !== preferences[key].join(',')) {
            data[key] = preferences[key].join(',');
          } else {
            if ((currentData[key] || []).join(',') !== [preferences[key]].join(',')) {
              data[key] = preferences[key];
            }
          }
        }
      } else if (PREFERENCE_TYPES[key] === 'number') {
        if (!_.isNaN(Number(preferences[key])) && isChanged(key, 'number')){
          data[key] = Number(preferences[key]);
        }
      } else if (PREFERENCE_TYPES[key] === 'boolean') {
        if (_.isBoolean(preferences[key]) && isChanged(key, 'bool')) {
           data[key] = preferences[key] ? 'true': 'false';
        }
      }
    });
    data['param_lastUpdate'] = Date.now();

    request
      .post(`${APIRoot.get()}/plusccapi/preferences/${domainId}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': DEFAULT_DATE,
      })
      .send(data)
      .end((err, res) => {
        let result = null;
        let success = false;

        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.PUT_PREFERENCES_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error !== 0){
            AppDispatcher.dispatch({
              type: CCAPIEvents.PUT_PREFERENCES_NG,
              data: res.body
            });
            success = false;
          } else {
            AppDispatcher.dispatch({
              type: CCAPIEvents.PUT_PREFERENCES_OK,
              data: res.body
            });
            success = true;
          }
          result = res.body;
        }

        if(_.isFunction(callback)) {
          callback(success, result);
        }
      });
  },

  putToken(domainId, token, refreshToken, idx) {
    const tokenKey = `lineAccessToken${idx}_token`;
    const refreshTokenKey = `lineAccessToken${idx}_refreshToken`;
    const data = {
      _method: 'put',
      [tokenKey]: token,
      [refreshTokenKey]: refreshToken,
      [APIRoot.getAuthDomainId() !== null ? 'auth_domainId' : '_ignore']: APIRoot.getAuthDomainId(),
    };
    request
      .post(`${APIRoot.get()}/plusccapi/preferences/${domainId}`)
      .withCredentials()
      .type('form')
      .set({
        'X-CSRF-Token': antiCsrfToken,
        'If-Modified-Since': 'Thu, 01 Jun 1970 00:00:00 GMT',
      })
      .send(data)
      .end((err, res) => {
        if (err) {
          Utils.logoutIfneccessary(err);
          AppDispatcher.dispatch({
            type: CCAPIEvents.PUT_PREFERENCES_NG,
            data: {
              error: -1
            }
          });
        } else {
          if (res.body.error !== 0){
            AppDispatcher.dispatch({
              type: CCAPIEvents.PUT_PREFERENCES_NG,
              data: res.body
            });
          } else {
            AppDispatcher.dispatch({
              type: CCAPIEvents.PUT_PREFERENCES_OK,
              data: res.body
            });
          }
        }
      });
  }

};
export default Action;
