'use strict';

import { AppDispatcherAction } from '../../types';

// export interface RightPanelWidgetsSettingObj {
//   id: string,
//   domainId: string,
//   order: number,
//   title: string,
//   url: string,
//   iconType: 'default' | 'custom',
//   iconFileId: string,
//   updatedAt: string,
// }

export interface RightPanelWidgetsSettingObj {
  id: string;
  domainId: string;
  order: number;
  title: string;
  url: string;
  iconType: string;
  iconFileId: string;
  updatedAt: string | number;
  operation?: 'insert' | 'update' | 'delete';
}

export interface Connector {
  id: string;
  domainId: string;
  order: number;
  title: string;
  url: string;
  iconType: string;
  iconFileId: string;
  updatedAt: string | number;
  operation?: 'insert' | 'update' | 'delete';
}

export interface RightPanelWidgetsAppDispatcherAction extends AppDispatcherAction {
  data: {
    rightPanelWidgets?: Connector[];
    connector?: Connector;
    widgets?: Connector[];
    operation?: 'insert' | 'update' | 'delete';
    error?: number;
  };
}
