// 'use strict';

// import {EventEmitter} from 'events';
// import assign from 'object-assign';
// import AppDispatcher from '../../../dispatcher/AppDispatcher';
// import AppConstants from '../../../constants/AppConstants';
// import CCAPIEvents from '../../../constants/CC_APIEvents';
// import {RightPanelWidgetsAppDispatcherAction,RightPanelWidgetsSettingObj} from './types';

// const StoreEvents = AppConstants.StoreEvents;
// const CHANGE_EVENT = StoreEvents.CHANGE;


// let initObjguest: RightPanelWidgetsSettingObj = {
//   id: '',
//   domainId: '',
//   order: 1,
//   title: '',
//   url: '',
//   iconType: 'default',
//   iconFileId: '',
//   updatedAt: '',
// }

// let rightPanelWidgetsSettingsData: RightPanelWidgetsSettingObj | any = {...initObjguest}

// export const permits = [
//   { key: 'loginPermitLevels0', labelKey: "common_label_permitlevel_domain_admin"},
//   { key: 'loginPermitLevels1', labelKey: "common_label_permitlevel_supervisor"},
//   { key: 'loginPermitLevels2', labelKey: "common_label_permitlevel_content_operator"},
//   { key: 'loginPermitLevels9', labelKey: "common_label_permitlevel_operator"},
// ]

// export const RightPanelWidgetsSettings = assign({}, EventEmitter.prototype, {
//   emitChange() {
//     this.emit(CHANGE_EVENT);
//   },
//   addChangeListener(callback) {
//     this.on(CHANGE_EVENT, callback);
//   },
//   removeChangeListener(callback) {
//     this.removeListener(CHANGE_EVENT, callback);
//   },
//   initData() {
//     rightPanelWidgetsSettingsData = {...initObjguest}
//   },
//   data(): RightPanelWidgetsSettingObj | any {
//     return rightPanelWidgetsSettingsData;
//   }
// });

// RightPanelWidgetsSettings.dispatchToken = AppDispatcher.register((action: RightPanelWidgetsAppDispatcherAction) => {
//   if (RightPanelWidgetsSettings.listeners(CHANGE_EVENT).length < 1) {return false; }
//   if (!action.data) { return false; }
//   switch (action.type) {
    
//     case CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK:
//       RightPanelWidgetsSettings.initData()
      
//       // GETリクエストの結果の場合
//       if (action.data.connector) {
//         rightPanelWidgetsSettingsData.id = action.data.connector.id || ''
//         rightPanelWidgetsSettingsData.domainId = action.data.connector.domainId || ''
//         rightPanelWidgetsSettingsData.order = action.data.connector.order || 1
//         rightPanelWidgetsSettingsData.title = action.data.connector.title || ''
//         rightPanelWidgetsSettingsData.url = action.data.connector.url || ''
//         rightPanelWidgetsSettingsData.iconType = action.data.connector.iconType || 'default'
//         rightPanelWidgetsSettingsData.iconFileId = action.data.connector.iconFileId || ''
//         rightPanelWidgetsSettingsData.updatedAt = action.data.connector.updatedAt || ''
        
//         // 正常なGETデータの場合はsuccessフラグをfalseに
//         rightPanelWidgetsSettingsData.success = false;
//         rightPanelWidgetsSettingsData.error = 0;
//       }
      
//       // 複数のwidgetsの場合（将来の拡張用）
//       if (action.data.widgets && action.data.widgets.length > 0) {
//         // 最初のwidgetを使用（現在の単一widget仕様に合わせる）
//         const connector = action.data.widgets[0];
//         rightPanelWidgetsSettingsData.id = connector.id || ''
//         rightPanelWidgetsSettingsData.domainId = connector.domainId || ''
//         rightPanelWidgetsSettingsData.order = connector.order || 1
//         rightPanelWidgetsSettingsData.title = connector.title || ''
//         rightPanelWidgetsSettingsData.url = connector.url || ''
//         rightPanelWidgetsSettingsData.iconType = connector.iconType || 'default'
//         rightPanelWidgetsSettingsData.iconFileId = connector.iconFileId || ''
//         rightPanelWidgetsSettingsData.updatedAt = connector.updatedAt || ''
        
//         // 正常なGETデータの場合はsuccessフラグをfalseに
//         rightPanelWidgetsSettingsData.success = false;
//         rightPanelWidgetsSettingsData.error = 0;
//       }
      
//       // データが全く無い場合
//       if (!action.data.connector && (!action.data.widgets || action.data.widgets.length === 0)) {
//         rightPanelWidgetsSettingsData.success = false;
//         rightPanelWidgetsSettingsData.error = 0;
//       }
      
//       console.log('GET_OK: Updated store data:', rightPanelWidgetsSettingsData); // デバッグ用
//       RightPanelWidgetsSettings.emitChange();
//       break;
      
//     case CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_NG:
//       RightPanelWidgetsSettings.initData()
//       rightPanelWidgetsSettingsData = {
//         ...rightPanelWidgetsSettingsData,
//         error: action.data.error || -1,
//         success: false
//       };
//       RightPanelWidgetsSettings.emitChange();
//       break;
      
//     case CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_OK:
//       // PUTリクエストの成功
//       rightPanelWidgetsSettingsData = {
//         ...rightPanelWidgetsSettingsData,
//         error: 0,
//         success: true
//       };
//       console.log('PUT_OK: Success flag set:', rightPanelWidgetsSettingsData); // デバッグ用
//       RightPanelWidgetsSettings.emitChange();
//       break;
      
//     case CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_NG:
//       // PUTリクエストの失敗
//       rightPanelWidgetsSettingsData = {
//         ...rightPanelWidgetsSettingsData,
//         error: action.data.error || -1,
//         success: false
//       };
//       RightPanelWidgetsSettings.emitChange();
//       break;
      
//     default:
//       break;
//   }
// });
'use strict';

import { EventEmitter } from 'events';
import assign from 'object-assign';
import AppDispatcher from '../../../dispatcher/AppDispatcher';
import AppConstants from '../../../constants/AppConstants';
import CCAPIEvents from '../../../constants/CC_APIEvents';
import { RightPanelWidgetsAppDispatcherAction, RightPanelWidgetsSettingObj } from './types';

const StoreEvents = AppConstants.StoreEvents;
const CHANGE_EVENT = StoreEvents.CHANGE;

const MAX_WIDGETS = 5;

let rightPanelWidgetsSettingsData: RightPanelWidgetsSettingObj[] = [];

let errorCode: number = 0;
let successFlag: boolean = false;

function reorderWidgets() {
  rightPanelWidgetsSettingsData = rightPanelWidgetsSettingsData
    .filter(w => w.operation !== 'delete')
    .sort((a, b) => a.order - b.order)
    .map((widget, idx) => ({ ...widget, order: idx + 1 }));
}

export const RightPanelWidgetsSettings = assign({}, EventEmitter.prototype, {
  emitChange() {
    this.emit(CHANGE_EVENT);
  },
  addChangeListener(callback: () => void) {
    this.on(CHANGE_EVENT, callback);
  },
  removeChangeListener(callback: () => void) {
    this.removeListener(CHANGE_EVENT, callback);
  },
  initData() {
    rightPanelWidgetsSettingsData = [];
    errorCode = 0;
    successFlag = false;
  },
  data(): { widgets: RightPanelWidgetsSettingObj[]; error: number; success: boolean } {
    return {
      widgets: rightPanelWidgetsSettingsData,
      error: errorCode,
      success: successFlag,
    };
  },
});

RightPanelWidgetsSettings.dispatchToken = AppDispatcher.register((action: RightPanelWidgetsAppDispatcherAction) => {
  if (RightPanelWidgetsSettings.listeners(CHANGE_EVENT).length < 1) return false;
  if (!action.data) return false;

  switch (action.type) {
    case CCAPIEvents.GET_RIGHT_PANEL_WIDGETS_SETTINGS_OK:
      RightPanelWidgetsSettings.initData();

      if (Array.isArray(action.data.rightPanelWidgets) && action.data.rightPanelWidgets.length > 0) {
        rightPanelWidgetsSettingsData = action.data.rightPanelWidgets.map((widget: any) => ({
          id: widget.id || '',
          domainId: widget.domainId || '',
          order: widget.order || 1,
          title: widget.title || '',
          url: widget.url || '',
          iconType: widget.iconType || 'default',
          iconFileId: widget.iconFileId || '',
          updatedAt: widget.updatedAt || '',
          operation: 'update',
        }));
      } else {
        rightPanelWidgetsSettingsData = [];
      }

      errorCode = 0;
      successFlag = true;

      reorderWidgets();

      RightPanelWidgetsSettings.emitChange();
      break;

    case CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_OK:
      errorCode = 0;
      successFlag = true;
      RightPanelWidgetsSettings.emitChange();
      break;

    case CCAPIEvents.PUT_RIGHT_PANEL_WIDGETS_SETTINGS_NG:
      errorCode = action.data.error || -1;
      successFlag = false;
      RightPanelWidgetsSettings.emitChange();
      break;

    case 'ADD_WIDGET': {
      const activeCount = rightPanelWidgetsSettingsData.filter(w => w.operation !== 'delete').length;
      if (activeCount >= MAX_WIDGETS) break;

      const newWidget: RightPanelWidgetsSettingObj = {
        id: 'temp_' + Date.now(),
        domainId: '',
        order: rightPanelWidgetsSettingsData.length + 1,
        title: '',
        url: '',
        iconType: 'default',
        iconFileId: '',
        updatedAt: '',
        operation: 'insert',
      };
      rightPanelWidgetsSettingsData.push(newWidget);
      reorderWidgets();
      RightPanelWidgetsSettings.emitChange();
      break;
    }

    case 'DELETE_WIDGET': {
      const index = rightPanelWidgetsSettingsData.findIndex(w => w.id === action.data.id);
      if (index === -1) break;

      if (rightPanelWidgetsSettingsData[index].operation === 'insert') {
        rightPanelWidgetsSettingsData.splice(index, 1);
      } else {
        rightPanelWidgetsSettingsData[index].operation = 'delete';
      }
      reorderWidgets();
      RightPanelWidgetsSettings.emitChange();
      break;
    }

    case 'MOVE_WIDGET_UP': {
      const index = rightPanelWidgetsSettingsData.findIndex(w => w.id === action.data.id);
      if (index <= 0) break; // 先頭は移動不可

      [rightPanelWidgetsSettingsData[index - 1], rightPanelWidgetsSettingsData[index]] =
        [rightPanelWidgetsSettingsData[index], rightPanelWidgetsSettingsData[index - 1]];

      reorderWidgets();
      RightPanelWidgetsSettings.emitChange();
      break;
    }

    default:
      break;
  }
});
