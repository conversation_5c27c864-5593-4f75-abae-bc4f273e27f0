import React from 'react';
import { <PERSON>sed<PERSON>utton, Snackbar, TextField, Paper } from 'material-ui';
import ProgressOverlay from '../../../../common/ProgressOverlay';
import FormCss from '../../../../common/FormCss';
import KonnectAPIActionCreator from '../../../../../actions/KonnectAPIActionCreator';
import PermissionStore from '../../../../../stores/PermissionStore';
import { ja } from '../../../../../constants/lang/ja';
import CommonStyle from '../../../../themes/CommonStyle';
import IESelectField from '../../../../common/ieCompat/IESelectField';
import APIRoot from '../../../../../actions/APIRoot';
import { RightPanelWidgetsSettings } from '../../../../../stores/apiResults/RightPanelWidgetsSettings';
import RightPanelWidgetsAction from '../../../../../actions/konnectPlusCCAPIs/RightPanelWidgetsAction';
import PublicFile from '../../../../../stores/apiResults/PublicFile';

// Constants
const PERMISSION_PATH = 'callcenterOperationMiscRightPanelWidgetsSetting';
const MAX_WIDGETS = 5;
const IMAGE_VALIDATION_CONFIG = {
  maxSizeKB: 10000,
  maxWidth: 500,
  maxHeight: 500,
};

const FIELD_KEY = {
  IFRAME_TITLE: 'iframeTitle',
  IFRAME_ICON: 'iframeIcon',
  IFRAME_URL: 'iframeUrl',
} as const;

const IMAGE_TYPES = [
  'DEFAULT',
  'UPLOAD',
] as const;

// Types
interface IframeItem {
  key: number;
  id: string;
  title: string;
  icon: string;
  url: string;
  titleError?: string;
  iconError?: string;
  urlError?: string;
}

interface ValidationError {
  [key: string]: string;
}

interface State {
  editMode: boolean;
  snackMessage: string;
  isLoading: boolean;
  canAction: boolean;
  iframes: IframeItem[];
  inputFileStateHandler?: () => void;
  processingPublicFileName?: string;
  progress?: boolean;
  // 動的な画像関連のプロパティ
  [key: string]: any;
}

const styles = {
  InlineImage: {
    height: '48px',
    width: '48px',
    marginLeft: '10px',
    backgroundColor: 'rgba(0, 0, 0, .1)',
    fontSize: '10px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
}

class RightPanelWidgets extends React.Component<any, State> {
  _isMounted: boolean = false;
  timeoutToken: any = null;
  snackRef: any = null;
  inputFileStateHandlerTimer: any = null;
  
  // 動的なrefをサポート
  [key: string]: any;

  constructor(props) {
    super(props);
    
    // Bind methods
    this.onPermissionChange = this.onPermissionChange.bind(this);
    this.inputFileStateHandler = this.inputFileStateHandler.bind(this);
    this.renderImageInputForIframe = this.renderImageInputForIframe.bind(this);
    this.onStoreChange = this.onStoreChange.bind(this);
    this.safeSetState = this.safeSetState.bind(this);
    this.onPublicFileChange = this.onPublicFileChange.bind(this);
    
    // Initialize state
    this.state = this.getInitialState();
  }

  /**
   * 初期stateを取得
   */
  getInitialState(): State {
    const initialIframe = this.createInitialIframe(0);
    return {
      editMode: false,
      snackMessage: '',
      isLoading: false,
      canAction: PermissionStore.canAction(PERMISSION_PATH),
      iframes: [initialIframe],
      // 初期iframeの画像state
      image_header_icon_0Type: 'DEFAULT',
      image_header_icon_0PreviousType: null,
      image_header_icon_0RefreshTime: Date.now(),
      inputFileStateHandler: null,
      processingPublicFileName: '',
      progress: false,
    };
  }

  /**
   * 初期iframeアイテムを作成
   */
  createInitialIframe(key: number): IframeItem {
    return {
      key,
      id: '',
      title: '',
      icon: '',
      url: '',
      titleError: '',
      iconError: '',
      urlError: '',
    };
  }

  componentDidMount() {
    this._isMounted = true;
    
    // Store変更リスナーを設定
    PermissionStore.addChangeListener(this.onPermissionChange);
    RightPanelWidgetsSettings.addChangeListener(this.onStoreChange);
    PublicFile.addChangeListener(this.onPublicFileChange);
    window.addEventListener('focus', this.inputFileStateHandler);
    
    // 次のティックでデータ取得を開始
    this.scheduleInitialLoad();
  }

  /**
   * 初期データ読み込みをスケジュール
   */
  scheduleInitialLoad() {
    setTimeout(() => {
      if (this._isMounted) {
        this.safeSetState({ isLoading: true }, () => {
          RightPanelWidgetsAction.getRightPanelWidgetsSettingsTemplate();
        });
      }
    }, 0);
  }

  componentDidUpdate() {
    if (this.state.snackMessage && this.snackRef && this._isMounted) {
      this.snackRef.show();
      if (!this.timeoutToken) {
        this.timeoutToken = setTimeout(() => {
          if (this.snackRef && this.snackRef._bindClickAway) {
            this.snackRef._bindClickAway();
          }
          this.timeoutToken = null;
        }, 100)
      }
    }
  }

  componentWillUnmount() {
    this._isMounted = false;
    
    // Store変更リスナーを削除
    PermissionStore.removeChangeListener(this.onPermissionChange);
    RightPanelWidgetsSettings.removeChangeListener(this.onStoreChange);
    PublicFile.removeChangeListener(this.onPublicFileChange);
    window.removeEventListener('focus', this.inputFileStateHandler);
    
    // タイマーをクリア
    this.clearTimeouts();
    
    // refをクリア
    this.clearRefs();
  }

  /**
   * 全てのタイマーをクリア
   */
  clearTimeouts() {
    if (this.timeoutToken) {
      clearTimeout(this.timeoutToken);
      this.timeoutToken = null;
    }
    if (this.inputFileStateHandlerTimer) {
      clearTimeout(this.inputFileStateHandlerTimer);
      this.inputFileStateHandlerTimer = null;
    }
  }

  /**
   * 全てのrefをクリア
   */
  clearRefs() {
    Object.keys(this).forEach(key => {
      if (key.includes('hidden_fileInput_') && key.includes('Ref')) {
        this[key] = null;
      }
    });
    this.snackRef = null;
  }

  /**
   * PublicFileの変更を処理
   */
  onPublicFileChange() {
    // ファイルアップロード完了時の処理
    this.state.iframes.forEach(iframe => {
      const key = `image_header_icon_${iframe.key}`;
      const fileName = `callcenterClientResource/${key}`;
      const apiResult = PublicFile.data(fileName);
      
      if (apiResult && !apiResult.postResult) {
        // アップロード完了
        this.safeSetState({
          [`${key}RefreshTime`]: Date.now(),
          processingPublicFileName: '',
          progress: false,
        });
      }
    });
  }

  /**
   * 権限変更を処理
   */
  onPermissionChange() {
    this.safeSetState({
      canAction: PermissionStore.canAction(PERMISSION_PATH)
    });
  }

  /**
   * Store変更を処理
   */
  onStoreChange() {
    const storeData = RightPanelWidgetsSettings.data();
    console.log('Store data received:', storeData);
    
    if (!this._isMounted) return;
    
    // PUT成功の場合
    if (this.isPutSuccess(storeData)) {
      this.handlePutSuccess();
      return;
    }
    
    // エラーの場合
    if (this.hasError(storeData)) {
      this.handleError();
      return;
    }
    
    // GETリクエストの正常なデータの場合
    if (this.isGetData(storeData)) {
      this.handleGetData(storeData);
    }
  }

  /**
   * PUT成功かどうかを判定
   */
  isPutSuccess(storeData: any): boolean {
    return storeData && storeData.success === true && storeData.error === 0;
  }

  /**
   * エラーがあるかどうかを判定
   */
  hasError(storeData: any): boolean {
    return storeData && storeData.error && storeData.error !== 0;
  }

  /**
   * GETデータかどうかを判定
   */
  isGetData(storeData: any): boolean {
    return storeData && (storeData.success === undefined || storeData.success === false);
  }

  /**
   * PUT成功時の処理
   */
  handlePutSuccess() {
    this.safeSetState({
      snackMessage: this.context.i18n.admin_domain_update_successful || '更新が完了しました',
      editMode: false,
      isLoading: false
    });
    
    // 最新データを再取得
    setTimeout(() => {
      if (this._isMounted) {
        RightPanelWidgetsAction.getRightPanelWidgetsSettingsTemplate();
      }
    }, 100);
  }

  /**
   * エラー時の処理
   */
  handleError() {
    this.safeSetState({
      isLoading: false,
      snackMessage: 'サーバーエラーが発生しました'
    });
  }

  /**
   * GETデータの処理
   */
  handleGetData(storeData: any) {
    const iframes = this.parseStoreDataToIframes(storeData);
    const imageStates = this.createImageStates(iframes);

    this.safeSetState({
      iframes,
      ...imageStates,
      isLoading: false,
    });
    
    console.log('Updated component state with iframes:', iframes);
  }

  /**
   * StoreデータをIframeデータに変換
   */
  parseStoreDataToIframes(storeData: any): IframeItem[] {
    if (storeData.title || storeData.url) {
      // 単一のconnectorオブジェクトの場合
      return [{
        key: 0,
        id: storeData.id || '',
        title: storeData.title || '',
        icon: storeData.iconFileId || '',
        url: storeData.url || '',

        titleError: '',
        iconError: '',
        urlError: '',
      }];
    } else {
      // データが空の場合は初期フォーム
      return [this.createInitialIframe(0)];
    }
  }

  /**
   * iframesに基づいて画像stateを作成
   */
  createImageStates(iframes: IframeItem[]): any {
    const imageStates = {};
    iframes.forEach(iframe => {
      const key = `image_header_icon_${iframe.key}`;
      imageStates[`${key}Type`] = iframe.icon ? 'UPLOAD' : 'DEFAULT';
      imageStates[`${key}RefreshTime`] = Date.now();
      imageStates[`${key}PreviousType`] = null;
    });
    return imageStates;
  }

  /**
   * 現在のドメインIDを取得
   */
  getCurrentDomainId(): string {
    return this.context.auth_domainId || this.context.domainId;
  }

  /**
   * 安全なsetState（マウント状態をチェック）
   */
  safeSetState(partialState: any, callback?: () => void) {
    if (this._isMounted) {
      this.setState(partialState, callback);
    }
  }

  /**
   * Snackbar キャンセル処理
   */
  handleCancel() {
    if (this.snackRef && this.snackRef.dismiss) {
      this.snackRef.dismiss();
    }
  }

  /**
   * ファイル入力状態のハンドラー
   */
  inputFileStateHandler() {
    const handler = this.state.inputFileStateHandler;
    if (typeof handler === 'function') {
      clearTimeout(this.inputFileStateHandlerTimer);
      this.inputFileStateHandlerTimer = setTimeout(handler, 500);
    }
  }

  /**
   * Snackbar 閉じる処理
   */
  onSnackDismiss() {
    this.safeSetState({ snackMessage: '' });
  }

  /**
   * 編集モード変更
   */
  onModeChange() {
    this.safeSetState({
      editMode: !this.state.editMode
    });
  }

  /**
   * フィールドバリデーション
   */
  validateField(key: string, value: string, showErrorOnEmpty: boolean = true): ValidationError {
    const error: ValidationError = {};
    error[key] = '';

    switch (key) {
      case FIELD_KEY.IFRAME_TITLE:
        error[key] = this.validateTitle(value, showErrorOnEmpty);
        break;
      case FIELD_KEY.IFRAME_URL:
        error[key] = this.validateUrl(value, showErrorOnEmpty);
        break;
    }
    return error;
  }

  /**
   * タイトルのバリデーション
   */
  validateTitle(value: string, showErrorOnEmpty: boolean): string {
    if (!value && showErrorOnEmpty) {
      return this.context.i18n.error_common_required || '必須項目です';
    }
    if (value && value.length > 20) {
      return 'タイトルは20文字以内で入力してください。';
    }
    return '';
  }

  /**
   * URLのバリデーション
   */
  validateUrl(value: string, showErrorOnEmpty: boolean): string {
    if (!value && showErrorOnEmpty) {
      return this.context.i18n.error_common_required || '必須項目です';
    }
    if (value && !value.startsWith('https://')) {
      return 'httpsから始まるURLを入力してください。';
    }
    if (value && !/^[a-zA-Z0-9\-._~:/?#[\]@!$&'()*+,;=%]+$/.test(value)) {
      return '半角英数字で入力してください。';
    }
    if (value && value.length > 2000) {
      return 'URLは2000文字以内で入力してください。';
    }
    return '';
  }

  /**
   * アイコンのバリデーション
   */
  validateIcon(iframe: IframeItem): string {
    const key = `image_header_icon_${iframe.key}`;
    const imageType = this.getImageType(key);
    
    if (imageType === 'UPLOAD' && !iframe.icon) {
      return this.context.i18n.error_common_required || '必須項目です';
    }
    
    return '';
  }

  /**
   * 全フィールドのバリデーション
   */
  validateAllFields(showErrorOnEmpty: boolean = true): boolean {
    let hasError = false;
    const updatedIframes = this.state.iframes.map(iframe => {
      const titleError = this.validateField(FIELD_KEY.IFRAME_TITLE, iframe.title, showErrorOnEmpty);
      const urlError = this.validateField(FIELD_KEY.IFRAME_URL, iframe.url, showErrorOnEmpty);
      const iconError = showErrorOnEmpty ? this.validateIcon(iframe) : '';

      if (titleError[FIELD_KEY.IFRAME_TITLE] || iconError || urlError[FIELD_KEY.IFRAME_URL]) {
        hasError = true;
      }

      return {
        ...iframe,
        titleError: titleError[FIELD_KEY.IFRAME_TITLE],
        iconError: iconError,
        urlError: urlError[FIELD_KEY.IFRAME_URL],
      };
    });

    this.safeSetState({ iframes: updatedIframes });
    return hasError;
  }

  /**
   * バリデーションのみ実行（UI更新なし）
   */
  validateOnly(): boolean {
    return this.state.iframes.some(iframe => {
      const titleError = this.validateField(FIELD_KEY.IFRAME_TITLE, iframe.title, true);
      const urlError = this.validateField(FIELD_KEY.IFRAME_URL, iframe.url, true);
      const iconError = this.validateIcon(iframe);

      return titleError[FIELD_KEY.IFRAME_TITLE] || iconError || urlError[FIELD_KEY.IFRAME_URL];
    });
  }

  /**
   * フォーム送信処理
   */
  onSubmit() {
    if (this.validateAllFields(true)) {
      this.safeSetState({
        snackMessage: this.context.i18n.error_common_has_field_error || 'フィールドエラーがあります'
      });
      return;
    }

    const widgets = this.buildWidgetsData();
    const params = { widgets };

    console.log('Submitting data:', params);

    this.safeSetState({ isLoading: true });
    RightPanelWidgetsAction.putRightPanelWidgetsSettingsTemplate(params);
  }

  /**
   * 送信用のウィジェットデータを構築
   */
  buildWidgetsData() {
    const validIframes = this.state.iframes.filter(iframe => 
      iframe.title.trim() && iframe.url.trim()
    );

    return validIframes.map((iframe, index) => {
      const key = `image_header_icon_${iframe.key}`;
      const imageType = this.getImageType(key);
      
      return {
        id: iframe.id || '',
        operation: iframe.id ? 'update' : 'insert',
        order: index,
        title: iframe.title.trim(),
        url: iframe.url.trim(),
        iconType: imageType === 'UPLOAD' ? 'custom' : 'default',
        iconFileId: iframe.icon || '',
      };
    });
  }

  /**
   * エラーがないかチェック
   */
  hasNoError(): boolean {
    if (!this.state.editMode) {
      return true;
    }
    
    return this.state.iframes.every(iframe => {
      const titleError = this.validateField(FIELD_KEY.IFRAME_TITLE, iframe.title, false);
      const urlError = this.validateField(FIELD_KEY.IFRAME_URL, iframe.url, false);
      const iconError = this.validateIcon(iframe);
      
      return !titleError[FIELD_KEY.IFRAME_TITLE] && !iconError && !urlError[FIELD_KEY.IFRAME_URL];
    });
  }

  /**
   * iframeフィールド変更処理
   */
  onIframeFieldChange = (key: number, field: string, value: string) => {
    const updatedIframes = this.state.iframes.map(iframe => {
      if (iframe.key === key) {
        const updatedIframe = { ...iframe, [field]: value };
        
        // リアルタイムバリデーション（必須チェックは除く）
        if (field !== 'icon') {
          const error = this.validateField(field, value, false);
          if (field === 'title') {
            updatedIframe.titleError = error[FIELD_KEY.IFRAME_TITLE];
          } else if (field === 'url') {
            updatedIframe.urlError = error[FIELD_KEY.IFRAME_URL];
          }
        }
        
        return updatedIframe;
      }
      return iframe;
    });

    this.safeSetState({ iframes: updatedIframes });
  }

  /**
   * フィールドフォーカスアウト処理
   */
  onFieldBlur = (key: number, field: string) => {
    const iframe = this.state.iframes.find(item => item.key === key);
    if (!iframe) return;

    const value = iframe[field];
    
    // フォーカスアウト時は必須チェックを含む完全バリデーション
    if (field !== 'icon') {
      const error = this.validateField(field, value, true);
      
      const updatedIframes = this.state.iframes.map(item => {
        if (item.key === key) {
          const updatedIframe = { ...item };
          if (field === 'title') {
            updatedIframe.titleError = error[FIELD_KEY.IFRAME_TITLE];
          } else if (field === 'url') {
            updatedIframe.urlError = error[FIELD_KEY.IFRAME_URL];
          }
          return updatedIframe;
        }
        return item;
      });

      this.safeSetState({ iframes: updatedIframes });
    }
  }

  /**
   * アイコンアップロード完了時の処理
   */
  onIconUploadComplete = (iframeKey: number, imageUrl: string) => {
    const updatedIframes = this.state.iframes.map(iframe => {
      if (iframe.key === iframeKey) {
        return {
          ...iframe,
          icon: imageUrl,
          iconError: '', // アップロード完了時はエラーをクリア
        };
      }
      return iframe;
    });

    this.safeSetState({ iframes: updatedIframes });
  }

  /**
   * 画像タイプを安全に取得
   */
  getImageType(key: string): 'DEFAULT' | 'UPLOAD' {
    const typeKey = `${key}Type`;
    const imageType = this.state[typeKey];
    
    // 文字列で、かつ有効な値かチェック
    if (typeof imageType === 'string' && (imageType === 'DEFAULT' || imageType === 'UPLOAD')) {
      return imageType as 'DEFAULT' | 'UPLOAD';
    }
    
    // デフォルト値を返す
    return 'DEFAULT';
  }

  /**
   * アイコン用の画像アップロード機能を各iframeに対応
   */
  renderImageInputForIframe(iframe: IframeItem, index: number) {
    const key = `image_header_icon_${iframe.key}`;
    const refreshTimeKey = `${key}RefreshTime`;
    const refreshTime = this.state[refreshTimeKey] || Date.now();
    const imageType = this.getImageType(key);

    const menuItems = [
      {payload: IMAGE_TYPES[0], text: ja.dialog.default || 'デフォルト'},
      {payload: IMAGE_TYPES[1], text: ja.dialog.upload || 'アップロード'},
    ];
    
    return (
      <div className='selectIconContainer' style={{display: 'flex', alignItems: 'center'}}>
        <div className='selectImageField'>
          <IESelectField
            labelStyle={{fontSize: '14px'}}
            menuItemStyle={{fontSize: '14px'}}
            selectedIndex={IMAGE_TYPES.indexOf(imageType)}
            menuItems={menuItems}
            onChange={this.onImageTypeChangeForIframe(iframe.key)}
            disabled={!this.state.editMode}
          />
            <input
              id={`hidden_fileInput_${key}`}
              ref={(ref) => {
                if (ref) {
                  this[`hidden_fileInput_${key}Ref`] = ref;
                }
              }}
              style={{display: 'none'}}
              type="file"
              accept='image/*'
              onChange={this.onImageFileChangeForIframe(iframe.key)}
            />
            <p style={{color: 'rgba(0, 0, 0, 0.5)',lineHeight: '1.2',fontSize: '12px', margin: '0'}}>
              {ja.rightPanelWidgets.icon_placeholder}
            </p>
        </div>
        {imageType === 'DEFAULT' &&
          <Paper style={styles.InlineImage}>
            <img
              src={`${APIRoot.getAssetsCdnURL()}/assets/images/admin/embedSample/img/image_avatar_guest.png`}
              alt="デフォルト画像"
              style={{ width: '36px', height: '36px', objectFit: 'cover' }}
            />
          </Paper>
        }
        {imageType === 'UPLOAD' &&
          <Paper style={styles.InlineImage} onClick={this.toggleFileInputDialogKeyForIframe(iframe.key)}>
            <img
              src={`${this.context.baseUrl}/api/globalPublicFiles/mst/${this.getCurrentDomainId()}/callcenterClientResource/${key}?t=${refreshTime}`}
              alt="アップロード画像"
              style={{ width: '36px', height: '36px', objectFit: 'cover' }}
            />
          </Paper>
        }
        {iframe.iconError && 
          <p style={{ color: 'red', fontSize: '12px', margin: '5px 0' }}>
            {iframe.iconError}
          </p>
        }
      </div>
    );
  }

  /**
   * 画像タイプ変更処理
   */
  onImageTypeChangeForIframe(iframeKey: number) {
    return (event, selectedIndex) => {
      const imageType = IMAGE_TYPES[selectedIndex];
      const key = `image_header_icon_${iframeKey}`;
      const currentImageType = this.getImageType(key);
      
      if (imageType === 'UPLOAD') {
        this.openFileDialog(iframeKey);
      } else {
        this.safeSetState({
          [`${key}Type`]: imageType,
          [`${key}PreviousType`]: currentImageType,
        });
        
        // デフォルト選択時はアイコンフィールドをクリア
        this.onIframeFieldChange(iframeKey, 'icon', '');
      }
    }
  }

  /**
   * ファイルダイアログ開く処理（関数型）
   */
  toggleFileInputDialogKeyForIframe(iframeKey: number) {
    return () => {
      this.openFileDialog(iframeKey);
    }
  }

  /**
   * ファイルダイアログを開く
   */
  openFileDialog(iframeKey: number) {
    const key = `image_header_icon_${iframeKey}`;
    
    // DOMが完全にマウントされるまで待機
    setTimeout(() => {
      const hiddenFileInput = this[`hidden_fileInput_${key}Ref`];
      
      if (!hiddenFileInput) {
        console.error(`File input ref not found for key: ${key}`);
        return;
      }
      
      this.setupFileDialogHandler(iframeKey, key, hiddenFileInput);
      this.triggerFileInput(hiddenFileInput, key);
    }, 10);
  }

  /**
   * ファイルダイアログハンドラーをセットアップ
   */
  setupFileDialogHandler(iframeKey: number, key: string, hiddenFileInput: any) {
    const currentImageType = this.getImageType(key);
    
    this.safeSetState({
      [`${key}Type`]: 'UPLOAD',
      [`${key}PreviousType`]: currentImageType,
      inputFileStateHandler: () => {
        if (!hiddenFileInput.value) {
          const previousType = this.state[`${key}PreviousType`] || 'DEFAULT';
          this.safeSetState({
            [`${key}Type`]: previousType,
            [`${key}PreviousType`]: null,
            inputFileStateHandler: null,
            progress: false,
          }, () => {
            this.resetHiddenFileInputForIframe(iframeKey);
          });
        }
      },
      progress: true,
    });
  }

  /**
   * ファイル入力をトリガー
   */
  triggerFileInput(hiddenFileInput: any, key: string) {
    try {
      hiddenFileInput.click();
    } catch (error) {
      console.error('Error clicking file input:', error);
      const previousType = this.state[`${key}PreviousType`] || 'DEFAULT';
      this.safeSetState({
        [`${key}Type`]: previousType,
        progress: false,
      });
    }
  }

  /**
   * 画像ファイル変更処理
   */
  onImageFileChangeForIframe(iframeKey: number) {
    return () => {
      clearTimeout(this.inputFileStateHandlerTimer);
      this.processFileUpload(iframeKey);
    }
  }

  /**
   * ファイルアップロード処理
   */
  processFileUpload(iframeKey: number) {
    const key = `image_header_icon_${iframeKey}`;
    const files = this.getFilesFromHiddenFileInputForIframe(iframeKey);
    
    if (!files || !files[0]) return;

    clearTimeout(this.inputFileStateHandlerTimer);
    const fileName = `callcenterClientResource/${key}`;
    
    this.safeSetState({
      processingPublicFileName: fileName,
    }, () => {
      this.uploadFile(files[0], fileName, iframeKey, key);
    });
  }

  /**
   * ファイルアップロード実行
   */
  uploadFile(file: File, fileName: string, iframeKey: number, key: string) {
    const contentType = file.type || 'image/png';
    
    KonnectAPIActionCreator.postPublicFile(
      this.getCurrentDomainId(), 
      file, 
      fileName, 
      contentType, 
      true/*exposeToAll*/, 
      true/*noNeedAuth*/, 
      false/*isAnonymous*/
    );
    
    // アップロード後の処理をシミュレート
    setTimeout(() => {
      const imageUrl = `${this.context.baseUrl}/api/globalPublicFiles/mst/${this.getCurrentDomainId()}/${fileName}`;
      this.onIconUploadComplete(iframeKey, imageUrl);
      
      // リフレッシュタイムを更新
      this.safeSetState({
        [`${key}RefreshTime`]: Date.now(),
        processingPublicFileName: '',
        progress: false,
      });
    }, 1000);
  }

  /**
   * 隠しファイル入力からファイルを取得
   */
  getFilesFromHiddenFileInputForIframe(iframeKey: number): FileList | null {
    const key = `image_header_icon_${iframeKey}`;
    const hiddenFileInput = this[`hidden_fileInput_${key}Ref`];
    return hiddenFileInput ? hiddenFileInput.files : null;
  }

  /**
   * 隠しファイル入力をリセット
   */
  resetHiddenFileInputForIframe(iframeKey: number) {
    const key = `image_header_icon_${iframeKey}`;
    const hiddenFileInput = this[`hidden_fileInput_${key}Ref`];
    if (hiddenFileInput) {
      hiddenFileInput.value = '';
    }
  }

  /**
   * フィールド削除処理
   */
  removeField(key: number) {
    if (this.state.iframes.length <= 1) {
      // 1つしかない場合は値を未設定の状態にリセット
      const resetIframe = this.createInitialIframe(0);
      const imageKey = 'image_header_icon_0';
      
      this.safeSetState({ 
        iframes: [resetIframe],
        [`${imageKey}Type`]: 'DEFAULT',
        [`${imageKey}PreviousType`]: null,
        [`${imageKey}RefreshTime`]: Date.now(),
      });
    } else {
      // 複数ある場合は通常の削除処理
      const iframes = this.state.iframes.filter(iframe => iframe.key !== key);
      
      // 削除されるアイテムの画像stateも削除
      const imageKey = `image_header_icon_${key}`;
      const newState = { iframes };
      delete newState[`${imageKey}Type`];
      delete newState[`${imageKey}PreviousType`];
      delete newState[`${imageKey}RefreshTime`];
      
      this.safeSetState(newState);
    }
  }

  /**
   * フィールド追加処理
   */
  addField() {
    const iframes = this.state.iframes;
    const newKey = iframes.length > 0 ? Math.max(...iframes.map(iframe => iframe.key)) + 1 : 0;
    
    const newIframe = this.createInitialIframe(newKey);
    
    // 新しいアイテム用の画像state初期化
    const imageKey = `image_header_icon_${newKey}`;
    const newImageState = {
      [`${imageKey}Type`]: 'DEFAULT',
      [`${imageKey}PreviousType`]: null,
      [`${imageKey}RefreshTime`]: Date.now(),
    };
    
    const updatedIframes = [...iframes, newIframe];
    this.safeSetState({ 
      iframes: updatedIframes,
      ...newImageState
    });
  }

  /**
   * アイテム移動処理
   */
  moveItem = (key: number, direction: 'up') => {
    const iframes = [...this.state.iframes];
    const currentIndex = iframes.findIndex(iframe => iframe.key === key);
    
    if (currentIndex === -1) return;
    
    if (direction === 'up' && currentIndex > 0) {
      const newIndex = currentIndex - 1;
      [iframes[currentIndex], iframes[newIndex]] = [iframes[newIndex], iframes[currentIndex]];
      this.safeSetState({ iframes });
    }
  }

  /**
   * iframeアイテムをレンダー
   */
  renderIframeItem(iframe: IframeItem, index: number) {
    return (
      <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '40px'}} className='formIframeArea' key={iframe.key}>
        <div style={{ flex: 1 }}>
          {this.renderTitleField(iframe)}
          {this.renderIconField(iframe, index)}
          {this.renderUrlField(iframe)}
        </div>
        <div style={{ marginLeft: '20px', alignSelf: 'flex-end' }}>
          {this.renderActionButtons(iframe, index)}
        </div>
      </div>
    );
  }

  /**
   * タイトルフィールドをレンダー
   */
  renderTitleField(iframe: IframeItem) {
    return (
      <div className='fieldRow' style={{ marginBottom: '20px' }}>
        <p className='fieldLabel fieldLabel_rightPanel required'>{ja.rightPanelWidgets.title}</p>
        <div>
          <TextField
            style={{ width: '600px' }}
            value={iframe.title}
            disabled={!this.state.editMode}
            errorText={iframe.titleError || ''}
            onChange={(e) => this.onIframeFieldChange(iframe.key, 'title', e.target.value)}
            onBlur={() => this.onFieldBlur(iframe.key, 'title')}
          />
          <p style={{color: 'rgba(0, 0, 0, 0.5)',lineHeight: '1.2',fontSize: '12px', margin: '0'}}>
            {ja.rightPanelWidgets.title_placeholder}
          </p>
        </div>
      </div>
    );
  }

  /**
   * アイコンフィールドをレンダー
   */
  renderIconField(iframe: IframeItem, index: number) {
    return (
      <div className='fieldRow' style={{ marginBottom: '20px' }}>
        <p className='fieldLabel fieldLabel_rightPanel required'>{ja.rightPanelWidgets.icon}</p>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {this.renderImageInputForIframe(iframe, index)}
        </div>
      </div>
    );
  }

  /**
   * URLフィールドをレンダー
   */
  renderUrlField(iframe: IframeItem) {
    return (
      <div className='fieldRow' style={{ marginBottom: '20px' }}>
        <p className='fieldLabel fieldLabel_rightPanel required'>{ja.rightPanelWidgets.url}</p>
        <div>
          <TextField
            style={{ width: '600px' }}
            value={iframe.url}
            placeholder='https://'
            disabled={!this.state.editMode}
            errorText={iframe.urlError || ''}
            onChange={(e) => this.onIframeFieldChange(iframe.key, 'url', e.target.value)}
            onBlur={() => this.onFieldBlur(iframe.key, 'url')}
          />
        </div>
      </div>
    );
  }

  /**
   * アクションボタンをレンダー
   */
  renderActionButtons(iframe: IframeItem, index: number) {
    return (
      <>
        <RaisedButton
          label={ja.button.delete}
          backgroundColor={CommonStyle.warningButtonBgColor}
          labelColor={CommonStyle.warningButtonTextColor}
          onClick={() => this.removeField(iframe.key)}
          disabled={!this.state.editMode}
          style={{ marginRight: '10px' }}
        />
        <RaisedButton
          label={ja.button.up}
          onClick={() => this.moveItem(iframe.key, 'up')}
          disabled={!this.state.editMode || index === 0}
        />
      </>
    );
  }

  render() {
    if (this.state.isLoading) {
      return <ProgressOverlay timeout={500} />;
    }
    
    const hasNoError = this.hasNoError();

    return (
      <div>
        <div style={FormCss.container}>
          <div style={FormCss.heading}>
            <div style={FormCss.headingTitle}>
              <h3>{ja.rightPanelWidgets.rightPanelWidgetsSetting}</h3>
            </div>
          </div>
          
          <div style={FormCss.content}>
            <div style={FormCss.subHeading}>
              <div style={FormCss.subHeadingTitle}>
                <h4>{ja.rightPanelWidgets.edit}</h4>
              </div>
              <div style={FormCss.rightButton}>
                <RaisedButton
                  label={this.state.editMode ? ja.button.cancel : ja.button.edit}
                  onClick={this.onModeChange.bind(this)}
                  disabled={!this.state.canAction}
                />
                <RaisedButton
                  className='submitButton'
                  label={ja.button.save}
                  secondary
                  onClick={this.onSubmit.bind(this)}
                  disabled={!(this.state.editMode && hasNoError)}
                />
              </div>
            </div>
            
            <div className='formFieldArea'>
              {this.state.iframes.map((iframe, index) => this.renderIframeItem(iframe, index))}              
              <div style={FormCss.rightButton}>
                <RaisedButton
                  style={FormCss.rightButton1st}
                  label={ja.button.add_menu}
                  onClick={() => this.addField()}
                  disabled={!this.state.editMode || this.state.iframes.length >= MAX_WIDGETS}
                />
                <RaisedButton
                  style={FormCss.rightButton1st}
                  secondary
                  label={ja.button.save}
                  onClick={this.onSubmit.bind(this)}
                  disabled={!(this.state.editMode && hasNoError)}
                />
              </div>
            </div>
          </div>
        </div>
        
        <Snackbar
          style={{ zIndex: '10' }}
          ref={(ref) => { 
            if (ref) {
              this.snackRef = ref;
            }
          }}
          message={this.state.snackMessage}
          onActionTouchTap={this.handleCancel.bind(this)}
          onDismiss={this.onSnackDismiss.bind(this)}
        />
      </div>
    );
  }
}

export default RightPanelWidgets;


