import React from 'react';
import { shallow } from 'enzyme';
import Editor from './Editor';

describe('Editor Component', () => {
  let wrapper;
  let mockSnack;

  beforeEach(() => {
    mockSnack = { show: jest.fn() };
    wrapper = shallow(<Editor />);
    wrapper.instance().snack = mockSnack;
  });

  describe('componentDidUpdate', () => {
    it('should call snack.show() when snackMessage is set', () => {
      // Initial state without snackMessage
      expect(mockSnack.show).not.toHaveBeenCalled();

      // Update state with snackMessage
      wrapper.setState({ snackMessage: 'Test message' });

      // Verify snack.show() was called
      expect(mockSnack.show).toHaveBeenCalled();
    });

    it('should not call snack.show() when snackMessage is not set', () => {
      // Initial state without snackMessage
      expect(mockSnack.show).not.toHaveBeenCalled();

      // Update state without snackMessage
      wrapper.setState({ editMode: true });

      // Verify snack.show() was not called
      expect(mockSnack.show).not.toHaveBeenCalled();
    });
  });
});