version: 2.1
orbs:
  # https://circleci.com/orbs/registry/orb/circleci/aws-s3
  # S3へのアーカイブ用のorb
  # Project Setting > Environment Variablesで
  # AWS_ACCESS_KEY_ID、AWS_SECRET_ACCESS_KEY、AWS_REGIONの登録が必要
  # 上記のキーのAWSユーザーはIAMで専用ユーザーを作成済み
  # また専用ユーザーにS3アクセスできるポリシーをアタッチ済み
  aws-s3: circleci/aws-s3@1.0.4
executors:
  test-executor:
    docker:
      - image: circleci/node:lts-browsers
        auth:
          username: $DOCKERHUB_USER
          password: $DOCKERHUB_PASSWORD
  build-executor:
    docker:
      - image: circleci/node:lts-browsers # lts -> lts-browsersに変更。openapitoolsがJVMを必要としているため
        auth:
          username: $DOCKERHUB_USER
          password: $DOCKERHUB_PASSWORD
      - image: circleci/python:2.7
        auth:
          username: $DOCKERHUB_USER
          password: $DOCKERHUB_PASSWORD
  awscli-executor:
    docker:
      - image: circleci/python:2.7
        auth:
          username: $DOCKERHUB_USER
          password: $DOCKERHUB_PASSWORD
  sonarqube-executor:
    docker:
      - image: cimg/openjdk:8.0.345-browsers
        auth:
          username: $DOCKERHUB_USER
          password: $DOCKERHUB_PASSWORD
commands:
  add_bb_user_key:
    description: "Setup Bitbucket user key"
    steps:
      # jenkins_mobilus user key ([MBA-admin]mba-admin@circle-ci)
      - add_ssh_keys:
          fingerprints:
            - "SHA256:OIaaDdM/TEbWFlkcMXnkVKF+I6uMUXIee8m67zUrK84"
  do_test:
    description: "Run test via npm test:singleRun"
    parameters:
      KARMA_TARGET:
        type: string
        default: "all"
    steps:
      - add_bb_user_key
      - run: echo Test target is << parameters.KARMA_TARGET >>
      # jenkins_mobilus user key ([MBA-admin]mba-admin@circle-ci)
      - add_ssh_keys:
          fingerprints:
            - "SHA256:OIaaDdM/TEbWFlkcMXnkVKF+I6uMUXIee8m67zUrK84"
      - checkout
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package-lock.json" }}
      - run:
          name: start karma
          # commitログに"skip test"が含まれていたらテストしない
          command: |
            commitMassage=`git log -n 1 --oneline --pretty=format:"%s"`
            if [ "$(echo $commitMassage | grep -e 'skip test')" ]; then
              echo "skip test"
            else
              KARMA_TARGET=<< parameters.KARMA_TARGET >> npm run test:singleRun
            fi
      # テスト結果はCircleCI上でJOBの詳細から確認できる
      # プランをアップグレードするとINSIGHTSが使えるっぽい
      # codecovについて
      # - https://mobi-work.slack.com/archives/CHEE4CMTJ/p1562306192027200
      # Orbは以下のエラーで使えない
      # Orb codecov/codecov@1.0.5 not loaded. To use this orb, an organization admin must opt-in to using third party orbs in Organization Security settings.
      - run:
          name: codecov
          command: |
            curl -s https://codecov.io/bash | bash -s -- \
            -F "<< parameters.KARMA_TARGET >>" \
            -Z || echo 'Codecov upload failed'
      - store_test_results:
          path: test-results/junit

jobs:
  # node_modulesのインストール
  # package-lock.jsonが変わらない限りキャッシュを使う
  # キャッシュされたものは他のjobでも参照する
  install:
    executor: test-executor
    steps:
      - add_bb_user_key
      # コード最新化
      - checkout
      # キャッシュあれば使う
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package-lock.json" }}
      # 実行する処理
      - run: npm ci
      # node_modulesをキャッシュ
      - save_cache:
          paths:
            - node_modules
          key: v1-dependencies-{{ checksum "package-lock.json" }}
  # 1つめのテスト
  test0:
    executor: test-executor
    steps:
      - do_test:
          KARMA_TARGET: "no_components"
  # 2つめのテスト
  test1:
    executor: test-executor
    steps:
      - do_test:
          KARMA_TARGET: "components_admin"
  # 3つめのテスト
  test2:
    executor: test-executor
    steps:
      - do_test:
          KARMA_TARGET: "components_admin_preferences"
  # 4つめのテスト
  test3:
    executor: test-executor
    steps:
      - do_test:
          KARMA_TARGET: "components_common"
  # 5つめのテスト
  test4:
    executor: test-executor
    steps:
      - do_test:
          KARMA_TARGET: "components_other"
  # リリース用のビルド
  build:
    executor: build-executor
    steps:
      - add_bb_user_key
      - checkout
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package-lock.json" }}
      - run: bash build_circleci.sh
      # uploadのJOBに渡すためにpersistする
      - run: mkdir -p /tmp/workspace
      - run: cp -R artifact /tmp/workspace/
      - persist_to_workspace:
          root: /tmp/workspace
          paths:
            - artifact
  upload:
    executor: awscli-executor
    steps:
      - attach_workspace:
          at: /tmp/workspace
      - aws-s3/sync:
          from: /tmp/workspace/artifact
          to: 's3://mbwac-build-artifacts/mba-admin/'
  # tagされたビルド成果物をS3に保存する（mba-buildなどから参照するため）MBA−10223
  tagUpload:
    executor: awscli-executor
    steps:
      - attach_workspace:
          at: /tmp/workspace
      - run: mkdir -p /tmp/workspace/artifact/tags/$CIRCLE_TAG/$CIRCLE_PROJECT_REPONAME
      - run: cp -R /tmp/workspace/artifact/`echo ${CIRCLE_BRANCH} | sed "s/\//_/g"`/latest/* /tmp/workspace/artifact/tags/$CIRCLE_TAG/$CIRCLE_PROJECT_REPONAME/
      - aws-s3/sync:
          from: /tmp/workspace/artifact/tags/
          to: 's3://mbwac-build-artifacts/tags/'
  publish:
    # CDN用S3への公開
    executor: awscli-executor
    steps:
      - attach_workspace:
          at: /tmp/workspace
      # MBA-4068 assetsファイルは開発環境CDN用S3にコピーする
      - run: unzip /tmp/workspace/artifact/develop/latest/mba-admin.zip -d /tmp/workspace/artifact/develop/latest/
      - aws-s3/sync:
          from: /tmp/workspace/artifact/develop/latest/mba-admin/public/assets
          to: s3://${AWS_S3_ASSETS_BUCKET_NAME}/assets/
          arguments: |
            --cache-control "max-age=0,s-maxage=86400"
      # cfのinvalidationはとりあえずJSのみ
      - run: aws cloudfront create-invalidation --distribution-id "${AWS_CF_DISTRIBUTION_ID}" --path  "/assets/script/admin/*"
  sonarqube:
    executor: sonarqube-executor
    steps:
      - checkout
      - restore_cache:
          keys:
            - v1-dependencies-{{ checksum "package-lock.json" }}
      - restore_cache:
          keys:
            - sonarqube-${SONAR_VERSION}
      - run:
          name: Install Sonarqube scanner
          command: |
            SONAR_ZIP_FILE="sonar-scanner-cli-${SONAR_VERSION}-${SONAR_TARGET_OS}.zip"
            SCANNER_ZIP_DIR="sonar-scanner-${SONAR_VERSION}-${SONAR_TARGET_OS}"
            SCANNER_DIR="/tmp/sonar-scanner"

            if [ ! -d "$SCANNER_DIR" ]; then
              curl -kL "https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/${SONAR_ZIP_FILE}" -o /tmp/${SONAR_ZIP_FILE}
              unzip /tmp/$SONAR_ZIP_FILE -d /tmp
              mv /tmp/$SCANNER_ZIP_DIR $SCANNER_DIR
              rm -f /tmp/$SONAR_ZIP_FILE
            fi
      - save_cache:
          paths:
            - "/tmp/sonar-scanner"
          key: sonarqube-${SONAR_VERSION}
      - run:
          name: Run Sonarqube scanner
          command: |
            if [ "$CIRCLE_BRANCH" = "develop" ] || [[ "$CIRCLE_BRANCH" =~ ^feature ]]; then
              SCANNER_DIR="/tmp/sonar-scanner"
              DEFAULT_SONAR_PARAMS="-Dsonar.host.url=$SONAR_HOST_URL \
              -Dsonar.token=$SONAR_TOKEN \
              -Dsonar.projectName=$CIRCLE_PROJECT_REPONAME"
              SONAR_SCANNER_OPTS="-Xmx2048m"
              
              SONAR_PROJECT_KEY=$CIRCLE_PROJECT_REPONAME
              echo "Analyzing ${CIRCLE_BRANCH} branch to push issues to SonarQube server"
            
              ${SCANNER_DIR}/bin/sonar-scanner $DEFAULT_SONAR_PARAMS \
              -Dproject.settings=sonar-project.properties \
              -Dsonar.projectVersion=$CIRCLE_BUILD_NUM \
              -Dsonar.projectKey=$SONAR_PROJECT_KEY;
            fi
# コミットが発生すると以下のworkflowsが実行される
workflows:
  version: 2
  all_in_one:
    jobs:
      - install:
          context:
            - docker-hub-creds
          filters:
            tags:
              only: /.*/
      - test0:
          context:
            - docker-hub-creds
          requires:
            - install
      - test1:
          context:
            - docker-hub-creds
          requires:
            - install
      # MBA-16337 test2とtest3を一時的に無効化
      # - test2:
      #     context:
      #       - docker-hub-creds
      #     requires:
      #       - install
      # - test3:
      #     context:
      #       - docker-hub-creds
      #     requires:
      #       - install
      - test4:
          context:
            - docker-hub-creds
          requires:
            - install
      - sonarqube:
          context:
            - docker-hub-creds
            - mba-sonarqube
          requires:
            - install
      - build:
          context:
            - docker-hub-creds
          requires:
            - install
          filters:
            # featureブランチなどはビルド不要
            branches:
              only:
                - master
                - stage
                - develop
                - develop2
                - /patch.*/
                - refactor/MBA-11614
                - /publish-dev.?/.*/
            tags:
              only: /.*/
      - upload:
          context:
            - docker-hub-creds
          requires:
            - build
            - test0
            - test1
            # MBA-16337 test2とtest3を一時的に無効化
            # - test2
            # - test3
            - test4
          filters:
            branches:
              only:
                - master
                - stage
                - develop
                - develop2
                - /patch.*/
                - /publish-dev.?/.*/
      - tagUpload:
          context:
            - docker-hub-creds
          requires:
            - build
          filters:
            tags:
              # なんらかのタグという意味で/.+/指定だとだめだったので、MobiAgent-XXXとかMBA-XXXXとかMはじまりのタグのみ
              # https://circleci.com/docs/2.0/workflows/#executing-workflows-for-a-git-tag
              only: /^M.*/
            branches:
              ignore: /.*/
      - publish:
          context:
            - docker-hub-creds
          requires:
            - build
            - test0
            - test1
            # MBA-16337 test2とtest3を一時的に無効化
            # - test2
            # - test3
            - test4
          filters:
            branches:
              only:
                - develop
                - /publish-dev.?/.*/
